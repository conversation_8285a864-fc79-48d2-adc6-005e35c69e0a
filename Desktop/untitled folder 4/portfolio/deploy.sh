#!/bin/bash

# 🚀 Portfolio Deployment Script for Netlify
# This script helps you deploy your portfolio to Netlify

echo "🎯 Portfolio Deployment Script"
echo "=============================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Build the project
echo "🔨 Building the project..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi

# Check if Netlify CLI is available
if command -v netlify &> /dev/null; then
    echo "🌐 Netlify CLI found. You can now deploy!"
    echo ""
    echo "Choose your deployment option:"
    echo "1. Preview deployment (test before going live)"
    echo "2. Production deployment (live site)"
    echo "3. Skip deployment (just build)"
    echo ""
    read -p "Enter your choice (1-3): " choice
    
    case $choice in
        1)
            echo "🔍 Creating preview deployment..."
            netlify deploy --dir=build
            ;;
        2)
            echo "🚀 Creating production deployment..."
            netlify deploy --prod --dir=build
            ;;
        3)
            echo "⏭️  Skipping deployment. Your build is ready in the 'build' folder."
            ;;
        *)
            echo "❌ Invalid choice. Skipping deployment."
            ;;
    esac
else
    echo "⚠️  Netlify CLI not found globally."
    echo "You can:"
    echo "1. Install globally: npm install -g netlify-cli"
    echo "2. Use local version: npx netlify deploy --dir=build"
    echo "3. Drag and drop the 'build' folder to netlify.com"
fi

echo ""
echo "🎉 Deployment process complete!"
echo "📁 Your built files are in the 'build' folder"
echo "📖 Check DEPLOYMENT.md for detailed instructions"
