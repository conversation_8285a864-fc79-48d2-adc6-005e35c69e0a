.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom scrollbar for modal */
.modal-scrollable::-webkit-scrollbar {
  width: 8px;
}

.modal-scrollable::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

.modal-scrollable::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #06b6d4, #3b82f6);
  border-radius: 4px;
}

.modal-scrollable::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0891b2, #2563eb);
}

/* Smooth scrolling */
.modal-scrollable {
  scroll-behavior: smooth;
}
