import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import BarcelonaTheme from './components/BarcelonaTheme';
import Home from './pages/Home';
import About from './pages/About';
import Projects from './pages/Projects';
import Contact from './pages/Contact';
import './App.css';

function App() {
  const [isBarcelonaTheme, setIsBarcelonaTheme] = useState(false);

  const toggleBarcelonaTheme = () => {
    setIsBarcelonaTheme(!isBarcelonaTheme);
  };

  return (
    <Router>
      <div style={{
        minHeight: '100vh',
        background: isBarcelonaTheme
          ? 'linear-gradient(135deg, #004D98 0%, #A50044 100%)'
          : 'linear-gradient(135deg, #1e293b 0%, #7c3aed 100%)',
        color: 'white',
        fontFamily: 'system-ui, -apple-system, sans-serif',
        transition: 'background 0.5s ease'
      }}>
        <Navigation />
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/projects" element={<Projects />} />
          <Route path="/contact" element={<Contact />} />
        </Routes>
        <BarcelonaTheme
          isActive={isBarcelonaTheme}
          onToggle={toggleBarcelonaTheme}
        />
      </div>
    </Router>
  );
}

export default App;
