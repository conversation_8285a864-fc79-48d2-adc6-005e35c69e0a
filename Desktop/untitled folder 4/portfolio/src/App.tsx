import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Mail,
  MapPin,
  Github,
  Zap,
  Rocket,
  Music,
  Coffee,
  Code,
  Database,
  Smartphone,
  Cloud
} from 'lucide-react';
import './App.css';

function App() {
  const [currentRole, setCurrentRole] = useState(0);

  const roles = [
    "🎓 Senior CS Student",
    "💻 Full-Stack Developer",
    "📊 Data Analyst",
    "🚀 Digital Innovator"
  ];

  const skills = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Healthcare Tech",
      stat: "35% improvement",
      description: "Optimizing healthcare workflows"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Web Applications",
      stat: "4+ Projects",
      description: "Full-stack web solutions"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile Apps",
      stat: "Cross-Platform",
      description: "React Native & Flutter"
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "Cloud Solutions",
      stat: "Firebase & AWS",
      description: "Scalable cloud architecture"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentRole((prev) => (prev + 1) % roles.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e293b 0%, #7c3aed 100%)',
      color: 'white',
      padding: '2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '4rem',
          alignItems: 'center',
          width: '100%'
        }}>
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}
          >
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              lineHeight: '1.1',
              margin: 0
            }}>
              Hi, I'm{" "}
              <span style={{
                background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                Darshan Adhikari
              </span>
            </h1>

            {/* Animated Role */}
            <div style={{ height: '4rem', display: 'flex', alignItems: 'center' }}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentRole}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  style={{
                    fontSize: '1.5rem',
                    fontWeight: '600',
                    color: '#06b6d4',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem'
                  }}
                >
                  <Zap size={24} color="#fbbf24" />
                  {roles[currentRole]}
                  <Rocket size={24} color="#3b82f6" />
                </motion.div>
              </AnimatePresence>
            </div>

            <p style={{
              fontSize: '1.25rem',
              color: '#bfdbfe',
              margin: 0
            }}>
              Transforming Ideas into Digital Reality 🚀
            </p>

            <p style={{
              color: '#94a3b8',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              margin: 0
            }}>
              <Music size={20} />
              Building real solutions with a sci-fi playlist in the background
              <Coffee size={20} />
            </p>

            {/* CTA Buttons */}
            <div style={{ display: 'flex', gap: '1rem' }}>
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                style={{
                  background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  border: 'none',
                  borderRadius: '0.75rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '1rem'
                }}
              >
                <Rocket size={20} />
                View My Work
                <ArrowRight size={20} />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                style={{
                  border: '2px solid #06b6d4',
                  color: '#06b6d4',
                  background: 'rgba(6, 182, 212, 0.1)',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.75rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '1rem'
                }}
              >
                <Mail size={20} />
                Get in Touch
              </motion.button>
            </div>

            {/* Contact Info */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', color: '#94a3b8' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Mail size={16} />
                <span style={{ fontSize: '0.9rem' }}><EMAIL></span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <MapPin size={16} />
                <span style={{ fontSize: '0.9rem' }}>Cape Girardeau, MO</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Github size={16} />
                <span style={{ fontSize: '0.9rem' }}>github.com/DarshanAdh</span>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '2rem' }}
          >
            {/* Profile Photo Placeholder */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              style={{
                width: '300px',
                height: '300px',
                borderRadius: '1.5rem',
                background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                padding: '2px',
                position: 'relative'
              }}
            >
              <div style={{
                width: '100%',
                height: '100%',
                borderRadius: '1.5rem',
                background: 'rgba(15, 23, 42, 0.95)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center'
              }}>
                <div style={{
                  width: '120px',
                  height: '120px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem'
                }}>
                  DA
                </div>
                <p style={{ color: '#06b6d4', fontSize: '1.1rem', fontWeight: '600', margin: '0 0 0.5rem 0' }}>
                  Professional Photo
                </p>
                <p style={{ color: '#94a3b8', fontSize: '0.9rem', margin: 0 }}>
                  Coming Soon
                </p>
              </div>
            </motion.div>

            {/* Quick Stats Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1rem',
              width: '100%'
            }}>
              {skills.map((skill, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  style={{
                    background: 'rgba(15, 23, 42, 0.8)',
                    border: '1px solid rgba(6, 182, 212, 0.3)',
                    borderRadius: '0.75rem',
                    padding: '1rem',
                    textAlign: 'center',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{ color: '#06b6d4', marginBottom: '0.5rem' }}>
                    {skill.icon}
                  </div>
                  <div style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#06b6d4', marginBottom: '0.25rem' }}>
                    {skill.stat}
                  </div>
                  <div style={{ fontSize: '0.75rem', color: '#94a3b8' }}>
                    {skill.title}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export default App;
