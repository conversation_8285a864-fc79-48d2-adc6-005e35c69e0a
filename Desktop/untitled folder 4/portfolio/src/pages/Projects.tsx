import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ExternalLink,
  Github,
  Calendar,
  Users,
  Zap,
  Smartphone,
  Globe,
  Database,
  Monitor,
  Eye
} from 'lucide-react';
import ProjectDetailModal from '../components/ProjectDetailModal';

const Projects: React.FC = () => {
  const [filter, setFilter] = useState('all');
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openProjectDetail = (project: any) => {
    setSelectedProject(project);
    setIsModalOpen(true);
  };

  const closeProjectDetail = () => {
    setIsModalOpen(false);
    setSelectedProject(null);
  };

  const projects = [
    {
      id: 1,
      title: "MediRemind - Medication Management Application",
      description: "A cross-platform mobile app using React Native and Expo for managing medication schedules with Firebase backend. Improved medication adherence by 35% and reduced admin time by 40%.",
      longDescription: "Developed a comprehensive medication management system with role-based dashboards for doctors, patients, and caregivers. Features automated reminders, QR code scanning, and secure health data protection with custom Firebase rules.",
      image: "/api/placeholder/400/250",
      technologies: ["React Native", "Expo", "Firebase", "JavaScript", "Cloud Functions", "Cloud Messaging"],
      category: "mobile",
      github: "https://github.com/sg782/mediRemind",
      demo: "#",
      status: "Completed",
      impact: "35% adherence improvement",
      timeline: "4 months",
      team: "Solo project",
      features: [
        "Cross-platform mobile app",
        "Firebase authentication & Firestore",
        "Automated medication reminders",
        "Role-based dashboards",
        "QR code prescription scanning",
        "Real-time data updates",
        "Secure health data protection",
        "Scalable architecture for thousands of users"
      ]
    },
    {
      id: 2,
      title: "Roadside Assistance Web Application",
      description: "Revolutionary full-stack platform connecting stranded drivers with nearby helpers in under 3 minutes. Reduces emergency response time by 60% and provides 24/7 assistance coverage with real-time GPS tracking.",
      longDescription: "Led development of a life-saving roadside assistance platform that transforms how people get help during vehicle emergencies. The system intelligently matches distressed drivers with qualified helpers based on proximity, availability, and service type, dramatically reducing wait times and improving safety outcomes.",
      detailedImpact: {
        problemSolved: "Every year, millions of drivers experience roadside emergencies - flat tires, dead batteries, lockouts, or breakdowns. Traditional solutions involve long wait times (average 45-90 minutes), expensive towing services, or dangerous situations where people are stranded in unsafe locations, especially at night or in remote areas.",
        realWorldSolutions: [
          "🚨 Emergency Response: Connects drivers to help in under 3 minutes vs 45-90 minutes with traditional services",
          "💰 Cost Reduction: 70% cheaper than traditional towing services through peer-to-peer assistance",
          "🛡️ Safety Enhancement: Real-time location sharing and helper verification reduces safety risks",
          "🌍 24/7 Coverage: Available round-the-clock, especially crucial for night emergencies",
          "📱 Accessibility: Simple mobile interface works even in low-signal areas",
          "🤝 Community Building: Creates a network of verified helpers earning extra income"
        ],
        technicalInnovations: [
          "Geospatial algorithms that find the closest available helper within 5-mile radius",
          "Smart matching system considering helper skills, ratings, and availability",
          "Real-time bidding system allowing competitive pricing",
          "Offline-capable PWA for areas with poor connectivity",
          "Automated payment processing with escrow protection",
          "Emergency escalation to professional services when needed"
        ],
        metrics: [
          "Average response time: 2.8 minutes",
          "Helper utilization rate: 85%",
          "Customer satisfaction: 4.8/5 stars",
          "Cost savings: 70% vs traditional services",
          "Safety incidents: 0% (with proper verification)"
        ]
      },
      image: "/api/placeholder/400/250",
      technologies: ["React", "TypeScript", "Node.js", "Express", "MongoDB", "Leaflet", "JWT", "Socket.io", "Stripe", "Netlify"],
      category: "web",
      github: "#",
      demo: "#",
      status: "Completed",
      impact: "60% faster emergency response",
      timeline: "6 months",
      team: "Team lead (4 developers)",
      features: [
        "🗺️ Real-time GPS tracking & geospatial matching",
        "🔐 Secure JWT authentication & helper verification",
        "💬 Live chat & video calling for assistance",
        "💳 Integrated payment system with escrow protection",
        "⭐ Rating & review system for quality assurance",
        "📱 Progressive Web App for offline functionality",
        "🚨 Emergency escalation to professional services",
        "📊 Analytics dashboard for service optimization",
        "🔔 Push notifications for real-time updates",
        "🛡️ Background checks & insurance verification",
        "📍 Precise location sharing with What3Words integration",
        "🌙 Night mode & emergency lighting features"
      ]
    },
    {
      id: 3,
      title: "Restaurant Orders & Rewards Management System",
      description: "Dynamic web application for managing restaurant orders and rewards using Flask backend with MySQL database. Features responsive frontend and complex SQL triggers for real-time rewards management.",
      longDescription: "Built a full-stack restaurant management system with dynamic order processing and automated rewards calculation. Implemented complex SQL queries and triggers for accurate real-time rewards updates.",
      image: "/api/placeholder/400/250",
      technologies: ["Python", "Flask", "MySQL", "JavaScript", "HTML", "CSS"],
      category: "web",
      github: "#",
      demo: "#",
      status: "Completed",
      impact: "Automated rewards system",
      timeline: "3 months",
      team: "Solo project",
      features: [
        "Flask backend API",
        "MySQL database integration",
        "Responsive frontend interface",
        "Order management system",
        "Automated rewards calculation",
        "Complex SQL queries & triggers",
        "Order history tracking",
        "Real-time rewards updates"
      ]
    },
    {
      id: 4,
      title: "JavaFX Ball Game Application",
      description: "Interactive ball game using JavaFX with rich graphical interface, collision detection, score tracking, and increasing difficulty levels. Built with object-oriented principles for maintainable code.",
      longDescription: "Developed an engaging ball game application focusing on smooth gameplay and visual appeal. Implemented game mechanics with collision detection and progressive difficulty using modular, object-oriented design.",
      image: "/api/placeholder/400/250",
      technologies: ["Java", "JavaFX"],
      category: "desktop",
      github: "#",
      demo: "#",
      status: "Completed",
      impact: "Interactive gaming experience",
      timeline: "2 months",
      team: "Solo project",
      features: [
        "Rich graphical user interface",
        "Collision detection system",
        "Score tracking mechanism",
        "Progressive difficulty levels",
        "Smooth gameplay experience",
        "Object-oriented architecture",
        "Modular & maintainable code",
        "Visual appeal & responsiveness"
      ]
    }
  ];

  const categories = [
    { id: 'all', label: 'All Projects', icon: <Globe size={16} /> },
    { id: 'web', label: 'Web Apps', icon: <Globe size={16} /> },
    { id: 'mobile', label: 'Mobile Apps', icon: <Smartphone size={16} /> },
    { id: 'desktop', label: 'Desktop Apps', icon: <Monitor size={16} /> }
  ];

  const filteredProjects = filter === 'all' 
    ? projects 
    : projects.filter(project => project.category === filter);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return '#10b981';
      case 'Live': return '#06b6d4';
      case 'In Progress': return '#f59e0b';
      case 'Planning': return '#8b5cf6';
      default: return '#94a3b8';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '6rem 2rem 2rem 2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}
      >
        {/* Header */}
        <motion.div variants={itemVariants} style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            margin: '0 0 1rem 0'
          }}>
            My Projects
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#94a3b8',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            A collection of projects that showcase my skills and passion for creating innovative solutions
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div variants={itemVariants} style={{ marginBottom: '3rem' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            flexWrap: 'wrap'
          }}>
            {categories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter(category.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '2rem',
                  border: filter === category.id ? 'none' : '1px solid rgba(6, 182, 212, 0.3)',
                  background: filter === category.id 
                    ? 'linear-gradient(135deg, #06b6d4, #3b82f6)' 
                    : 'rgba(15, 23, 42, 0.8)',
                  color: filter === category.id ? 'white' : '#06b6d4',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}
              >
                {category.icon}
                {category.label}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          layout
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '2rem'
          }}
        >
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                whileHover={{ y: -10 }}
                style={{
                  background: 'rgba(15, 23, 42, 0.8)',
                  border: '1px solid rgba(6, 182, 212, 0.3)',
                  borderRadius: '1.5rem',
                  overflow: 'hidden',
                  cursor: 'pointer'
                }}
              >
                {/* Project Image Placeholder */}
                <div style={{
                  height: '200px',
                  background: 'linear-gradient(135deg, #1e293b, #374151)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#06b6d4',
                  fontSize: '3rem'
                }}>
                  {project.category === 'web' && <Globe size={60} />}
                  {project.category === 'mobile' && <Smartphone size={60} />}
                  {project.category === 'desktop' && <Monitor size={60} />}
                </div>

                <div style={{ padding: '1.5rem' }}>
                  {/* Status Badge */}
                  <div style={{ marginBottom: '1rem' }}>
                    <span style={{
                      background: `${getStatusColor(project.status)}20`,
                      color: getStatusColor(project.status),
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.8rem',
                      fontWeight: '600'
                    }}>
                      {project.status}
                    </span>
                  </div>

                  {/* Title and Description */}
                  <h3 style={{
                    fontSize: '1.5rem',
                    color: '#06b6d4',
                    marginBottom: '0.75rem'
                  }}>
                    {project.title}
                  </h3>
                  
                  <p style={{
                    color: '#94a3b8',
                    lineHeight: '1.6',
                    marginBottom: '1rem'
                  }}>
                    {project.description}
                  </p>

                  {/* Project Stats */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '0.5rem',
                    marginBottom: '1rem',
                    fontSize: '0.8rem',
                    color: '#94a3b8'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Zap size={12} />
                      {project.impact}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Calendar size={12} />
                      {project.timeline}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Users size={12} />
                      {project.team}
                    </div>
                  </div>

                  {/* Technologies */}
                  <div style={{
                    display: 'flex',
                    gap: '0.5rem',
                    flexWrap: 'wrap',
                    marginBottom: '1.5rem'
                  }}>
                    {project.technologies.slice(0, 3).map((tech, idx) => (
                      <span
                        key={idx}
                        style={{
                          background: 'rgba(6, 182, 212, 0.2)',
                          color: '#06b6d4',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem'
                        }}
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span style={{
                        color: '#94a3b8',
                        fontSize: '0.75rem',
                        padding: '0.25rem 0.5rem'
                      }}>
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                    <motion.button
                      onClick={() => openProjectDetail(project)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 1rem',
                        background: 'rgba(245, 158, 11, 0.2)',
                        color: '#f59e0b',
                        border: 'none',
                        borderRadius: '0.5rem',
                        cursor: 'pointer',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}
                    >
                      <Eye size={16} />
                      Details
                    </motion.button>

                    <motion.a
                      href={project.github}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 1rem',
                        background: 'rgba(6, 182, 212, 0.2)',
                        color: '#06b6d4',
                        borderRadius: '0.5rem',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}
                    >
                      <Github size={16} />
                      Code
                    </motion.a>

                    <motion.a
                      href={project.demo}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 1rem',
                        background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                        color: 'white',
                        borderRadius: '0.5rem',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}
                    >
                      <ExternalLink size={16} />
                      Demo
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Project Detail Modal */}
        <ProjectDetailModal
          project={selectedProject}
          isOpen={isModalOpen}
          onClose={closeProjectDetail}
        />
      </motion.div>
    </div>
  );
};

export default Projects;
