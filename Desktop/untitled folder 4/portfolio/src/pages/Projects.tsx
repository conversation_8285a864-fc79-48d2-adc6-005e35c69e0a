import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ExternalLink,
  Github,
  Calendar,
  Users,
  Zap,
  Smartphone,
  Globe,
  Database
} from 'lucide-react';

const Projects: React.FC = () => {
  const [filter, setFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: "MediRemind",
      description: "A comprehensive healthcare management system that improved workflow efficiency by 35%. Features include patient scheduling, medication reminders, and real-time health monitoring.",
      longDescription: "MediRemind addresses the critical need for efficient healthcare management by providing a unified platform for patients and healthcare providers. The system includes automated appointment scheduling, medication tracking with smart reminders, and a dashboard for monitoring patient health metrics in real-time.",
      image: "/api/placeholder/400/250",
      technologies: ["React", "TypeScript", "Firebase", "Node.js", "Material-UI"],
      category: "web",
      github: "https://github.com/sg782/mediRemind",
      demo: "#",
      status: "Completed",
      impact: "35% efficiency improvement",
      timeline: "3 months",
      team: "Solo project",
      features: [
        "Patient appointment scheduling",
        "Medication reminder system",
        "Real-time health monitoring",
        "Provider dashboard",
        "Secure data encryption"
      ]
    },
    {
      id: 2,
      title: "Portfolio Website",
      description: "A modern, responsive portfolio website built with React and TypeScript, featuring smooth animations and a sci-fi inspired design.",
      longDescription: "This portfolio showcases my work and skills through an interactive, animated interface. Built with modern web technologies and optimized for performance across all devices.",
      image: "/api/placeholder/400/250",
      technologies: ["React", "TypeScript", "Framer Motion", "CSS3"],
      category: "web",
      github: "#",
      demo: "#",
      status: "Live",
      impact: "Professional presence",
      timeline: "2 weeks",
      team: "Solo project",
      features: [
        "Responsive design",
        "Smooth animations",
        "Dark theme",
        "Interactive elements",
        "SEO optimized"
      ]
    },
    {
      id: 3,
      title: "Data Analytics Dashboard",
      description: "Interactive business intelligence dashboard for visualizing complex datasets with real-time updates and customizable charts.",
      longDescription: "A comprehensive analytics platform that transforms raw business data into actionable insights through interactive visualizations and real-time monitoring capabilities.",
      image: "/api/placeholder/400/250",
      technologies: ["Python", "Pandas", "Plotly", "Dash", "PostgreSQL"],
      category: "data",
      github: "#",
      demo: "#",
      status: "In Progress",
      impact: "Data-driven decisions",
      timeline: "4 months",
      team: "2 developers",
      features: [
        "Real-time data processing",
        "Interactive charts",
        "Custom filters",
        "Export functionality",
        "Multi-user support"
      ]
    },
    {
      id: 4,
      title: "Mobile Task Manager",
      description: "Cross-platform mobile application for task management with offline sync, team collaboration, and productivity analytics.",
      longDescription: "A feature-rich task management app that helps teams stay organized and productive. Includes offline capabilities, real-time collaboration, and detailed productivity insights.",
      image: "/api/placeholder/400/250",
      technologies: ["React Native", "Firebase", "Redux", "AsyncStorage"],
      category: "mobile",
      github: "#",
      demo: "#",
      status: "Planning",
      impact: "Team productivity",
      timeline: "6 months",
      team: "3 developers",
      features: [
        "Offline synchronization",
        "Team collaboration",
        "Push notifications",
        "Analytics dashboard",
        "Cross-platform support"
      ]
    }
  ];

  const categories = [
    { id: 'all', label: 'All Projects', icon: <Globe size={16} /> },
    { id: 'web', label: 'Web Apps', icon: <Globe size={16} /> },
    { id: 'mobile', label: 'Mobile Apps', icon: <Smartphone size={16} /> },
    { id: 'data', label: 'Data Science', icon: <Database size={16} /> }
  ];

  const filteredProjects = filter === 'all' 
    ? projects 
    : projects.filter(project => project.category === filter);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return '#10b981';
      case 'Live': return '#06b6d4';
      case 'In Progress': return '#f59e0b';
      case 'Planning': return '#8b5cf6';
      default: return '#94a3b8';
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '6rem 2rem 2rem 2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}
      >
        {/* Header */}
        <motion.div variants={itemVariants} style={{ textAlign: 'center', marginBottom: '3rem' }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            margin: '0 0 1rem 0'
          }}>
            My Projects
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#94a3b8',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            A collection of projects that showcase my skills and passion for creating innovative solutions
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div variants={itemVariants} style={{ marginBottom: '3rem' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '1rem',
            flexWrap: 'wrap'
          }}>
            {categories.map((category) => (
              <motion.button
                key={category.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter(category.id)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '2rem',
                  border: filter === category.id ? 'none' : '1px solid rgba(6, 182, 212, 0.3)',
                  background: filter === category.id 
                    ? 'linear-gradient(135deg, #06b6d4, #3b82f6)' 
                    : 'rgba(15, 23, 42, 0.8)',
                  color: filter === category.id ? 'white' : '#06b6d4',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}
              >
                {category.icon}
                {category.label}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          layout
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
            gap: '2rem'
          }}
        >
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                whileHover={{ y: -10 }}
                style={{
                  background: 'rgba(15, 23, 42, 0.8)',
                  border: '1px solid rgba(6, 182, 212, 0.3)',
                  borderRadius: '1.5rem',
                  overflow: 'hidden',
                  cursor: 'pointer'
                }}
              >
                {/* Project Image Placeholder */}
                <div style={{
                  height: '200px',
                  background: 'linear-gradient(135deg, #1e293b, #374151)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#06b6d4',
                  fontSize: '3rem'
                }}>
                  {project.category === 'web' && <Globe size={60} />}
                  {project.category === 'mobile' && <Smartphone size={60} />}
                  {project.category === 'data' && <Database size={60} />}
                </div>

                <div style={{ padding: '1.5rem' }}>
                  {/* Status Badge */}
                  <div style={{ marginBottom: '1rem' }}>
                    <span style={{
                      background: `${getStatusColor(project.status)}20`,
                      color: getStatusColor(project.status),
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.8rem',
                      fontWeight: '600'
                    }}>
                      {project.status}
                    </span>
                  </div>

                  {/* Title and Description */}
                  <h3 style={{
                    fontSize: '1.5rem',
                    color: '#06b6d4',
                    marginBottom: '0.75rem'
                  }}>
                    {project.title}
                  </h3>
                  
                  <p style={{
                    color: '#94a3b8',
                    lineHeight: '1.6',
                    marginBottom: '1rem'
                  }}>
                    {project.description}
                  </p>

                  {/* Project Stats */}
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '0.5rem',
                    marginBottom: '1rem',
                    fontSize: '0.8rem',
                    color: '#94a3b8'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Zap size={12} />
                      {project.impact}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Calendar size={12} />
                      {project.timeline}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <Users size={12} />
                      {project.team}
                    </div>
                  </div>

                  {/* Technologies */}
                  <div style={{
                    display: 'flex',
                    gap: '0.5rem',
                    flexWrap: 'wrap',
                    marginBottom: '1.5rem'
                  }}>
                    {project.technologies.slice(0, 3).map((tech, idx) => (
                      <span
                        key={idx}
                        style={{
                          background: 'rgba(6, 182, 212, 0.2)',
                          color: '#06b6d4',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.5rem',
                          fontSize: '0.75rem'
                        }}
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span style={{
                        color: '#94a3b8',
                        fontSize: '0.75rem',
                        padding: '0.25rem 0.5rem'
                      }}>
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div style={{ display: 'flex', gap: '0.75rem' }}>
                    <motion.a
                      href={project.github}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 1rem',
                        background: 'rgba(6, 182, 212, 0.2)',
                        color: '#06b6d4',
                        borderRadius: '0.5rem',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}
                    >
                      <Github size={16} />
                      Code
                    </motion.a>
                    
                    <motion.a
                      href={project.demo}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        padding: '0.5rem 1rem',
                        background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                        color: 'white',
                        borderRadius: '0.5rem',
                        textDecoration: 'none',
                        fontSize: '0.9rem',
                        fontWeight: '600'
                      }}
                    >
                      <ExternalLink size={16} />
                      Demo
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Projects;
