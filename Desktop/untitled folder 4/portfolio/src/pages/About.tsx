import React from 'react';
import { motion } from 'framer-motion';
import { 
  GraduationCap, 
  Code, 
  Heart, 
  Music, 
  Film, 
  Trophy,
  BookOpen,
  Target,
  Zap
} from 'lucide-react';

const About: React.FC = () => {
  const education = [
    {
      degree: "Bachelor of Science in Computer Science",
      school: "Southeast Missouri State University",
      period: "2021 - 2025",
      gpa: "3.8/4.0",
      highlights: ["Dean's List", "CS Honor Society", "Advanced Algorithms"]
    }
  ];

  const skills = [
    {
      category: "Frontend",
      technologies: ["React", "TypeScript", "JavaScript", "HTML/CSS", "Tailwind CSS", "Framer Motion"],
      color: "#06b6d4"
    },
    {
      category: "Backend",
      technologies: ["Node.js", "Python", "Java", "Express.js", "REST APIs", "GraphQL"],
      color: "#10b981"
    },
    {
      category: "Database",
      technologies: ["Firebase", "MongoDB", "PostgreSQL", "MySQL", "Redis"],
      color: "#8b5cf6"
    },
    {
      category: "Cloud & Tools",
      technologies: ["AWS", "Docker", "Git", "GitHub Actions", "Vercel", "Netlify"],
      color: "#f59e0b"
    }
  ];

  const hobbies = [
    {
      icon: <Music size={24} />,
      title: "Music Production",
      description: "Creating beats and melodies in my spare time",
      color: "#06b6d4"
    },
    {
      icon: <Film size={24} />,
      title: "Movies & Series",
      description: "Sci-fi, thrillers, and tech documentaries",
      color: "#8b5cf6"
    },
    {
      icon: <Trophy size={24} />,
      title: "Soccer",
      description: "Barcelona fan ⚽ - Playing and watching",
      color: "#10b981"
    },
    {
      icon: <Code size={24} />,
      title: "Open Source",
      description: "Contributing to projects and learning new tech",
      color: "#f59e0b"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '6rem 2rem 2rem 2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}
      >
        {/* Header */}
        <motion.div variants={itemVariants} style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            margin: '0 0 1rem 0'
          }}>
            About Me
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#94a3b8',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Passionate about creating innovative solutions that make a real difference in people's lives
          </p>
        </motion.div>

        {/* Personal Story */}
        <motion.div variants={itemVariants} style={{ marginBottom: '4rem' }}>
          <div style={{
            background: 'rgba(15, 23, 42, 0.8)',
            border: '1px solid rgba(6, 182, 212, 0.3)',
            borderRadius: '1.5rem',
            padding: '2rem',
            textAlign: 'center'
          }}>
            <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '1.5rem' }}>
              <div style={{
                background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                borderRadius: '50%',
                padding: '1rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Zap size={32} color="white" />
              </div>
            </div>
            <h2 style={{
              fontSize: '2rem',
              color: '#06b6d4',
              marginBottom: '1rem'
            }}>
              My Journey
            </h2>
            <p style={{
              fontSize: '1.1rem',
              color: '#bfdbfe',
              lineHeight: '1.8',
              maxWidth: '800px',
              margin: '0 auto'
            }}>
              I'm a senior Computer Science student at Southeast Missouri State University with a passion for 
              building technology that solves real-world problems. My journey started with curiosity about 
              how things work and evolved into a love for creating digital solutions that improve people's lives.
              <br /><br />
              When I'm not coding, you'll find me producing music, watching sci-fi movies, or cheering for 
              Barcelona. I believe the best solutions come from understanding both the technical and human 
              sides of problems.
            </p>
          </div>
        </motion.div>

        {/* Education */}
        <motion.div variants={itemVariants} style={{ marginBottom: '4rem' }}>
          <h2 style={{
            fontSize: '2.5rem',
            color: '#06b6d4',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            Education
          </h2>
          {education.map((edu, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02, y: -5 }}
              style={{
                background: 'rgba(15, 23, 42, 0.8)',
                border: '1px solid rgba(6, 182, 212, 0.3)',
                borderRadius: '1.5rem',
                padding: '2rem',
                marginBottom: '1rem'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
                <div style={{
                  background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                  borderRadius: '50%',
                  padding: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <GraduationCap size={24} color="white" />
                </div>
                <div>
                  <h3 style={{ fontSize: '1.5rem', color: '#06b6d4', margin: 0 }}>
                    {edu.degree}
                  </h3>
                  <p style={{ color: '#94a3b8', margin: '0.25rem 0' }}>
                    {edu.school} • {edu.period}
                  </p>
                  <p style={{ color: '#bfdbfe', margin: 0, fontWeight: '600' }}>
                    GPA: {edu.gpa}
                  </p>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                {edu.highlights.map((highlight, idx) => (
                  <span
                    key={idx}
                    style={{
                      background: 'rgba(6, 182, 212, 0.2)',
                      color: '#06b6d4',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '1rem',
                      fontSize: '0.9rem'
                    }}
                  >
                    {highlight}
                  </span>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Skills */}
        <motion.div variants={itemVariants} style={{ marginBottom: '4rem' }}>
          <h2 style={{
            fontSize: '2.5rem',
            color: '#06b6d4',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            Technical Skills
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '1.5rem'
          }}>
            {skills.map((skillGroup, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                style={{
                  background: 'rgba(15, 23, 42, 0.8)',
                  border: `1px solid ${skillGroup.color}40`,
                  borderRadius: '1.5rem',
                  padding: '1.5rem'
                }}
              >
                <h3 style={{
                  fontSize: '1.25rem',
                  color: skillGroup.color,
                  marginBottom: '1rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <Target size={20} />
                  {skillGroup.category}
                </h3>
                <div style={{ display: 'flex', gap: '0.5rem', flexWrap: 'wrap' }}>
                  {skillGroup.technologies.map((tech, idx) => (
                    <span
                      key={idx}
                      style={{
                        background: `${skillGroup.color}20`,
                        color: skillGroup.color,
                        padding: '0.25rem 0.75rem',
                        borderRadius: '1rem',
                        fontSize: '0.9rem',
                        border: `1px solid ${skillGroup.color}40`
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Hobbies */}
        <motion.div variants={itemVariants}>
          <h2 style={{
            fontSize: '2.5rem',
            color: '#06b6d4',
            marginBottom: '2rem',
            textAlign: 'center'
          }}>
            When I'm Not Coding
          </h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1.5rem'
          }}>
            {hobbies.map((hobby, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                style={{
                  background: 'rgba(15, 23, 42, 0.8)',
                  border: `1px solid ${hobby.color}40`,
                  borderRadius: '1.5rem',
                  padding: '1.5rem',
                  textAlign: 'center'
                }}
              >
                <div style={{
                  background: `${hobby.color}20`,
                  borderRadius: '50%',
                  width: '60px',
                  height: '60px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 1rem auto',
                  color: hobby.color
                }}>
                  {hobby.icon}
                </div>
                <h3 style={{
                  fontSize: '1.25rem',
                  color: hobby.color,
                  marginBottom: '0.5rem'
                }}>
                  {hobby.title}
                </h3>
                <p style={{
                  color: '#94a3b8',
                  fontSize: '0.9rem',
                  margin: 0
                }}>
                  {hobby.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default About;
