import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  ArrowRight, 
  Mail, 
  MapPin, 
  Github, 
  Zap,
  Rocket,
  Music,
  Coffee,
  Code,
  Database,
  Smartphone,
  Cloud
} from 'lucide-react';

const Home: React.FC = () => {
  const [currentRole, setCurrentRole] = useState(0);
  
  const roles = [
    "🎓 Senior CS Student",
    "💻 Full-Stack Developer", 
    "📊 Data Analyst",
    "🚀 Digital Innovator"
  ];

  const skills = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Programming Languages",
      stat: "8+ Languages",
      description: "Python, Java, JavaScript, TypeScript, C, C#"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Full-Stack Development",
      stat: "Modern Frameworks",
      description: "React, Node.js, Flask, Express, Firebase"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Security & Testing",
      stat: "Enterprise-Ready",
      description: "JWT, Role-Based Access, White Box Testing"
    },
    {
      icon: <Cloud className="w-8 h-8" />,
      title: "Data & Collaboration",
      stat: "Agile Workflow",
      description: "NumPy, Pandas, Git, Pair Programming"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentRole((prev) => (prev + 1) % roles.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [roles.length]);

  return (
    <div style={{
      minHeight: '100vh',
      padding: '6rem 2rem 2rem 2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        minHeight: 'calc(100vh - 8rem)',
        display: 'flex',
        alignItems: 'center'
      }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '4rem',
          alignItems: 'center',
          width: '100%'
        }}>
          {/* Left Column - Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}
          >
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              lineHeight: '1.1',
              margin: 0
            }}>
              Hi, I'm{" "}
              <span style={{
                background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                Darshan Adhikari
              </span>
            </h1>
            
            {/* Animated Role */}
            <div style={{ height: '4rem', display: 'flex', alignItems: 'center' }}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentRole}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  style={{
                    fontSize: '1.5rem',
                    fontWeight: '600',
                    color: '#06b6d4',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem'
                  }}
                >
                  <Zap size={24} color="#fbbf24" />
                  {roles[currentRole]}
                  <Rocket size={24} color="#3b82f6" />
                </motion.div>
              </AnimatePresence>
            </div>
            
            <p style={{
              fontSize: '1.25rem',
              color: '#bfdbfe',
              margin: 0
            }}>
              Transforming Ideas into Digital Reality 🚀
            </p>
            
            <p style={{
              color: '#94a3b8',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              margin: 0
            }}>
              <Music size={20} />
              Building real solutions with epic playlists and way too much coffee ☕
              <Coffee size={20} />
            </p>

            {/* Technical Skills Highlight */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              style={{
                background: 'linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1))',
                border: '1px solid rgba(6, 182, 212, 0.2)',
                borderRadius: '1rem',
                padding: '1rem',
                marginTop: '1rem'
              }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                marginBottom: '0.5rem'
              }}>
                <Zap size={16} color="#06b6d4" />
                <span style={{
                  color: '#06b6d4',
                  fontWeight: '600',
                  fontSize: '0.9rem'
                }}>
                  Technical Arsenal
                </span>
              </div>
              <div style={{
                display: 'flex',
                gap: '0.5rem',
                flexWrap: 'wrap'
              }}>
                {['Python', 'Java', 'TypeScript', 'React', 'Node.js', 'Firebase', 'JWT', 'MongoDB'].map((tech, idx) => (
                  <span
                    key={idx}
                    style={{
                      background: 'rgba(6, 182, 212, 0.2)',
                      color: '#06b6d4',
                      padding: '0.25rem 0.5rem',
                      borderRadius: '0.5rem',
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}
                  >
                    {tech}
                  </span>
                ))}
                <span style={{
                  color: '#94a3b8',
                  fontSize: '0.75rem',
                  padding: '0.25rem 0.5rem',
                  fontStyle: 'italic'
                }}>
                  +20 more technologies
                </span>
              </div>
            </motion.div>

            {/* CTA Buttons */}
            <div style={{ display: 'flex', gap: '1rem' }}>
              <Link to="/projects" style={{ textDecoration: 'none' }}>
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                    color: 'white',
                    padding: '0.75rem 1.5rem',
                    border: 'none',
                    borderRadius: '0.75rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    fontSize: '1rem'
                  }}
                >
                  <Rocket size={20} />
                  View My Work
                  <ArrowRight size={20} />
                </motion.button>
              </Link>
              
              <Link to="/contact" style={{ textDecoration: 'none' }}>
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    border: '2px solid #06b6d4',
                    color: '#06b6d4',
                    background: 'rgba(6, 182, 212, 0.1)',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.75rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    fontSize: '1rem'
                  }}
                >
                  <Mail size={20} />
                  Get in Touch
                </motion.button>
              </Link>
            </div>

            {/* Contact Info */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', color: '#94a3b8' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Mail size={16} />
                <span style={{ fontSize: '0.9rem' }}><EMAIL></span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <MapPin size={16} />
                <span style={{ fontSize: '0.9rem' }}>Cape Girardeau, MO</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <Github size={16} />
                <span style={{ fontSize: '0.9rem' }}>github.com/DarshanAdh</span>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '2rem' }}
          >
            {/* Professional Photo */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              style={{
                width: '300px',
                height: '300px',
                borderRadius: '1.5rem',
                background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                padding: '3px',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              <img
                src="/darshan-photo.jpg"
                alt="Darshan Adhikari"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '1.5rem',
                  display: 'block'
                }}
                onError={(e) => {
                  // Fallback if image doesn't load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'flex';
                }}
              />
              {/* Fallback placeholder */}
              <div style={{
                width: '100%',
                height: '100%',
                borderRadius: '1.5rem',
                background: 'rgba(15, 23, 42, 0.95)',
                display: 'none',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                position: 'absolute',
                top: 0,
                left: 0
              }}>
                <div style={{
                  width: '120px',
                  height: '120px',
                  borderRadius: '50%',
                  background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '2.5rem',
                  fontWeight: 'bold',
                  marginBottom: '1rem'
                }}>
                  DA
                </div>
                <p style={{ color: '#06b6d4', fontSize: '1.1rem', fontWeight: '600', margin: '0 0 0.5rem 0' }}>
                  Professional Photo
                </p>
                <p style={{ color: '#94a3b8', fontSize: '0.9rem', margin: 0 }}>
                  Loading...
                </p>
              </div>
            </motion.div>

            {/* Quick Stats Grid */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '1rem',
              width: '100%'
            }}>
              {skills.map((skill, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  style={{
                    background: 'rgba(15, 23, 42, 0.8)',
                    border: '1px solid rgba(6, 182, 212, 0.3)',
                    borderRadius: '0.75rem',
                    padding: '1rem',
                    textAlign: 'center',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{ color: '#06b6d4', marginBottom: '0.5rem' }}>
                    {skill.icon}
                  </div>
                  <div style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#06b6d4', marginBottom: '0.25rem' }}>
                    {skill.stat}
                  </div>
                  <div style={{ fontSize: '0.75rem', color: '#94a3b8' }}>
                    {skill.title}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Home;
