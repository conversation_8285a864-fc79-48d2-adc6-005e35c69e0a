import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Mail,
  MapPin,
  Github,
  Send,
  MessageCircle,
  Calendar
} from 'lucide-react';

const Contact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      alert('Thank you for your message! I\'ll get back to you soon.');
      setFormData({ name: '', email: '', subject: '', message: '' });
      setIsSubmitting(false);
    }, 1000);
  };

  const contactMethods = [
    {
      icon: <Mail size={24} />,
      title: "Email",
      value: "<EMAIL>",
      description: "Send me an email anytime",
      color: "#06b6d4",
      action: "mailto:<EMAIL>"
    },
    {
      icon: <Github size={24} />,
      title: "GitHub",
      value: "github.com/DarshanAdh",
      description: "Check out my code",
      color: "#10b981",
      action: "https://github.com/DarshanAdh"
    },
    {
      icon: <MapPin size={24} />,
      title: "Location",
      value: "Cape Girardeau, MO",
      description: "Available for remote work",
      color: "#8b5cf6",
      action: "#"
    },
    {
      icon: <Calendar size={24} />,
      title: "Schedule",
      value: "Book a Meeting",
      description: "Let's discuss your project",
      color: "#f59e0b",
      action: "#"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      padding: '6rem 2rem 2rem 2rem',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}
      >
        {/* Header */}
        <motion.div variants={itemVariants} style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #ffffff 0%, #06b6d4 50%, #3b82f6 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            margin: '0 0 1rem 0'
          }}>
            Let's Connect
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#94a3b8',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Have a project in mind or just want to chat? I'd love to hear from you!
          </p>
        </motion.div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '4rem',
          alignItems: 'start'
        }}>
          {/* Contact Methods */}
          <motion.div variants={itemVariants}>
            <h2 style={{
              fontSize: '2rem',
              color: '#06b6d4',
              marginBottom: '2rem'
            }}>
              Get in Touch
            </h2>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {contactMethods.map((method, index) => (
                <motion.a
                  key={index}
                  href={method.action}
                  whileHover={{ scale: 1.02, x: 10 }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '1rem',
                    padding: '1.5rem',
                    background: 'rgba(15, 23, 42, 0.8)',
                    border: `1px solid ${method.color}40`,
                    borderRadius: '1rem',
                    textDecoration: 'none',
                    color: 'inherit',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{
                    background: `${method.color}20`,
                    borderRadius: '50%',
                    padding: '0.75rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: method.color
                  }}>
                    {method.icon}
                  </div>
                  <div>
                    <h3 style={{
                      fontSize: '1.1rem',
                      color: method.color,
                      margin: '0 0 0.25rem 0'
                    }}>
                      {method.title}
                    </h3>
                    <p style={{
                      color: '#bfdbfe',
                      margin: '0 0 0.25rem 0',
                      fontWeight: '600'
                    }}>
                      {method.value}
                    </p>
                    <p style={{
                      color: '#94a3b8',
                      fontSize: '0.9rem',
                      margin: 0
                    }}>
                      {method.description}
                    </p>
                  </div>
                </motion.a>
              ))}
            </div>

            {/* Quick Message */}
            <motion.div
              variants={itemVariants}
              style={{
                marginTop: '2rem',
                padding: '1.5rem',
                background: 'rgba(6, 182, 212, 0.1)',
                border: '1px solid rgba(6, 182, 212, 0.3)',
                borderRadius: '1rem'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                <MessageCircle size={20} color="#06b6d4" />
                <h3 style={{ color: '#06b6d4', margin: 0 }}>Quick Response</h3>
              </div>
              <p style={{ color: '#94a3b8', margin: 0, lineHeight: '1.6' }}>
                I typically respond to emails within 24 hours. For urgent matters, 
                feel free to reach out via multiple channels.
              </p>
            </motion.div>
          </motion.div>

          {/* Contact Form */}
          <motion.div variants={itemVariants}>
            <h2 style={{
              fontSize: '2rem',
              color: '#06b6d4',
              marginBottom: '2rem'
            }}>
              Send a Message
            </h2>

            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <label style={{
                    display: 'block',
                    color: '#bfdbfe',
                    marginBottom: '0.5rem',
                    fontSize: '0.9rem',
                    fontWeight: '600'
                  }}>
                    Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      background: 'rgba(15, 23, 42, 0.8)',
                      border: '1px solid rgba(6, 182, 212, 0.3)',
                      borderRadius: '0.5rem',
                      color: 'white',
                      fontSize: '1rem'
                    }}
                    placeholder="Your name"
                  />
                </div>
                
                <div>
                  <label style={{
                    display: 'block',
                    color: '#bfdbfe',
                    marginBottom: '0.5rem',
                    fontSize: '0.9rem',
                    fontWeight: '600'
                  }}>
                    Email *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    style={{
                      width: '100%',
                      padding: '0.75rem',
                      background: 'rgba(15, 23, 42, 0.8)',
                      border: '1px solid rgba(6, 182, 212, 0.3)',
                      borderRadius: '0.5rem',
                      color: 'white',
                      fontSize: '1rem'
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  color: '#bfdbfe',
                  marginBottom: '0.5rem',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}>
                  Subject *
                </label>
                <input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    background: 'rgba(15, 23, 42, 0.8)',
                    border: '1px solid rgba(6, 182, 212, 0.3)',
                    borderRadius: '0.5rem',
                    color: 'white',
                    fontSize: '1rem'
                  }}
                  placeholder="What's this about?"
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  color: '#bfdbfe',
                  marginBottom: '0.5rem',
                  fontSize: '0.9rem',
                  fontWeight: '600'
                }}>
                  Message *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    background: 'rgba(15, 23, 42, 0.8)',
                    border: '1px solid rgba(6, 182, 212, 0.3)',
                    borderRadius: '0.5rem',
                    color: 'white',
                    fontSize: '1rem',
                    resize: 'vertical'
                  }}
                  placeholder="Tell me about your project or just say hello!"
                />
              </div>

              <motion.button
                type="submit"
                disabled={isSubmitting}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.5rem',
                  padding: '1rem 2rem',
                  background: isSubmitting 
                    ? 'rgba(6, 182, 212, 0.5)' 
                    : 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.75rem',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: isSubmitting ? 'not-allowed' : 'pointer'
                }}
              >
                <Send size={20} />
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </motion.button>
            </form>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default Contact;
