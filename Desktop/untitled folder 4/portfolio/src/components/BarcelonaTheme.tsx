import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface BarcelonaThemeProps {
  isActive: boolean;
  onToggle: () => void;
}

const BarcelonaTheme: React.FC<BarcelonaThemeProps> = ({ isActive, onToggle }) => {
  const [showCelebration, setShowCelebration] = useState(false);

  useEffect(() => {
    if (isActive) {
      setShowCelebration(true);
      const timer = setTimeout(() => setShowCelebration(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isActive]);

  const barcelonaColors = useMemo(() => ({
    primary: '#004D98', // Barça Blue
    secondary: '#A50044', // Barça Red
    gold: '#FFED02', // Barça Gold
    background: 'linear-gradient(135deg, #004D98 0%, #A50044 100%)',
    cardBackground: 'rgba(0, 77, 152, 0.1)',
    textPrimary: '#FFFFFF',
    textSecondary: '#FFED02'
  }), []);

  // Konami Code sequence: ↑↑↓↓←→←→BA
  const [konamiSequence, setKonamiSequence] = useState<string[]>([]);
  const konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const newSequence = [...konamiSequence, event.code].slice(-10);
      setKonamiSequence(newSequence);

      if (newSequence.join(',') === konamiCode.join(',')) {
        onToggle();
        setKonamiSequence([]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [konamiSequence, onToggle, konamiCode]);

  const ConfettiParticle: React.FC<{ delay: number }> = ({ delay }) => (
    <motion.div
      initial={{ y: -100, x: Math.random() * window.innerWidth, rotate: 0, opacity: 1 }}
      animate={{ 
        y: window.innerHeight + 100, 
        rotate: 360,
        opacity: 0 
      }}
      transition={{ 
        duration: 3, 
        delay,
        ease: "easeOut" 
      }}
      style={{
        position: 'fixed',
        width: '10px',
        height: '10px',
        background: Math.random() > 0.5 ? barcelonaColors.primary : barcelonaColors.secondary,
        borderRadius: '50%',
        zIndex: 9999,
        pointerEvents: 'none'
      }}
    />
  );

  const GoalCelebration: React.FC = () => (
    <AnimatePresence>
      {showCelebration && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            pointerEvents: 'none',
            zIndex: 9998
          }}
        >
          {/* Confetti */}
          {Array.from({ length: 50 }, (_, i) => (
            <ConfettiParticle key={i} delay={i * 0.1} />
          ))}
          
          {/* Goal Text */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.8, type: "spring" }}
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: '4rem',
              fontWeight: 'bold',
              color: barcelonaColors.gold,
              textShadow: `2px 2px 4px ${barcelonaColors.primary}`,
              textAlign: 'center'
            }}
          >
            ¡GOLAZO! ⚽<br />
            <span style={{ fontSize: '2rem' }}>Visca el Barça! 🔵🔴</span>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const BarcelonaToggle: React.FC = () => (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={onToggle}
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        width: '60px',
        height: '60px',
        borderRadius: '50%',
        border: 'none',
        background: isActive ? barcelonaColors.background : 'rgba(6, 182, 212, 0.2)',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '1.5rem',
        zIndex: 1000,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
      }}
      title="Toggle Barcelona Theme (or try the Konami code!)"
    >
      ⚽
    </motion.button>
  );

  // Apply Barcelona theme styles to document
  useEffect(() => {
    if (isActive) {
      document.documentElement.style.setProperty('--primary-color', barcelonaColors.primary);
      document.documentElement.style.setProperty('--secondary-color', barcelonaColors.secondary);
      document.documentElement.style.setProperty('--accent-color', barcelonaColors.gold);
      document.documentElement.style.setProperty('--background-gradient', barcelonaColors.background);
      document.body.style.background = barcelonaColors.background;
    } else {
      // Reset to original theme
      document.documentElement.style.removeProperty('--primary-color');
      document.documentElement.style.removeProperty('--secondary-color');
      document.documentElement.style.removeProperty('--accent-color');
      document.documentElement.style.removeProperty('--background-gradient');
      document.body.style.background = '';
    }

    return () => {
      if (!isActive) {
        document.documentElement.style.removeProperty('--primary-color');
        document.documentElement.style.removeProperty('--secondary-color');
        document.documentElement.style.removeProperty('--accent-color');
        document.documentElement.style.removeProperty('--background-gradient');
        document.body.style.background = '';
      }
    };
  }, [isActive, barcelonaColors]);

  return (
    <>
      <BarcelonaToggle />
      <GoalCelebration />
      
      {/* Barcelona Badge */}
      <AnimatePresence>
        {isActive && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            style={{
              position: 'fixed',
              top: '20px',
              left: '20px',
              background: barcelonaColors.background,
              padding: '10px 15px',
              borderRadius: '25px',
              color: barcelonaColors.textPrimary,
              fontWeight: 'bold',
              fontSize: '0.9rem',
              zIndex: 1000,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            🔵🔴 Més que un club! ⚽
          </motion.div>
        )}
      </AnimatePresence>

      {/* Hint for Konami Code */}
      {!isActive && konamiSequence.length > 0 && konamiSequence.length < 10 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          style={{
            position: 'fixed',
            bottom: '100px',
            right: '20px',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '8px',
            fontSize: '0.8rem',
            zIndex: 1000
          }}
        >
          Konami progress: {konamiSequence.length}/10 🎮
        </motion.div>
      )}
    </>
  );
};

export default BarcelonaTheme;
