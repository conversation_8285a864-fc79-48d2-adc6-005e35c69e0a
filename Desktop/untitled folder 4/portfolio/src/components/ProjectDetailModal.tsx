import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ExternalLink, Github, Zap, Users, Calendar, Target, CheckCircle } from 'lucide-react';

interface ProjectDetailModalProps {
  project: any;
  isOpen: boolean;
  onClose: () => void;
}

const ProjectDetailModal: React.FC<ProjectDetailModalProps> = ({ project, isOpen, onClose }) => {
  if (!project) return null;

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      transition: { duration: 0.2 }
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            variants={backdropVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={onClose}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(0, 0, 0, 0.8)',
              zIndex: 9999,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '2rem'
            }}
          />
          
          {/* Modal */}
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              position: 'fixed',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              background: 'rgba(15, 23, 42, 0.95)',
              border: '1px solid rgba(6, 182, 212, 0.3)',
              borderRadius: '1.5rem',
              maxWidth: '900px',
              maxHeight: '90vh',
              width: '90%',
              overflow: 'auto',
              zIndex: 10000,
              backdropFilter: 'blur(10px)'
            }}
          >
            {/* Header */}
            <div style={{
              padding: '2rem',
              borderBottom: '1px solid rgba(6, 182, 212, 0.2)',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start'
            }}>
              <div>
                <h2 style={{
                  fontSize: '2rem',
                  color: '#06b6d4',
                  marginBottom: '0.5rem'
                }}>
                  {project.title}
                </h2>
                <p style={{
                  color: '#94a3b8',
                  fontSize: '1.1rem',
                  lineHeight: '1.6'
                }}>
                  {project.longDescription}
                </p>
              </div>
              
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={onClose}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#94a3b8',
                  cursor: 'pointer',
                  padding: '0.5rem'
                }}
              >
                <X size={24} />
              </motion.button>
            </div>

            {/* Content */}
            <div style={{ padding: '2rem' }}>
              {/* Project Stats */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                <div style={{
                  background: 'rgba(6, 182, 212, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <Zap size={24} color="#06b6d4" style={{ margin: '0 auto 0.5rem' }} />
                  <div style={{ color: '#06b6d4', fontWeight: 'bold' }}>{project.impact}</div>
                  <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Impact</div>
                </div>
                
                <div style={{
                  background: 'rgba(16, 185, 129, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <Calendar size={24} color="#10b981" style={{ margin: '0 auto 0.5rem' }} />
                  <div style={{ color: '#10b981', fontWeight: 'bold' }}>{project.timeline}</div>
                  <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Duration</div>
                </div>
                
                <div style={{
                  background: 'rgba(139, 92, 246, 0.1)',
                  padding: '1rem',
                  borderRadius: '0.75rem',
                  textAlign: 'center'
                }}>
                  <Users size={24} color="#8b5cf6" style={{ margin: '0 auto 0.5rem' }} />
                  <div style={{ color: '#8b5cf6', fontWeight: 'bold' }}>{project.team}</div>
                  <div style={{ color: '#94a3b8', fontSize: '0.9rem' }}>Team</div>
                </div>
              </div>

              {/* Real-World Impact Section (for Roadside Assistance) */}
              {project.detailedImpact && (
                <div style={{ marginBottom: '2rem' }}>
                  <h3 style={{
                    fontSize: '1.5rem',
                    color: '#06b6d4',
                    marginBottom: '1rem',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    <Target size={20} />
                    Real-World Impact
                  </h3>
                  
                  <div style={{
                    background: 'rgba(15, 23, 42, 0.8)',
                    padding: '1.5rem',
                    borderRadius: '1rem',
                    marginBottom: '1.5rem'
                  }}>
                    <h4 style={{ color: '#f59e0b', marginBottom: '0.75rem' }}>Problem Solved:</h4>
                    <p style={{ color: '#bfdbfe', lineHeight: '1.6' }}>
                      {project.detailedImpact.problemSolved}
                    </p>
                  </div>

                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                    gap: '1rem',
                    marginBottom: '1.5rem'
                  }}>
                    <div style={{
                      background: 'rgba(16, 185, 129, 0.1)',
                      padding: '1.5rem',
                      borderRadius: '1rem'
                    }}>
                      <h4 style={{ color: '#10b981', marginBottom: '1rem' }}>Solutions Delivered:</h4>
                      {project.detailedImpact.realWorldSolutions.map((solution: string, index: number) => (
                        <div key={index} style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '0.5rem',
                          marginBottom: '0.5rem',
                          color: '#bfdbfe',
                          fontSize: '0.9rem'
                        }}>
                          <CheckCircle size={16} color="#10b981" style={{ marginTop: '0.1rem', flexShrink: 0 }} />
                          {solution}
                        </div>
                      ))}
                    </div>

                    <div style={{
                      background: 'rgba(6, 182, 212, 0.1)',
                      padding: '1.5rem',
                      borderRadius: '1rem'
                    }}>
                      <h4 style={{ color: '#06b6d4', marginBottom: '1rem' }}>Technical Innovations:</h4>
                      {project.detailedImpact.technicalInnovations.map((innovation: string, index: number) => (
                        <div key={index} style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          gap: '0.5rem',
                          marginBottom: '0.5rem',
                          color: '#bfdbfe',
                          fontSize: '0.9rem'
                        }}>
                          <Zap size={16} color="#06b6d4" style={{ marginTop: '0.1rem', flexShrink: 0 }} />
                          {innovation}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div style={{
                    background: 'rgba(245, 158, 11, 0.1)',
                    padding: '1.5rem',
                    borderRadius: '1rem'
                  }}>
                    <h4 style={{ color: '#f59e0b', marginBottom: '1rem' }}>Key Metrics:</h4>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                      gap: '1rem'
                    }}>
                      {project.detailedImpact.metrics.map((metric: string, index: number) => (
                        <div key={index} style={{
                          background: 'rgba(245, 158, 11, 0.2)',
                          padding: '0.75rem',
                          borderRadius: '0.5rem',
                          textAlign: 'center',
                          color: '#fbbf24',
                          fontWeight: '600',
                          fontSize: '0.9rem'
                        }}>
                          {metric}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Features */}
              <div style={{ marginBottom: '2rem' }}>
                <h3 style={{
                  fontSize: '1.5rem',
                  color: '#06b6d4',
                  marginBottom: '1rem'
                }}>
                  Key Features
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '0.75rem'
                }}>
                  {project.features.map((feature: string, index: number) => (
                    <div key={index} style={{
                      background: 'rgba(6, 182, 212, 0.1)',
                      padding: '0.75rem 1rem',
                      borderRadius: '0.5rem',
                      color: '#bfdbfe',
                      fontSize: '0.9rem',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem'
                    }}>
                      <CheckCircle size={16} color="#06b6d4" />
                      {feature}
                    </div>
                  ))}
                </div>
              </div>

              {/* Technologies */}
              <div style={{ marginBottom: '2rem' }}>
                <h3 style={{
                  fontSize: '1.5rem',
                  color: '#06b6d4',
                  marginBottom: '1rem'
                }}>
                  Technologies Used
                </h3>
                <div style={{
                  display: 'flex',
                  gap: '0.5rem',
                  flexWrap: 'wrap'
                }}>
                  {project.technologies.map((tech: string, index: number) => (
                    <span key={index} style={{
                      background: 'rgba(6, 182, 212, 0.2)',
                      color: '#06b6d4',
                      padding: '0.5rem 1rem',
                      borderRadius: '1rem',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
                <motion.a
                  href={project.github}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    background: 'rgba(6, 182, 212, 0.2)',
                    color: '#06b6d4',
                    borderRadius: '0.75rem',
                    textDecoration: 'none',
                    fontWeight: '600'
                  }}
                >
                  <Github size={20} />
                  View Code
                </motion.a>
                
                <motion.a
                  href={project.demo}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1.5rem',
                    background: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
                    color: 'white',
                    borderRadius: '0.75rem',
                    textDecoration: 'none',
                    fontWeight: '600'
                  }}
                >
                  <ExternalLink size={20} />
                  Live Demo
                </motion.a>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default ProjectDetailModal;
