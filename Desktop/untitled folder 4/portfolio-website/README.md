# Dar<PERSON>kari - Portfolio Website

A modern, responsive portfolio website built with React, Vite, and Framer Motion.

## 🚀 Quick Start

1. Install dependencies: `npm install`
2. Start dev server: `npm run dev`
3. Build for production: `npm run build`

## 🌐 Deploy to Netlify

1. Run `npm run build`
2. Drag & drop the `dist` folder to Netlify
3. Or connect your GitHub repo to Netlify for auto-deployment

## 🛠️ Technologies

- React 19 + Vite
- Framer Motion for animations
- Lucide React for icons
- Modern CSS with gradients

## 📞 Contact

- Email: darshana<PERSON><EMAIL>
- GitHub: github.com/DarshanAdh

Built with ❤️ by <PERSON><PERSON>
