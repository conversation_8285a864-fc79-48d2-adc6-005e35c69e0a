import React from 'react';
import { motion } from 'framer-motion';
import Hero from './components/Hero';
import About from './components/About';
import Skills from './components/Skills';
import Projects from './components/Projects';
import Hobbies from './components/Hobbies';
import Contact from './components/Contact';
import Navigation from './components/Navigation';
import './App.css';
import './styles/design-system.css';

const App: React.FC = () => {
  return (
    <div className="App min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100">
      {/* Blue & Black Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-600/15 to-slate-900/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute top-1/2 -left-40 w-80 h-80 bg-gradient-to-tr from-slate-800/15 to-blue-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-tl from-blue-400/10 to-slate-700/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            x: [0, 50, 0],
            y: [0, -30, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Main Content with Section Separators */}
      <div className="relative z-10">
        <Navigation />

        {/* Hero Section */}
        <div className="bg-gradient-to-br from-blue-600 via-slate-800 to-black">
          <Hero />
        </div>

        {/* Section Separator */}
        <div className="h-16 bg-gradient-to-r from-blue-600 to-slate-800 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-12 h-1 bg-white/30 rounded-full"></div>
          </div>
        </div>

        {/* About Section */}
        <div className="bg-white border-l-4 border-blue-600 shadow-xl">
          <About />
        </div>

        {/* Section Separator */}
        <div className="h-16 bg-gradient-to-r from-slate-800 to-blue-600 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-12 h-1 bg-white/30 rounded-full"></div>
          </div>
        </div>

        {/* Skills Section */}
        <div className="bg-gradient-to-br from-slate-900 to-blue-900 border-l-4 border-blue-400">
          <Skills />
        </div>

        {/* Section Separator */}
        <div className="h-16 bg-gradient-to-r from-blue-600 to-slate-800 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-12 h-1 bg-white/30 rounded-full"></div>
          </div>
        </div>

        {/* Projects Section */}
        <div className="bg-slate-50 border-l-4 border-slate-800 shadow-xl">
          <Projects />
        </div>

        {/* Section Separator */}
        <div className="h-16 bg-gradient-to-r from-slate-800 to-blue-600 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-12 h-1 bg-white/30 rounded-full"></div>
          </div>
        </div>

        {/* Hobbies Section */}
        <div className="bg-white border-l-4 border-blue-600 shadow-xl">
          <Hobbies />
        </div>

        {/* Section Separator */}
        <div className="h-16 bg-gradient-to-r from-blue-600 to-slate-800 relative">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-12 h-1 bg-white/30 rounded-full"></div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="bg-gradient-to-br from-slate-900 to-blue-900">
          <Contact />
        </div>
      </div>
    </div>
  );
};

export default App;
