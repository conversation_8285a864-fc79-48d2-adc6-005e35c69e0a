import React from 'react';
import { motion } from 'framer-motion';
import ModernHero from './components/ModernHero';
import ModernAbout from './components/ModernAbout';
import ModernSkills from './components/ModernSkills';
import ModernProjects from './components/ModernProjects';
import ModernHobbies from './components/ModernHobbies';
import ModernContact from './components/ModernContact';
import Navigation from './components/Navigation';
import './App.css';
import './styles/design-system.css';

const App: React.FC = () => {
  return (
    <div className="App min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Modern Sections */}
      <ModernHero />
      <ModernAbout />
      <ModernSkills />
      <ModernProjects />
      <ModernHobbies />
      <ModernContact />
    </div>
  );
};

export default App;
