// Design System Types
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  success: string;
  warning: string;
  error: string;
}

export interface GradientConfig {
  from: string;
  via?: string;
  to: string;
  direction?: 'r' | 'l' | 'b' | 't' | 'br' | 'bl' | 'tr' | 'tl';
}

export interface ButtonVariant {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'gradient';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

// Component Types
export interface SkillItem {
  name: string;
  level: number;
  description: string;
  years: string;
  category: string;
}

export interface SkillCategory {
  icon: React.ReactNode;
  title: string;
  color: GradientConfig;
  skills: SkillItem[];
}

export interface ProjectTechnology {
  frontend?: string[];
  backend?: string[];
  database?: string[];
  tools?: string[];
  apis?: string[];
  authentication?: string[];
  deployment?: string[];
  core?: string[];
  concepts?: string[];
  features?: string[];
}

export interface ProjectAchievement {
  metric: string;
  description: string;
}

export interface ProjectFeature {
  title: string;
  description: string;
}

export interface Project {
  title: string;
  subtitle: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  duration: string;
  teamSize: string;
  status: string;
  technologies: ProjectTechnology;
  achievements: ProjectAchievement[];
  features: ProjectFeature[];
  challenges: string[];
  impact: string;
  gradient: GradientConfig;
  demoUrl: string;
  githubUrl: string;
}

export interface ContactInfo {
  icon: React.ReactNode;
  title: string;
  value: string;
  link?: string;
  description: string;
  color: GradientConfig;
}

export interface HobbyDetails {
  favoriteGenres?: string[];
  recentFavorites?: string[];
  favoriteArtists?: string[];
  platforms?: string[];
  position?: string;
  experience?: string;
  activity: string;
  connection: string;
}

export interface HobbyStat {
  label: string;
  value: string;
}

export interface Hobby {
  icon: React.ReactNode;
  title: string;
  description: string;
  gradient: GradientConfig;
  details: HobbyDetails;
  stats: HobbyStat[];
}

// Animation Types
export interface AnimationConfig {
  initial?: object;
  animate?: object;
  exit?: object;
  transition?: object;
  whileHover?: object;
  whileTap?: object;
}

// Layout Types
export interface SectionProps {
  id: string;
  className?: string;
  background?: 'light' | 'dark' | 'gradient' | 'pattern';
  children: React.ReactNode;
}

export interface ContainerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
  children: React.ReactNode;
}

// Navigation Types
export interface NavItem {
  name: string;
  href: string;
}

// Personal Information Types
export interface PersonalInfo {
  name: string;
  title: string;
  email: string;
  phone: string;
  location: string;
  github: string;
  linkedin?: string;
}

// Achievement Types
export interface Achievement {
  metric: string;
  label: string;
  description: string;
}
