/* Enhanced Design System */

/* CSS Custom Properties for Design Tokens - Blue & Black Theme */
:root {
  /* Blue & Black Color Palette */
  --color-primary: #1e40af;
  --color-primary-light: #3b82f6;
  --color-primary-dark: #1e3a8a;
  --color-secondary: #0f172a;
  --color-accent: #60a5fa;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-white: #ffffff;
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-900: #0f172a;

  /* Blue & Black Gradients */
  --gradient-primary: linear-gradient(135deg, #1e40af 0%, #0f172a 100%);
  --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  --gradient-accent: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
  
  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Enhanced Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  font-family: inherit;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-md);
}

.btn-md {
  padding: var(--space-md) var(--space-xl);
  font-size: var(--font-size-base);
  border-radius: var(--radius-lg);
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-xl) var(--space-3xl);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-2xl);
}

/* Enhanced Button Variants - Blue & Black Theme */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
  border: 2px solid transparent;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(30, 64, 175, 0.4);
  border-color: var(--color-primary-light);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-md);
  border: 2px solid transparent;
}

.btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: var(--color-primary);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.btn-gradient {
  background: var(--gradient-accent);
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-gradient:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

/* Enhanced Card System */
.card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.card:hover::before {
  transform: scaleX(1);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.card-elevated {
  box-shadow: var(--shadow-xl);
}

.card-elevated:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-2xl);
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Section Backgrounds */
.section-gradient-1 {
  background: var(--gradient-primary);
}

.section-gradient-2 {
  background: var(--gradient-secondary);
}

.section-gradient-3 {
  background: var(--gradient-accent);
}

.section-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
}

.section-mesh {
  background: 
    radial-gradient(ellipse 80% 80% at 50% -20%, rgba(102, 126, 234, 0.3), transparent),
    radial-gradient(ellipse 80% 80% at 80% 60%, rgba(118, 75, 162, 0.3), transparent),
    radial-gradient(ellipse 80% 80% at 40% 40%, rgba(240, 147, 251, 0.3), transparent);
}

/* Animated Backgrounds */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 20s linear infinite;
}

/* Enhanced Typography */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

/* Responsive Utilities */
@media (max-width: 768px) {
  .btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-base);
  }
  
  .btn-xl {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--font-size-lg);
  }
}
