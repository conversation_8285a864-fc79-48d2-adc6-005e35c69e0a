import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Film, Music, Trophy, X, Play, Heart, Star, Calendar, Award } from 'lucide-react';

const HobbiesPage: React.FC = () => {
  const [selectedHobby, setSelectedHobby] = useState<number | null>(null);

  const hobbies = [
    {
      id: 1,
      icon: <Film className="w-8 h-8" />,
      title: "Movies & Cinema",
      subtitle: "Sci-Fi Enthusiast",
      description: "Passionate about storytelling through film, especially sci-fi that inspires my coding creativity and problem-solving approach.",
      gradient: "from-blue-600 to-blue-800",
      stats: [
        { label: "Movies Watched", value: "200+", icon: <Film className="w-4 h-4" /> },
        { label: "Favorite Genre", value: "Sci-Fi", icon: <Star className="w-4 h-4" /> },
        { label: "Cinema Visits", value: "Monthly", icon: <Calendar className="w-4 h-4" /> }
      ],
      favorites: ["Inception", "The Matrix", "Interstellar", "Blade Runner 2049", "Dune", "Ex Machina"],
      quote: "There is no spoon - My debugging philosophy",
      connection: "Understanding narrative flow helps me design intuitive user journeys and create engaging interfaces. The logical problem-solving in sci-fi films directly influences my approach to coding challenges.",
      image: "/api/placeholder/300/200",
      funFacts: [
        "Can quote The Matrix trilogy from memory",
        "Analyzes plot structures for UX inspiration",
        "Prefers films with complex timelines"
      ]
    },
    {
      id: 2,
      icon: <Music className="w-8 h-8" />,
      title: "Music & Audio",
      subtitle: "Playlist Curator",
      description: "Music fuels my creativity and focus during coding sessions. I curate the perfect soundtracks for different types of development work.",
      gradient: "from-blue-700 to-blue-900",
      stats: [
        { label: "Hours Daily", value: "6+", icon: <Music className="w-4 h-4" /> },
        { label: "Playlists Created", value: "15+", icon: <Star className="w-4 h-4" /> },
        { label: "Coding Soundtrack", value: "Lo-fi", icon: <Award className="w-4 h-4" /> }
      ],
      favorites: ["Nujabes", "Emancipator", "Tycho", "Boards of Canada", "Ólafur Arnalds"],
      quote: "Lo-fi beats = bug-free code",
      connection: "Musical patterns and rhythms enhance my ability to recognize code patterns and write clean, structured code. Different genres help me focus on different types of tasks.",
      image: "/api/placeholder/300/200",
      funFacts: [
        "Has a playlist for every coding mood",
        "Discovers new artists weekly",
        "Believes tempo affects coding speed"
      ]
    },
    {
      id: 3,
      icon: <Trophy className="w-8 h-8" />,
      title: "Soccer & Sports",
      subtitle: "FC Barcelona Fan",
      description: "Team sports teach collaboration and strategy. Proud supporter of FC Barcelona for over 8 years, appreciating their tactical brilliance.",
      gradient: "from-blue-800 to-slate-900",
      stats: [
        { label: "Years Playing", value: "10+", icon: <Trophy className="w-4 h-4" /> },
        { label: "Favorite Team", value: "FC Barcelona", icon: <Heart className="w-4 h-4" /> },
        { label: "Games per Month", value: "4-6", icon: <Calendar className="w-4 h-4" /> }
      ],
      favorites: ["Lionel Messi", "Xavi", "Iniesta", "Pedri", "Gavi"],
      quote: "Més que un club! 🔵🔴",
      connection: "Team sports teach communication, strategy, and working towards common goals - essential skills for agile development and collaborative coding projects.",
      image: "/api/placeholder/300/200",
      funFacts: [
        "Never missed a Clásico match",
        "Owns 5 Barcelona jerseys",
        "Analyzes team formations like code architecture"
      ]
    }
  ];

  const hobbyStats = [
    { label: "Creative Outlets", value: "3", description: "Different ways to stay inspired" },
    { label: "Weekly Hours", value: "20+", description: "Time spent on hobbies" },
    { label: "Years Combined", value: "25+", description: "Total experience across hobbies" },
    { label: "Inspiration Level", value: "∞", description: "Endless creativity fuel" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Beyond the <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Code</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            When I'm not coding, you'll find me exploring these passions that fuel my creativity and enhance my problem-solving skills
          </p>
        </motion.div>

        {/* Hobby Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {hobbyStats.map((stat, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 text-center border border-blue-500/20"
            >
              <div className="text-3xl font-bold text-blue-400 mb-2">{stat.value}</div>
              <div className="text-white font-semibold mb-1">{stat.label}</div>
              <div className="text-blue-300 text-xs">{stat.description}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Hobbies Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {hobbies.map((hobby, index) => (
            <motion.div
              key={hobby.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10, scale: 1.02 }}
              onClick={() => setSelectedHobby(hobby.id)}
              className="bg-blue-900/30 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden cursor-pointer hover:shadow-2xl transition-all duration-300 group border border-blue-500/20"
            >
              {/* Card Header */}
              <div className={`h-48 bg-gradient-to-br ${hobby.gradient} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                      className="w-16 h-16 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center"
                    >
                      {hobby.icon}
                    </motion.div>
                    <p className="text-sm opacity-80">Hobby Photo</p>
                    <p className="text-xs opacity-60">Click to explore</p>
                  </div>
                </div>
                
                {/* Hover Overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  className="absolute inset-0 bg-black/40 flex items-center justify-center"
                >
                  <Play className="w-12 h-12 text-white" />
                </motion.div>
              </div>

              {/* Card Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-2">{hobby.title}</h3>
                <p className="text-blue-400 font-medium mb-3">{hobby.subtitle}</p>
                <p className="text-blue-100 text-sm leading-relaxed mb-4">{hobby.description}</p>
                
                {/* Quick Stats */}
                <div className="grid grid-cols-1 gap-2">
                  {hobby.stats.slice(0, 2).map((stat, statIndex) => (
                    <div key={statIndex} className="flex justify-between items-center text-sm bg-blue-800/20 p-2 rounded border border-blue-600/20">
                      <span className="text-blue-300 flex items-center gap-2">
                        {stat.icon}
                        {stat.label}
                      </span>
                      <span className="font-semibold text-blue-200">{stat.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Fun Facts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-8 border border-blue-500/20"
        >
          <h3 className="text-2xl font-bold text-center text-white mb-8">
            🎉 How My Hobbies <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Enhance My Coding</span>
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            {hobbies.map((hobby, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-blue-800/20 p-6 rounded-xl shadow-lg text-center border border-blue-600/20"
              >
                <div className="text-blue-300 mb-3 flex justify-center">
                  {hobby.icon}
                </div>
                <h4 className="font-semibold text-white mb-2">"{hobby.quote}"</h4>
                <p className="text-blue-100 text-sm leading-relaxed">{hobby.connection}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Modal for Hobby Details */}
      <AnimatePresence>
        {selectedHobby && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedHobby(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-blue-900/95 backdrop-blur-md rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-blue-500/30"
            >
              {(() => {
                const hobby = hobbies.find(h => h.id === selectedHobby);
                if (!hobby) return null;

                return (
                  <>
                    {/* Modal Header */}
                    <div className={`bg-gradient-to-br ${hobby.gradient} p-6 text-white relative`}>
                      <button
                        onClick={() => setSelectedHobby(null)}
                        className="absolute top-4 right-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                          {hobby.icon}
                        </div>
                        <div>
                          <h3 className="text-3xl font-bold">{hobby.title}</h3>
                          <p className="text-white/80 text-lg">{hobby.subtitle}</p>
                        </div>
                      </div>
                    </div>

                    {/* Modal Content */}
                    <div className="p-6 space-y-6">
                      <p className="text-blue-100 leading-relaxed text-lg">{hobby.description}</p>

                      {/* Stats Grid */}
                      <div>
                        <h4 className="font-semibold text-white mb-3 flex items-center gap-2 text-xl">
                          <Star className="w-5 h-5 text-yellow-400" />
                          Stats & Achievements
                        </h4>
                        <div className="grid grid-cols-3 gap-4">
                          {hobby.stats.map((stat, index) => (
                            <div key={index} className="text-center p-4 bg-blue-800/30 rounded-lg border border-blue-600/30">
                              <div className="flex justify-center text-blue-400 mb-2">
                                {stat.icon}
                              </div>
                              <div className="text-lg font-bold text-blue-300">{stat.value}</div>
                              <div className="text-xs text-blue-200">{stat.label}</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Favorites */}
                      <div>
                        <h4 className="font-semibold text-white mb-3 flex items-center gap-2 text-xl">
                          <Heart className="w-5 h-5 text-red-400" />
                          Favorites
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {hobby.favorites.map((item, index) => (
                            <span
                              key={index}
                              className="bg-blue-800/30 text-blue-200 px-4 py-2 rounded-full text-sm font-medium border border-blue-600/30"
                            >
                              {item}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Fun Facts */}
                      <div>
                        <h4 className="font-semibold text-white mb-3 flex items-center gap-2 text-xl">
                          🎉 Fun Facts
                        </h4>
                        <div className="space-y-2">
                          {hobby.funFacts.map((fact, index) => (
                            <div key={index} className="flex items-center gap-3 p-3 bg-blue-800/20 rounded-lg border border-blue-600/20">
                              <span className="text-blue-400">•</span>
                              <span className="text-blue-100">{fact}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Connection to Development */}
                      <div className="bg-blue-800/30 p-6 rounded-lg border border-blue-500/30">
                        <h4 className="font-semibold text-white mb-3 text-xl">Connection to Development</h4>
                        <p className="text-blue-100 italic leading-relaxed">"{hobby.connection}"</p>
                      </div>
                    </div>
                  </>
                );
              })()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HobbiesPage;
