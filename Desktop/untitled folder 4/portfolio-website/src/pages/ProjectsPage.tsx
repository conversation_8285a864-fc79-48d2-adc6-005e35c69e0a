import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Github, Smartphone, Globe, Database, Gamepad2, Filter, Calendar, Users, TrendingUp } from 'lucide-react';

const ProjectsPage: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const filters = ['All', 'Mobile', 'Web', 'Healthcare', 'Desktop'];

  const projects = [
    {
      id: 1,
      title: "MediRemind",
      subtitle: "Healthcare Innovation Platform",
      description: "Cross-platform mobile app revolutionizing medication adherence through intelligent automation and real-time monitoring. Features include smart notifications, progress tracking, and administrative dashboard.",
      category: ["Mobile", "Healthcare"],
      image: "/api/placeholder/400/250",
      technologies: ["React Native", "Firebase", "Expo", "Cloud Functions", "TypeScript"],
      metrics: [
        { label: "Adherence Improvement", value: "35%", icon: <TrendingUp className="w-4 h-4" /> },
        { label: "Admin Time Reduction", value: "40%", icon: <Users className="w-4 h-4" /> },
        { label: "User Capacity", value: "1000+", icon: <Database className="w-4 h-4" /> }
      ],
      features: [
        "Smart medication reminders",
        "Progress tracking & analytics",
        "Administrative dashboard",
        "Real-time notifications",
        "User management system"
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/mediremind",
      gradient: "from-blue-600 to-blue-800",
      status: "Production",
      duration: "6 months",
      team: "Solo Project"
    },
    {
      id: 2,
      title: "RoadAssist",
      subtitle: "Emergency Response Platform",
      description: "Full-stack web application providing real-time location-based roadside assistance with intelligent helper matching algorithm and comprehensive service management.",
      category: ["Web"],
      image: "/api/placeholder/400/250",
      technologies: ["React", "TypeScript", "Node.js", "MongoDB", "Express"],
      metrics: [
        { label: "Response Time", value: "< 2 min", icon: <TrendingUp className="w-4 h-4" /> },
        { label: "Test Coverage", value: "95%", icon: <Database className="w-4 h-4" /> },
        { label: "Uptime", value: "99.9%", icon: <Users className="w-4 h-4" /> }
      ],
      features: [
        "Real-time location tracking",
        "Intelligent helper matching",
        "Service request management",
        "Payment integration",
        "Rating & review system"
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/roadside-assistance",
      gradient: "from-blue-700 to-blue-900",
      status: "Completed",
      duration: "4 months",
      team: "Team of 3"
    },
    {
      id: 3,
      title: "RestaurantPro",
      subtitle: "Business Management Suite",
      description: "Comprehensive web application transforming restaurant operations with integrated order processing, inventory management, and detailed analytics dashboard.",
      category: ["Web"],
      image: "/api/placeholder/400/250",
      technologies: ["Python", "Flask", "MySQL", "JavaScript", "Bootstrap"],
      metrics: [
        { label: "Processing Speed", value: "+50%", icon: <TrendingUp className="w-4 h-4" /> },
        { label: "Customer Retention", value: "+30%", icon: <Users className="w-4 h-4" /> },
        { label: "Inventory Accuracy", value: "100%", icon: <Database className="w-4 h-4" /> }
      ],
      features: [
        "Order management system",
        "Inventory tracking",
        "Customer management",
        "Sales analytics",
        "Staff scheduling"
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/restaurant-management",
      gradient: "from-blue-800 to-slate-900",
      status: "Completed",
      duration: "3 months",
      team: "Solo Project"
    },
    {
      id: 4,
      title: "ArcadeMaster",
      subtitle: "Interactive Gaming Platform",
      description: "Sophisticated desktop gaming application with advanced physics simulation, multiple difficulty levels, and engaging user experience built with Java and JavaFX.",
      category: ["Desktop"],
      image: "/api/placeholder/400/250",
      technologies: ["Java", "JavaFX", "OOP Design", "Scene Builder"],
      metrics: [
        { label: "Frame Rate", value: "60 FPS", icon: <TrendingUp className="w-4 h-4" /> },
        { label: "Difficulty Levels", value: "5", icon: <Database className="w-4 h-4" /> },
        { label: "Code Quality", value: "A+", icon: <Users className="w-4 h-4" /> }
      ],
      features: [
        "Physics-based gameplay",
        "Multiple difficulty levels",
        "Score tracking system",
        "Smooth animations",
        "Responsive controls"
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/javafx-ball-game",
      gradient: "from-slate-700 to-blue-800",
      status: "Completed",
      duration: "2 months",
      team: "Solo Project"
    }
  ];

  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category.includes(activeFilter));

  const getIcon = (category: string[]) => {
    if (category.includes('Mobile')) return <Smartphone className="w-5 h-5" />;
    if (category.includes('Web')) return <Globe className="w-5 h-5" />;
    if (category.includes('Desktop')) return <Gamepad2 className="w-5 h-5" />;
    return <Database className="w-5 h-5" />;
  };

  const statusColors = {
    "Production": "bg-green-500/20 text-green-300 border-green-500/30",
    "Completed": "bg-blue-500/20 text-blue-300 border-blue-500/30",
    "In Progress": "bg-yellow-500/20 text-yellow-300 border-yellow-500/30"
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Featured <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Projects</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Explore my journey through innovative software solutions that solve real-world problems
          </p>
        </motion.div>

        {/* Project Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {filters.map((filter) => (
            <motion.button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeFilter === filter
                  ? 'bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg border border-blue-400/30'
                  : 'bg-blue-900/30 text-blue-200 hover:bg-blue-800/40 border border-blue-500/20'
              }`}
            >
              <Filter size={16} />
              {filter}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div layout className="grid lg:grid-cols-2 gap-8 mb-16">
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.4 }}
                className="bg-blue-900/30 backdrop-blur-md rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 border border-blue-500/20"
              >
                {/* Project Image */}
                <div className={`h-48 bg-gradient-to-br ${project.gradient} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                        {getIcon(project.category)}
                      </div>
                      <p className="text-sm opacity-80">Project Screenshot</p>
                      <p className="text-xs opacity-60">Coming Soon</p>
                    </div>
                  </div>
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/20 backdrop-blur-md text-white px-3 py-1 rounded-full text-sm font-medium">
                      {project.category.join(' • ')}
                    </span>
                  </div>

                  {/* Status Badge */}
                  <div className="absolute top-4 right-4">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${statusColors[project.status as keyof typeof statusColors]}`}>
                      {project.status}
                    </span>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-white mb-2">{project.title}</h3>
                    <p className="text-blue-400 font-medium mb-3">{project.subtitle}</p>
                    <p className="text-blue-100 leading-relaxed text-sm">{project.description}</p>
                  </div>

                  {/* Project Info */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="flex items-center gap-2 text-blue-200">
                      <Calendar className="w-4 h-4" />
                      <span>{project.duration}</span>
                    </div>
                    <div className="flex items-center gap-2 text-blue-200">
                      <Users className="w-4 h-4" />
                      <span>{project.team}</span>
                    </div>
                  </div>

                  {/* Technologies */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, index) => (
                        <span
                          key={index}
                          className="bg-blue-800/30 text-blue-200 px-3 py-1 rounded-full text-xs font-medium border border-blue-600/30"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {project.metrics.map((metric, index) => (
                      <div key={index} className="text-center bg-blue-800/20 p-3 rounded-lg border border-blue-600/20">
                        <div className="flex justify-center text-blue-400 mb-1">
                          {metric.icon}
                        </div>
                        <div className="text-sm font-bold text-blue-300">{metric.value}</div>
                        <div className="text-xs text-blue-200">{metric.label}</div>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <motion.a
                      href={project.demoUrl}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 bg-gradient-to-r from-blue-600 to-blue-800 text-white py-3 px-4 rounded-xl font-semibold text-center hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      <ExternalLink size={16} />
                      Live Demo
                    </motion.a>
                    <motion.a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 bg-slate-800 text-white py-3 px-4 rounded-xl font-semibold text-center hover:bg-slate-700 transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      <Github size={16} />
                      Source Code
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Project Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-8">
            Project <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Impact</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 shadow-lg border border-blue-500/20">
              <div className="text-3xl font-bold text-blue-400 mb-2">4+</div>
              <div className="text-blue-200 text-sm">Major Projects</div>
            </div>
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 shadow-lg border border-blue-500/20">
              <div className="text-3xl font-bold text-blue-400 mb-2">15+</div>
              <div className="text-blue-200 text-sm">Technologies Used</div>
            </div>
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 shadow-lg border border-blue-500/20">
              <div className="text-3xl font-bold text-blue-400 mb-2">5000+</div>
              <div className="text-blue-200 text-sm">Lines of Code</div>
            </div>
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 shadow-lg border border-blue-500/20">
              <div className="text-3xl font-bold text-blue-400 mb-2">100%</div>
              <div className="text-blue-200 text-sm">Passion Driven</div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default ProjectsPage;
