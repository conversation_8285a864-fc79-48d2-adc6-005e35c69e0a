import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  ArrowRight, 
  Mail, 
  MapPin, 
  Github, 
  ExternalLink,
  Code, 
  Database, 
  Smartphone, 
  Cloud,
  Star,
  Zap,
  Rocket,
  Brain,
  Heart,
  Music,
  Coffee,
  Terminal,
  ChevronDown
} from 'lucide-react';

const ModernHomePage: React.FC = () => {
  const [currentRole, setCurrentRole] = useState(0);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  
  const roles = [
    "🎓 Senior CS Student",
    "💻 Full-Stack Developer", 
    "📊 Data Analyst",
    "🚀 Digital Innovator"
  ];

  const skills = [
    {
      icon: "🏥",
      title: "Healthcare Tech",
      stat: "35% improvement",
      description: "Optimizing healthcare workflows",
      gradient: "from-emerald-400 to-teal-600",
      glow: "shadow-emerald-500/25"
    },
    {
      icon: "💻",
      title: "Web Applications", 
      stat: "4+ Projects",
      description: "Full-stack web solutions",
      gradient: "from-blue-400 to-cyan-600",
      glow: "shadow-blue-500/25"
    },
    {
      icon: "📱",
      title: "Mobile Apps",
      stat: "Cross-Platform",
      description: "React Native & Flutter",
      gradient: "from-purple-400 to-pink-600", 
      glow: "shadow-purple-500/25"
    },
    {
      icon: "☁️",
      title: "Cloud Solutions",
      stat: "Firebase & AWS",
      description: "Scalable cloud architecture",
      gradient: "from-orange-400 to-red-600",
      glow: "shadow-orange-500/25"
    }
  ];

  const projects = [
    {
      title: "MediRemind",
      description: "Healthcare management system with 35% efficiency improvement",
      tech: ["React", "Firebase", "TypeScript"],
      github: "https://github.com/sg782/mediRemind",
      demo: "#",
      gradient: "from-emerald-500 to-teal-600"
    },
    {
      title: "Portfolio Website",
      description: "Modern responsive portfolio with sci-fi animations",
      tech: ["React", "Tailwind", "Framer Motion"],
      github: "#",
      demo: "#",
      gradient: "from-blue-500 to-cyan-600"
    },
    {
      title: "Data Analytics Dashboard",
      description: "Interactive dashboard for business intelligence",
      tech: ["Python", "Pandas", "Plotly"],
      github: "#",
      demo: "#",
      gradient: "from-purple-500 to-pink-600"
    },
    {
      title: "Mobile Task Manager",
      description: "Cross-platform productivity app",
      tech: ["React Native", "Firebase", "Redux"],
      github: "#",
      demo: "#",
      gradient: "from-orange-500 to-red-600"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentRole((prev) => (prev + 1) % roles.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Floating Particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              opacity: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
        
        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.1)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.1)_1px,transparent_1px)] bg-[size:50px_50px]" />
        
        {/* Gradient Orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Mouse Follower */}
      <motion.div
        className="fixed w-6 h-6 bg-cyan-400/30 rounded-full pointer-events-none z-50 mix-blend-difference"
        style={{
          left: mousePosition.x - 12,
          top: mousePosition.y - 12,
        }}
        animate={{
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 0.3,
        }}
      />

      {/* Main Content */}
      <div className="container mx-auto px-6 py-20 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]"
        >
          {/* Left Side - Hero Content */}
          <motion.div variants={itemVariants} className="space-y-8">
            <motion.h1
              className="text-5xl lg:text-7xl font-bold bg-gradient-to-r from-white via-cyan-200 to-blue-400 bg-clip-text text-transparent"
              variants={floatingVariants}
              animate="animate"
            >
              Hi, I'm{" "}
              <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Darshan Adhikari
              </span>
            </motion.h1>

            {/* Animated Role */}
            <div className="h-16 flex items-center lg:justify-start justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentRole}
                  initial={{ opacity: 0, y: 20, rotateX: -90 }}
                  animate={{ opacity: 1, y: 0, rotateX: 0 }}
                  exit={{ opacity: 0, y: -20, rotateX: 90 }}
                  transition={{ duration: 0.5 }}
                  className="text-xl lg:text-2xl font-semibold text-cyan-300 flex items-center gap-3"
                >
                  <Zap className="w-6 h-6 text-yellow-400" />
                  {roles[currentRole]}
                  <Rocket className="w-6 h-6 text-blue-400" />
                </motion.div>
              </AnimatePresence>
            </div>

            <motion.p
              variants={itemVariants}
              className="text-lg lg:text-xl text-blue-100 leading-relaxed"
            >
              Transforming Ideas into Digital Reality 🚀
            </motion.p>

            <motion.p
              variants={itemVariants}
              className="text-base text-blue-200 flex items-center lg:justify-start justify-center gap-2"
            >
              <Music className="w-5 h-5" />
              Building real solutions with a sci-fi playlist in the background
              <Coffee className="w-5 h-5" />
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-4 lg:justify-start justify-center"
            >
              <Link to="/projects">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="group bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-6 py-3 rounded-xl font-bold shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 flex items-center gap-2"
                >
                  <Rocket className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                  View My Work
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </motion.button>
              </Link>

              <Link to="/contact">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="group border-2 border-cyan-400 text-cyan-400 px-6 py-3 rounded-xl font-bold hover:bg-cyan-400 hover:text-slate-900 transition-all duration-300 flex items-center gap-2 backdrop-blur-sm"
                >
                  <Mail className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                  Get in Touch
                </motion.button>
              </Link>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              variants={itemVariants}
              className="flex flex-col gap-3 lg:items-start items-center text-blue-200"
            >
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                <span className="text-sm">Cape Girardeau, MO</span>
              </div>
              <div className="flex items-center gap-2">
                <Github className="w-4 h-4" />
                <span className="text-sm">github.com/DarshanAdh</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Side - Profile & Quick Stats */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Profile Photo Placeholder */}
            <motion.div
              whileHover={{ scale: 1.02, rotateY: 5 }}
              className="relative mx-auto lg:mx-0 w-80 h-80 rounded-3xl bg-gradient-to-br from-cyan-500 to-blue-600 p-1"
            >
              <div className="w-full h-full rounded-3xl bg-slate-900/95 backdrop-blur-sm flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center text-4xl font-bold text-white">
                    DA
                  </div>
                  <p className="text-cyan-300 text-lg font-semibold">Professional Photo</p>
                  <p className="text-blue-400 text-sm mt-1">Coming Soon</p>
                </div>
              </div>

              {/* Glowing Effect */}
              <motion.div
                animate={{
                  boxShadow: [
                    "0 0 30px rgba(6, 182, 212, 0.3)",
                    "0 0 50px rgba(6, 182, 212, 0.5)",
                    "0 0 30px rgba(6, 182, 212, 0.3)"
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute inset-0 rounded-3xl"
              />
            </motion.div>

            {/* Quick Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-emerald-500 to-teal-600 p-1 rounded-xl"
              >
                <div className="bg-slate-900/90 backdrop-blur-sm rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-emerald-400">35%</div>
                  <div className="text-xs text-emerald-200">Healthcare Improvement</div>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-blue-500 to-cyan-600 p-1 rounded-xl"
              >
                <div className="bg-slate-900/90 backdrop-blur-sm rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">4+</div>
                  <div className="text-xs text-blue-200">Projects Completed</div>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-purple-500 to-pink-600 p-1 rounded-xl"
              >
                <div className="bg-slate-900/90 backdrop-blur-sm rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400">10+</div>
                  <div className="text-xs text-purple-200">Technologies</div>
                </div>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="bg-gradient-to-br from-orange-500 to-red-600 p-1 rounded-xl"
              >
                <div className="bg-slate-900/90 backdrop-blur-sm rounded-xl p-4 text-center">
                  <div className="text-2xl font-bold text-orange-400">AWS</div>
                  <div className="text-xs text-orange-200">Cloud Ready</div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* Skills Summary */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="py-20"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl lg:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent"
          >
            Skills & Expertise
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.05,
                  y: -10,
                  rotateY: 5,
                }}
                className={`relative group bg-gradient-to-br ${skill.gradient} p-1 rounded-2xl ${skill.glow} shadow-2xl`}
              >
                <div className="bg-slate-900/90 backdrop-blur-sm rounded-2xl p-6 h-full">
                  <div className="text-4xl mb-4 text-center">{skill.icon}</div>
                  <h3 className="text-xl font-bold text-white mb-2 text-center">{skill.title}</h3>
                  <div className="text-2xl font-bold text-cyan-300 mb-2 text-center">{skill.stat}</div>
                  <p className="text-blue-200 text-sm text-center">{skill.description}</p>

                  {/* Hover Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    initial={false}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="py-20"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl lg:text-5xl font-bold text-center mb-16 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"
          >
            Featured Projects
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.03,
                  y: -5,
                  rotateX: 5,
                }}
                className={`relative group bg-gradient-to-br ${project.gradient} p-1 rounded-2xl shadow-2xl`}
              >
                <div className="bg-slate-900/95 backdrop-blur-sm rounded-2xl p-8 h-full">
                  <h3 className="text-2xl font-bold text-white mb-4">{project.title}</h3>
                  <p className="text-blue-200 mb-6 leading-relaxed">{project.description}</p>

                  {/* Tech Stack */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.tech.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-3 py-1 bg-blue-500/20 text-cyan-300 rounded-full text-sm font-medium border border-blue-500/30"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  {/* Project Links */}
                  <div className="flex gap-4">
                    <motion.a
                      href={project.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-slate-800 text-white px-4 py-2 rounded-xl hover:bg-slate-700 transition-colors"
                    >
                      <Github className="w-4 h-4" />
                      Code
                    </motion.a>

                    <motion.a
                      href={project.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-cyan-500 text-slate-900 px-4 py-2 rounded-xl hover:bg-cyan-400 transition-colors font-medium"
                    >
                      <ExternalLink className="w-4 h-4" />
                      Live Demo
                    </motion.a>
                  </div>

                  {/* Hover Glow Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    initial={false}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="flex flex-col items-center text-cyan-300 mt-16"
        >
          <span className="text-sm mb-2">Explore More</span>
          <ChevronDown className="w-6 h-6" />
        </motion.div>
      </div>

      {/* Tech Stack Footer */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2 }}
        className="fixed bottom-4 right-4 bg-slate-900/80 backdrop-blur-sm rounded-xl p-4 border border-blue-500/30"
      >
        <div className="flex items-center gap-2 text-xs text-blue-300">
          <Terminal className="w-4 h-4" />
          <span>Built with React • TypeScript • Tailwind • Framer Motion</span>
        </div>
      </motion.div>
    </div>
  );
};

export default ModernHomePage;
