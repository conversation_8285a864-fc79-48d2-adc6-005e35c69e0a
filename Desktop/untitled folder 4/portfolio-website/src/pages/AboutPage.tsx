import React from 'react';
import { motion } from 'framer-motion';
import { Code, Database, Smartphone, Brain, Target, Zap, GraduationCap, MapPin, Calendar } from 'lucide-react';

const AboutPage: React.FC = () => {
  const achievements = [
    { number: "35%", label: "Medication Adherence", desc: "Improvement through MediRemind" },
    { number: "4+", label: "Full-Stack Projects", desc: "Production-ready applications" },
    { number: "10+", label: "Technologies", desc: "Modern frameworks & tools" },
    { number: "2024", label: "Expected Graduation", desc: "Computer Science Degree" }
  ];

  const focusAreas = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Full-Stack Development",
      description: "Building end-to-end solutions with React, Node.js, and modern frameworks",
      color: "from-blue-600 to-blue-800"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Data Analysis",
      description: "Transforming complex data into actionable insights using Python and visualization tools",
      color: "from-blue-700 to-blue-900"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile Development",
      description: "Creating cross-platform mobile experiences with React Native and Expo",
      color: "from-blue-800 to-slate-900"
    }
  ];

  const timeline = [
    {
      year: "2024",
      title: "Senior Year & Job Search",
      description: "Completing Computer Science degree while actively seeking full-time opportunities",
      icon: <GraduationCap className="w-6 h-6" />
    },
    {
      year: "2023",
      title: "Healthcare Innovation",
      description: "Developed MediRemind app, improving medication adherence by 35%",
      icon: <Code className="w-6 h-6" />
    },
    {
      year: "2022",
      title: "Full-Stack Focus",
      description: "Mastered React, Node.js, and modern web development practices",
      icon: <Database className="w-6 h-6" />
    },
    {
      year: "2021",
      title: "Computer Science Journey",
      description: "Started CS program at Southeast Missouri State University",
      icon: <Brain className="w-6 h-6" />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            About <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Me</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Passionate about creating technology that makes a real difference in people's lives
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Side - Polaroid Style Card */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            <div className="bg-blue-900/30 backdrop-blur-md p-6 rounded-2xl shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-300 border border-blue-500/20">
              <div className="w-full h-80 bg-gradient-to-br from-blue-800 to-blue-900 rounded-xl mb-4 flex items-center justify-center relative overflow-hidden">
                {/* Coding Session Photo Placeholder */}
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center">
                    <Code className="w-12 h-12 text-white" />
                  </div>
                  <p className="text-blue-200 font-medium">Coding Session Photo</p>
                  <p className="text-blue-300 text-sm">Coming Soon</p>
                </div>
                
                {/* Floating Code Elements */}
                <motion.div
                  animate={{ y: [-5, 5, -5] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute top-4 left-4 text-blue-400/30 font-mono text-xs"
                >
                  {"<Developer />"}
                </motion.div>
                <motion.div
                  animate={{ y: [5, -5, 5] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute bottom-4 right-4 text-cyan-400/30 font-mono text-xs"
                >
                  console.log("Hello World!");
                </motion.div>
              </div>
              <div className="text-center">
                <p className="text-blue-100 font-handwriting text-lg italic">
                  "Building real solutions with a sci-fi playlist in the background."
                </p>
                <p className="text-blue-300 text-sm mt-2">- Darshan's coding philosophy</p>
              </div>
            </div>
          </motion.div>

          {/* Right Side - Career Vision */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-3xl font-bold text-white mb-4 flex items-center gap-3">
                <Brain className="w-8 h-8 text-blue-400" />
                My Journey
              </h3>
              <p className="text-lg text-blue-100 leading-relaxed mb-6">
                As a senior Computer Science student at Southeast Missouri State University, 
                I'm passionate about creating technology that makes a real difference. My journey 
                spans from healthcare innovation to full-stack development, always with a focus 
                on solving meaningful problems.
              </p>
              <p className="text-lg text-blue-100 leading-relaxed">
                When I'm not coding, you'll find me analyzing the latest sci-fi films, 
                curating the perfect coding playlist, or cheering for FC Barcelona. 
                These interests fuel my creativity and bring fresh perspectives to my development work.
              </p>
            </div>

            <div className="bg-blue-900/30 backdrop-blur-md p-6 rounded-xl border border-blue-500/20">
              <h4 className="text-xl font-semibold text-white mb-3 flex items-center gap-2">
                <Target className="w-6 h-6 text-blue-400" />
                Current Focus
              </h4>
              <ul className="space-y-2 text-blue-100">
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-400" />
                  Healthcare technology innovation
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-400" />
                  Cross-platform mobile development
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-400" />
                  Data-driven application development
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-400" />
                  Seeking full-time opportunities in 2024
                </li>
              </ul>
            </div>

            {/* Personal Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-900/30 backdrop-blur-md p-4 rounded-xl border border-blue-500/20">
                <div className="flex items-center gap-2 text-blue-400 mb-2">
                  <MapPin className="w-5 h-5" />
                  <span className="font-semibold">Location</span>
                </div>
                <p className="text-blue-100">Cape Girardeau, MO</p>
              </div>
              <div className="bg-blue-900/30 backdrop-blur-md p-4 rounded-xl border border-blue-500/20">
                <div className="flex items-center gap-2 text-blue-400 mb-2">
                  <Calendar className="w-5 h-5" />
                  <span className="font-semibold">Graduation</span>
                </div>
                <p className="text-blue-100">May 2024</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Achievement Stats */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {achievements.map((achievement, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 text-center shadow-lg border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300"
            >
              <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-2">
                {achievement.number}
              </div>
              <div className="text-white font-semibold mb-1">{achievement.label}</div>
              <div className="text-blue-300 text-sm">{achievement.desc}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-center text-white mb-12">
            My <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Timeline</span>
          </h3>
          <div className="space-y-8">
            {timeline.map((item, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.02, x: 10 }}
                className="flex items-start gap-6 bg-blue-900/30 backdrop-blur-md p-6 rounded-xl border border-blue-500/20"
              >
                <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-3 rounded-full text-white">
                  {item.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-4 mb-2">
                    <span className="text-2xl font-bold text-blue-400">{item.year}</span>
                    <h4 className="text-xl font-semibold text-white">{item.title}</h4>
                  </div>
                  <p className="text-blue-100">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Focus Areas */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-3xl font-bold text-center text-white mb-12">
            What I'm <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Passionate About</span>
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {focusAreas.map((area, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -10 }}
                className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-8 shadow-lg border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300 relative overflow-hidden"
              >
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${area.color}`}></div>
                <div className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${area.color} text-white mb-4`}>
                  {area.icon}
                </div>
                <h4 className="text-xl font-bold text-white mb-3">{area.title}</h4>
                <p className="text-blue-100 leading-relaxed">{area.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AboutPage;
