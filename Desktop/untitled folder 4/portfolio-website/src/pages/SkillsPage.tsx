import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Code, Database, Smartphone, BarChart3, Star, Award, TrendingUp } from 'lucide-react';

const SkillsPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState(0);

  const skillCategories = [
    {
      icon: <Code className="w-6 h-6" />,
      title: "Languages",
      color: "from-blue-600 to-blue-800",
      skills: [
        { name: "Python", level: 90, experience: "3+ years", projects: "Used in 4+ full-stack projects", certification: "Advanced" },
        { name: "JavaScript", level: 85, experience: "2+ years", projects: "Production-ready REST APIs", certification: "Proficient" },
        { name: "TypeScript", level: 80, experience: "1+ years", projects: "Type-safe development", certification: "Intermediate" },
        { name: "Java", level: 85, experience: "2+ years", projects: "OOP, JavaFX, Backend development", certification: "Proficient" }
      ]
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "Frameworks",
      color: "from-blue-700 to-blue-900",
      skills: [
        { name: "React/React Native", level: 90, experience: "2+ years", projects: "Cross-platform mobile & web apps", certification: "Advanced" },
        { name: "Node.js/Express", level: 85, experience: "2+ years", projects: "RESTful APIs, Middleware", certification: "Proficient" },
        { name: "Flask", level: 80, experience: "1+ years", projects: "Python web framework", certification: "Intermediate" },
        { name: "Firebase", level: 85, experience: "2+ years", projects: "Auth, Firestore, Cloud Functions", certification: "Proficient" }
      ]
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Mobile & Web",
      color: "from-blue-800 to-slate-900",
      skills: [
        { name: "React Native", level: 90, experience: "2+ years", projects: "iOS/Android apps", certification: "Advanced" },
        { name: "Expo", level: 85, experience: "2+ years", projects: "Rapid prototyping", certification: "Proficient" },
        { name: "HTML5/CSS3", level: 90, experience: "3+ years", projects: "Semantic markup, Modern CSS", certification: "Advanced" },
        { name: "Responsive Design", level: 85, experience: "2+ years", projects: "Mobile-first approach", certification: "Proficient" }
      ]
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Data & Analytics",
      color: "from-slate-700 to-blue-800",
      skills: [
        { name: "NumPy", level: 80, experience: "2+ years", projects: "Numerical computing", certification: "Intermediate" },
        { name: "Pandas", level: 85, experience: "2+ years", projects: "Data manipulation & analysis", certification: "Proficient" },
        { name: "Matplotlib", level: 80, experience: "2+ years", projects: "Data visualization", certification: "Intermediate" },
        { name: "MySQL", level: 85, experience: "2+ years", projects: "Complex queries, optimization", certification: "Proficient" }
      ]
    }
  ];

  const certificationColors = {
    "Advanced": "bg-green-500/20 text-green-300 border-green-500/30",
    "Proficient": "bg-blue-500/20 text-blue-300 border-blue-500/30",
    "Intermediate": "bg-yellow-500/20 text-yellow-300 border-yellow-500/30"
  };

  const overallStats = [
    { icon: <Code className="w-8 h-8" />, label: "Programming Languages", value: "6+", color: "from-blue-600 to-blue-800" },
    { icon: <Database className="w-8 h-8" />, label: "Frameworks & Libraries", value: "12+", color: "from-blue-700 to-blue-900" },
    { icon: <TrendingUp className="w-8 h-8" />, label: "Years of Experience", value: "3+", color: "from-blue-800 to-slate-900" },
    { icon: <Award className="w-8 h-8" />, label: "Projects Completed", value: "15+", color: "from-slate-700 to-blue-800" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Technical <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Skills</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            A comprehensive toolkit built through hands-on projects and continuous learning
          </p>
        </motion.div>

        {/* Overall Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {overallStats.map((stat, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05, y: -5 }}
              className={`bg-gradient-to-br ${stat.color} bg-opacity-20 backdrop-blur-md rounded-2xl p-6 text-center border border-blue-500/20`}
            >
              <div className="text-blue-300 mb-3 flex justify-center">
                {stat.icon}
              </div>
              <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
              <div className="text-blue-200 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Category Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {skillCategories.map((category, index) => (
            <motion.button
              key={index}
              onClick={() => setActiveCategory(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeCategory === index
                  ? 'bg-gradient-to-r from-blue-600 to-blue-800 text-white shadow-lg border border-blue-400/30'
                  : 'bg-blue-900/30 text-blue-200 hover:bg-blue-800/40 border border-blue-500/20'
              }`}
            >
              {category.icon}
              {category.title}
            </motion.button>
          ))}
        </motion.div>

        {/* Skills Display */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            className="max-w-6xl mx-auto"
          >
            <div className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-8 border border-blue-500/20">
              <div className="grid md:grid-cols-2 gap-8">
                {skillCategories[activeCategory].skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="space-y-4 bg-blue-800/20 p-6 rounded-xl border border-blue-400/20"
                  >
                    <div className="flex justify-between items-center">
                      <h4 className="text-lg font-semibold text-white">{skill.name}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-blue-300 text-sm font-bold">{skill.level}%</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={16}
                              className={`${
                                i < Math.floor(skill.level / 20)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-500'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-blue-900/50 rounded-full h-3">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        className={`h-3 rounded-full bg-gradient-to-r ${skillCategories[activeCategory].color} shadow-lg`}
                      />
                    </div>

                    {/* Skill Details */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-xs bg-blue-600/30 text-blue-200 px-3 py-1 rounded-full border border-blue-500/30">
                          {skill.experience}
                        </span>
                        <span className={`text-xs px-3 py-1 rounded-full border ${certificationColors[skill.certification as keyof typeof certificationColors]}`}>
                          {skill.certification}
                        </span>
                      </div>
                      <p className="text-blue-100 text-sm leading-relaxed">{skill.projects}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Skills Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-8">
            Skill <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Proficiency</span>
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 border border-green-500/30">
              <div className="text-green-400 font-bold text-lg mb-2">Advanced (90%+)</div>
              <div className="text-green-300 text-sm">Production-ready expertise with deep understanding</div>
            </div>
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 border border-blue-500/30">
              <div className="text-blue-400 font-bold text-lg mb-2">Proficient (80-89%)</div>
              <div className="text-blue-300 text-sm">Strong working knowledge with practical experience</div>
            </div>
            <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-6 border border-yellow-500/30">
              <div className="text-yellow-400 font-bold text-lg mb-2">Intermediate (70-79%)</div>
              <div className="text-yellow-300 text-sm">Solid foundation with room for growth</div>
            </div>
          </div>
        </motion.div>

        {/* Learning Philosophy */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="mt-16 bg-blue-900/30 backdrop-blur-md rounded-2xl p-8 border border-blue-500/20 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-4">
            Continuous <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Learning</span>
          </h3>
          <p className="text-blue-100 text-lg leading-relaxed max-w-3xl mx-auto">
            Technology evolves rapidly, and I believe in staying ahead of the curve. I'm constantly exploring new frameworks, 
            best practices, and emerging technologies to deliver cutting-edge solutions. My learning approach combines 
            hands-on projects, online courses, and community engagement to ensure comprehensive skill development.
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default SkillsPage;
