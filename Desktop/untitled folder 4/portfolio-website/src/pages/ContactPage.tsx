import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Github, Linkedin, Send, User, MessageSquare, Coffee, Clock, CheckCircle } from 'lucide-react';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({ name: '', email: '', subject: '', message: '' });
    }, 3000);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email",
      value: "<EMAIL>",
      link: "mailto:<EMAIL>",
      description: "Best way to reach me for professional inquiries",
      color: "from-blue-600 to-blue-800",
      availability: "24/7"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Phone",
      value: "(*************",
      link: "tel:(*************",
      description: "Available for calls and video meetings",
      color: "from-blue-700 to-blue-900",
      availability: "Mon-Fri, 9 AM - 6 PM CST"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Location",
      value: "Cape Girardeau, MO",
      link: null,
      description: "Open to remote work and relocation",
      color: "from-blue-800 to-slate-900",
      availability: "Remote-friendly"
    }
  ];

  const socialLinks = [
    {
      name: "GitHub",
      icon: <Github className="w-6 h-6" />,
      url: "https://github.com/DarshanAdh",
      color: "bg-slate-800 hover:bg-slate-700",
      description: "Check out my code repositories"
    },
    {
      name: "LinkedIn",
      icon: <Linkedin className="w-6 h-6" />,
      url: "https://linkedin.com/in/darshan-adhikari",
      color: "bg-blue-600 hover:bg-blue-700",
      description: "Connect with me professionally"
    },
    {
      name: "Email",
      icon: <Mail className="w-6 h-6" />,
      url: "mailto:<EMAIL>",
      color: "bg-red-600 hover:bg-red-700",
      description: "Send me a direct email"
    }
  ];

  const inquiryTypes = [
    "Full-time Opportunities",
    "Freelance Projects",
    "Collaboration",
    "Technical Discussion",
    "General Inquiry"
  ];

  const responseTime = {
    email: "Within 24 hours",
    phone: "Same day",
    linkedin: "Within 48 hours"
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-20">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Let's Build Something <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Amazing Together</span>
          </h1>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Ready to collaborate on your next project? Whether you're looking for a dedicated developer, 
            a creative problem-solver, or a passionate team member, I'm excited to discuss how we can work together!
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Left Side - Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            {/* Profile Card */}
            <div className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 border border-blue-500/20">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  DA
                </div>
                <div>
                  <h3 className="text-white font-bold text-lg">Darshan Adhikari</h3>
                  <p className="text-blue-300">Full-Stack Developer</p>
                  <p className="text-blue-400 text-sm">Available for new opportunities</p>
                </div>
              </div>
              <p className="text-blue-100 text-sm">
                "Let's turn your ideas into reality with clean code and creative solutions!"
              </p>
            </div>

            {/* Contact Form */}
            <div className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-8 border border-blue-500/20">
              <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <MessageSquare className="w-6 h-6 text-blue-400" />
                Send me a message
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-blue-200 text-sm font-medium mb-2">
                      Your Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-blue-800/20 border border-blue-600/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300"
                      placeholder="John Doe"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-blue-200 text-sm font-medium mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-3 bg-blue-800/20 border border-blue-600/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-blue-200 text-sm font-medium mb-2">
                    Subject *
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-blue-800/20 border border-blue-600/30 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300"
                  >
                    <option value="">Select inquiry type</option>
                    {inquiryTypes.map((type, index) => (
                      <option key={index} value={type} className="bg-blue-900">
                        {type}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-blue-200 text-sm font-medium mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full px-4 py-3 bg-blue-800/20 border border-blue-600/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-300 resize-none"
                    placeholder="Tell me about your project or just say hello!"
                  />
                </div>

                <motion.button
                  type="submit"
                  disabled={isSubmitting || isSubmitted}
                  whileHover={{ scale: isSubmitting || isSubmitted ? 1 : 1.05, y: isSubmitting || isSubmitted ? 0 : -2 }}
                  whileTap={{ scale: isSubmitting || isSubmitted ? 1 : 0.95 }}
                  className={`w-full py-4 px-6 rounded-xl font-semibold shadow-lg transition-all duration-300 flex items-center justify-center gap-2 ${
                    isSubmitted
                      ? 'bg-green-600 text-white'
                      : isSubmitting
                      ? 'bg-blue-600/50 text-blue-200 cursor-not-allowed'
                      : 'bg-gradient-to-r from-blue-600 to-blue-800 text-white hover:shadow-xl'
                  }`}
                >
                  {isSubmitted ? (
                    <>
                      <CheckCircle className="w-5 h-5" />
                      Message Sent!
                    </>
                  ) : isSubmitting ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-5 h-5 border-2 border-current border-t-transparent rounded-full"
                      />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      Send Message
                    </>
                  )}
                </motion.button>
              </form>
            </div>
          </motion.div>

          {/* Right Side - Contact Info */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            {/* Contact Information */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-white mb-6">
                Get in <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Touch</span>
              </h3>
              {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 border border-blue-500/20 hover:border-blue-400/40 transition-all duration-300"
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${info.color} shadow-lg`}>
                      {info.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-semibold text-lg mb-1">{info.title}</h4>
                      {info.link ? (
                        <a
                          href={info.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-300 hover:text-blue-200 transition-colors duration-300 font-medium block mb-2"
                        >
                          {info.value}
                        </a>
                      ) : (
                        <p className="text-blue-300 font-medium mb-2">{info.value}</p>
                      )}
                      <p className="text-blue-100 text-sm mb-2">{info.description}</p>
                      <div className="flex items-center gap-2 text-xs">
                        <Clock className="w-3 h-3 text-blue-400" />
                        <span className="text-blue-400">{info.availability}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Social Links */}
            <div className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 border border-blue-500/20">
              <h4 className="text-white font-semibold text-lg mb-4 flex items-center gap-2">
                <User className="w-5 h-5 text-blue-400" />
                Connect with me
              </h4>
              <div className="space-y-4">
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.02, x: 5 }}
                    className={`${social.color} p-4 rounded-xl text-white shadow-lg transition-all duration-300 flex items-center gap-4`}
                  >
                    {social.icon}
                    <div>
                      <div className="font-semibold">{social.name}</div>
                      <div className="text-sm opacity-80">{social.description}</div>
                    </div>
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-blue-900/30 backdrop-blur-md rounded-2xl p-6 border border-blue-500/20">
              <h4 className="text-white font-semibold text-lg mb-4 flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-400" />
                Response Time
              </h4>
              <div className="space-y-3">
                {Object.entries(responseTime).map(([method, time], index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-blue-200 capitalize">{method}</span>
                    <span className="text-blue-400 font-medium">{time}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-blue-600/20 to-blue-800/20 backdrop-blur-md rounded-2xl p-6 border border-blue-400/30">
              <h4 className="text-white font-semibold text-lg mb-3 flex items-center gap-2">
                <Coffee className="w-5 h-5 text-yellow-400" />
                Let's grab a virtual coffee!
              </h4>
              <p className="text-blue-100 text-sm mb-4">
                I'm always excited to discuss new opportunities, collaborate on interesting projects, 
                or just chat about technology and innovation.
              </p>
              <div className="flex flex-wrap gap-3">
                <span className="bg-blue-600/30 text-blue-200 px-3 py-1 rounded-full text-sm">
                  🚀 Full-time opportunities
                </span>
                <span className="bg-blue-600/30 text-blue-200 px-3 py-1 rounded-full text-sm">
                  💼 Freelance projects
                </span>
                <span className="bg-blue-600/30 text-blue-200 px-3 py-1 rounded-full text-sm">
                  🤝 Collaboration
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
