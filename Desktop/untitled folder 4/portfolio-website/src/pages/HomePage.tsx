import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronDown, Github, Mail, MapPin, Download, ArrowRight, Code, Database, Smartphone } from 'lucide-react';

const HomePage: React.FC = () => {
  const [currentRole, setCurrentRole] = useState(0);
  const roles = [
    "Senior CS Student",
    "Full-Stack Developer", 
    "Data Analyst",
    "Problem Solver"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentRole((prev) => (prev + 1) % roles.length);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6 }
    }
  };

  const achievements = [
    { 
      icon: <Code className="w-8 h-8" />, 
      label: "Healthcare Tech", 
      desc: "35% improvement",
      gradient: "from-blue-500 to-blue-700"
    },
    { 
      icon: <Database className="w-8 h-8" />, 
      label: "Web Applications", 
      desc: "4+ projects",
      gradient: "from-blue-600 to-blue-800"
    },
    { 
      icon: <Smartphone className="w-8 h-8" />, 
      label: "Mobile Apps", 
      desc: "Cross-platform",
      gradient: "from-blue-700 to-blue-900"
    },
    { 
      icon: "☁️", 
      label: "Cloud Solutions", 
      desc: "Firebase & AWS",
      gradient: "from-blue-800 to-slate-900"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        {/* Floating Code Snippets */}
        <motion.div
          animate={{ 
            y: [-20, -40, -20],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{ duration: 4, repeat: Infinity }}
          className="absolute top-20 left-10 text-blue-400/30 font-mono text-sm"
        >
          const developer = "Darshan";
        </motion.div>
        <motion.div
          animate={{ 
            y: [-30, -50, -30],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{ duration: 5, repeat: Infinity, delay: 1 }}
          className="absolute top-40 right-20 text-cyan-400/30 font-mono text-sm"
        >
          function buildFuture() {"{"}
        </motion.div>
        <motion.div
          animate={{ 
            y: [-25, -45, -25],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{ duration: 3.5, repeat: Infinity, delay: 2 }}
          className="absolute bottom-40 left-20 text-blue-300/30 font-mono text-sm"
        >
          return innovation;
        </motion.div>

        {/* Geometric Shapes */}
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="absolute top-1/4 right-1/4 w-32 h-32 border border-blue-400/20 rounded-lg"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-1/4 left-1/4 w-24 h-24 border border-cyan-400/20 rounded-full"
        />
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid lg:grid-cols-2 gap-12 items-center min-h-screen"
        >
          {/* Left Side - Profile Photo */}
          <motion.div variants={itemVariants} className="text-center lg:text-left">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="relative inline-block mb-8"
            >
              {/* Profile Photo Placeholder */}
              <div className="w-80 h-80 mx-auto lg:mx-0 rounded-2xl bg-gradient-to-br from-blue-600 to-blue-800 p-1">
                <div className="w-full h-full rounded-2xl bg-slate-800 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-blue-700 flex items-center justify-center text-4xl font-bold text-white">
                      DA
                    </div>
                    <p className="text-blue-300 text-sm">Professional Photo</p>
                    <p className="text-blue-400 text-xs mt-1">Coming Soon</p>
                  </div>
                </div>
              </div>
              
              {/* Glowing Effect */}
              <motion.div
                animate={{ 
                  boxShadow: [
                    "0 0 20px rgba(59, 130, 246, 0.3)",
                    "0 0 40px rgba(59, 130, 246, 0.5)",
                    "0 0 20px rgba(59, 130, 246, 0.3)"
                  ]
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 rounded-2xl"
              />
            </motion.div>

            {/* Quick Stats */}
            <motion.div variants={itemVariants} className="grid grid-cols-2 gap-4 max-w-sm mx-auto lg:mx-0">
              <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-4 text-center border border-blue-500/20">
                <div className="text-2xl font-bold text-blue-400">4+</div>
                <div className="text-sm text-blue-200">Projects</div>
              </div>
              <div className="bg-blue-900/30 backdrop-blur-md rounded-xl p-4 text-center border border-blue-500/20">
                <div className="text-2xl font-bold text-blue-400">10+</div>
                <div className="text-sm text-blue-200">Technologies</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Side - Text Content */}
          <motion.div variants={itemVariants} className="text-center lg:text-left">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6"
            >
              <h1 className="text-5xl lg:text-7xl font-bold text-white mb-4">
                Hi, I'm{' '}
                <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  Darshan Adhikari
                </span>
              </h1>
              
              {/* Animated Role */}
              <div className="flex items-center justify-center lg:justify-start gap-2 mb-6">
                <span className="text-xl text-blue-200">🎓</span>
                <motion.span
                  key={currentRole}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="text-xl lg:text-2xl font-semibold text-blue-300"
                >
                  {roles[currentRole]}
                </motion.span>
              </div>
            </motion.div>

            <motion.p
              variants={itemVariants}
              className="text-lg lg:text-xl text-blue-100 mb-8 leading-relaxed max-w-2xl"
            >
              Transforming Ideas into Digital Reality 🚀
              <br />
              <span className="text-blue-300">
                Building real solutions with a sci-fi playlist in the background.
              </span>
            </motion.p>

            {/* Action Buttons */}
            <motion.div
              variants={itemVariants}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12"
            >
              <Link to="/projects">
                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-blue-600 to-blue-800 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
                >
                  🎯 View My Work
                  <ArrowRight size={20} />
                </motion.div>
              </Link>
              <Link to="/contact">
                <motion.div
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-blue-500 text-blue-400 px-8 py-4 rounded-xl font-semibold hover:bg-blue-500 hover:text-white transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Mail size={20} />
                  Get in Touch
                </motion.div>
              </Link>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              variants={itemVariants}
              className="flex flex-wrap justify-center lg:justify-start gap-6 text-blue-200"
            >
              <div className="flex items-center gap-2">
                <Mail size={16} />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin size={16} />
                <span className="text-sm">Cape Girardeau, MO</span>
              </div>
              <div className="flex items-center gap-2">
                <Github size={16} />
                <span className="text-sm">github.com/DarshanAdh</span>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Achievement Cards */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-20"
        >
          {achievements.map((achievement, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.08, y: -8 }}
              className={`bg-gradient-to-br ${achievement.gradient} bg-opacity-20 rounded-2xl p-6 text-center border border-blue-500/20 backdrop-blur-md`}
            >
              <motion.div 
                className="text-3xl mb-3 flex justify-center text-blue-300"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                {typeof achievement.icon === 'string' ? achievement.icon : achievement.icon}
              </motion.div>
              <div className="text-white font-bold text-sm mb-1">{achievement.label}</div>
              <div className="text-blue-200 text-xs">{achievement.desc}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <Link to="/about">
          <motion.div
            whileHover={{ scale: 1.1 }}
            className="flex flex-col items-center text-blue-300 hover:text-blue-200 transition-colors"
          >
            <span className="text-sm mb-2">Explore more</span>
            <ChevronDown size={24} />
          </motion.div>
        </Link>
      </motion.div>
    </div>
  );
};

export default HomePage;
