import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Code, Database, Smartphone, BarChart3, Star } from 'lucide-react';

const ModernSkills: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState(0);

  const skillCategories = [
    {
      icon: <Code className="w-6 h-6" />,
      title: "Languages",
      color: "from-blue-500 to-cyan-500",
      skills: [
        { name: "Python", level: 90, experience: "3+ years", projects: "Used in 4+ full-stack projects" },
        { name: "JavaScript", level: 85, experience: "2+ years", projects: "Advanced: Built production-ready REST APIs" },
        { name: "TypeScript", level: 80, experience: "1+ years", projects: "Type-safe development" },
        { name: "Java", level: 85, experience: "2+ years", projects: "OOP, JavaFX, Backend development" }
      ]
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "Frameworks",
      color: "from-cyan-500 to-blue-500",
      skills: [
        { name: "React/React Native", level: 90, experience: "2+ years", projects: "Cross-platform mobile & web apps" },
        { name: "Node.js/Express", level: 85, experience: "2+ years", projects: "RESTful APIs, Middleware" },
        { name: "Flask", level: 80, experience: "1+ years", projects: "Python web framework" },
        { name: "Firebase", level: 85, experience: "2+ years", projects: "Auth, Firestore, Cloud Functions" }
      ]
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Mobile & Web",
      color: "from-blue-600 to-slate-600",
      skills: [
        { name: "React Native", level: 90, experience: "2+ years", projects: "iOS/Android apps" },
        { name: "Expo", level: 85, experience: "2+ years", projects: "Rapid prototyping" },
        { name: "HTML5/CSS3", level: 90, experience: "3+ years", projects: "Semantic markup, Modern CSS" },
        { name: "Responsive Design", level: 85, experience: "2+ years", projects: "Mobile-first approach" }
      ]
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Data & Analytics",
      color: "from-slate-500 to-blue-500",
      skills: [
        { name: "NumPy", level: 80, experience: "2+ years", projects: "Numerical computing" },
        { name: "Pandas", level: 85, experience: "2+ years", projects: "Data manipulation & analysis" },
        { name: "Matplotlib", level: 80, experience: "2+ years", projects: "Data visualization" },
        { name: "MySQL", level: 85, experience: "2+ years", projects: "Complex queries, optimization" }
      ]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
          className="absolute top-20 right-20 w-32 h-32 border border-blue-400/10 rounded-lg"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 40, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-20 left-20 w-24 h-24 border border-cyan-400/10 rounded-full"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
            Technical <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">Skills</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-400 to-cyan-400 mx-auto mb-8"></div>
          <p className="text-xl text-blue-100 max-w-2xl mx-auto">
            A comprehensive toolkit built through hands-on projects and continuous learning
          </p>
        </motion.div>

        {/* Category Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {skillCategories.map((category, index) => (
            <motion.button
              key={index}
              onClick={() => setActiveCategory(index)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeCategory === index
                  ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg'
                  : 'bg-white/10 text-blue-200 hover:bg-white/20'
              }`}
            >
              {category.icon}
              {category.title}
            </motion.button>
          ))}
        </motion.div>

        {/* Skills Display */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
            className="max-w-4xl mx-auto"
          >
            <div className="bg-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/10">
              <div className="grid md:grid-cols-2 gap-8">
                {skillCategories[activeCategory].skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="space-y-4"
                  >
                    <div className="flex justify-between items-center">
                      <h4 className="text-lg font-semibold text-white">{skill.name}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-blue-300 text-sm">{skill.level}%</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={16}
                              className={`${
                                i < Math.floor(skill.level / 20)
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-400'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${skill.level}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        className={`h-2 rounded-full bg-gradient-to-r ${skillCategories[activeCategory].color}`}
                      />
                    </div>

                    {/* Skill Details */}
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                          {skill.experience}
                        </span>
                      </div>
                      <p className="text-blue-100 text-sm">{skill.projects}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Skills Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-blue-400/20">
              <div className="text-2xl font-bold text-blue-400 mb-2">15+</div>
              <div className="text-blue-200 text-sm">Technologies</div>
            </div>
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-cyan-400/20">
              <div className="text-2xl font-bold text-cyan-400 mb-2">4+</div>
              <div className="text-cyan-200 text-sm">Major Projects</div>
            </div>
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-blue-400/20">
              <div className="text-2xl font-bold text-blue-400 mb-2">3+</div>
              <div className="text-blue-200 text-sm">Years Experience</div>
            </div>
            <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-cyan-400/20">
              <div className="text-2xl font-bold text-cyan-400 mb-2">100%</div>
              <div className="text-cyan-200 text-sm">Passion Driven</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernSkills;
