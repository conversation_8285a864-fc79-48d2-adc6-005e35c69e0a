import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Film, Music, Trophy, X, Play, Heart, Star } from 'lucide-react';

const ModernHobbies: React.FC = () => {
  const [selectedHobby, setSelectedHobby] = useState<number | null>(null);

  const hobbies = [
    {
      id: 1,
      icon: <Film className="w-8 h-8" />,
      title: "Movies & Cinema",
      subtitle: "Sci-Fi Enthusiast",
      description: "Passionate about storytelling through film, especially sci-fi that inspires my coding creativity.",
      gradient: "from-red-500 to-pink-600",
      stats: [
        { label: "Movies Watched", value: "200+" },
        { label: "Favorite Genre", value: "Sci-Fi" },
        { label: "Cinema Visits", value: "Monthly" }
      ],
      favorites: ["Inception", "The Matrix", "Interstellar", "Blade Runner 2049", "Dune"],
      quote: "There is no spoon - My debugging philosophy",
      connection: "Understanding narrative flow helps me design intuitive user journeys and create engaging interfaces.",
      image: "/api/placeholder/300/200"
    },
    {
      id: 2,
      icon: <Music className="w-8 h-8" />,
      title: "Music & Audio",
      subtitle: "Playlist Curator",
      description: "Music fuels my creativity and focus during coding sessions. I curate the perfect soundtracks for development.",
      gradient: "from-purple-500 to-indigo-600",
      stats: [
        { label: "Hours Daily", value: "6+" },
        { label: "Playlists Created", value: "15+" },
        { label: "Coding Soundtrack", value: "Lo-fi" }
      ],
      favorites: ["Nujabes", "Emancipator", "Tycho", "Boards of Canada"],
      quote: "Lo-fi beats = bug-free code",
      connection: "Musical patterns and rhythms enhance my ability to recognize code patterns and write clean, structured code.",
      image: "/api/placeholder/300/200"
    },
    {
      id: 3,
      icon: <Trophy className="w-8 h-8" />,
      title: "Soccer & Sports",
      subtitle: "FC Barcelona Fan",
      description: "Team sports teach collaboration and strategy. Proud supporter of FC Barcelona for over 8 years.",
      gradient: "from-blue-500 to-cyan-500",
      stats: [
        { label: "Years Playing", value: "10+" },
        { label: "Favorite Team", value: "FC Barcelona" },
        { label: "Games per Month", value: "4-6" }
      ],
      favorites: ["Lionel Messi", "Xavi", "Iniesta", "Pedri"],
      quote: "Més que un club! 🔵🔴",
      connection: "Team sports teach communication, strategy, and working towards common goals - essential for agile development.",
      image: "/api/placeholder/300/200"
    }
  ];

  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Beyond the <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">Code</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            When I'm not coding, you'll find me exploring these passions that fuel my creativity and enhance my problem-solving skills
          </p>
        </motion.div>

        {/* Hobbies Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {hobbies.map((hobby, index) => (
            <motion.div
              key={hobby.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              onClick={() => setSelectedHobby(hobby.id)}
              className="bg-white rounded-2xl shadow-xl overflow-hidden cursor-pointer hover:shadow-2xl transition-all duration-300 group"
            >
              {/* Card Header */}
              <div className={`h-48 bg-gradient-to-br ${hobby.gradient} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                      className="w-16 h-16 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center"
                    >
                      {hobby.icon}
                    </motion.div>
                    <p className="text-sm opacity-80">Hobby Photo</p>
                    <p className="text-xs opacity-60">Click to explore</p>
                  </div>
                </div>
                
                {/* Hover Overlay */}
                <motion.div
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  className="absolute inset-0 bg-black/40 flex items-center justify-center"
                >
                  <Play className="w-12 h-12 text-white" />
                </motion.div>
              </div>

              {/* Card Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-800 mb-2">{hobby.title}</h3>
                <p className="text-blue-600 font-medium mb-3">{hobby.subtitle}</p>
                <p className="text-slate-600 text-sm leading-relaxed mb-4">{hobby.description}</p>
                
                {/* Quick Stats */}
                <div className="grid grid-cols-1 gap-2">
                  {hobby.stats.slice(0, 2).map((stat, statIndex) => (
                    <div key={statIndex} className="flex justify-between items-center text-sm">
                      <span className="text-slate-500">{stat.label}</span>
                      <span className="font-semibold text-slate-700">{stat.value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Fun Facts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-2xl p-8"
        >
          <h3 className="text-2xl font-bold text-center text-slate-800 mb-8">
            🎉 Fun Facts About Me
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            {hobbies.map((hobby, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white p-6 rounded-xl shadow-lg text-center"
              >
                <div className="text-3xl mb-3">{hobby.icon}</div>
                <h4 className="font-semibold text-slate-800 mb-2">{hobby.quote}</h4>
                <p className="text-slate-600 text-sm">{hobby.connection}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Modal for Hobby Details */}
      <AnimatePresence>
        {selectedHobby && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedHobby(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              {(() => {
                const hobby = hobbies.find(h => h.id === selectedHobby);
                if (!hobby) return null;

                return (
                  <>
                    {/* Modal Header */}
                    <div className={`bg-gradient-to-br ${hobby.gradient} p-6 text-white relative`}>
                      <button
                        onClick={() => setSelectedHobby(null)}
                        className="absolute top-4 right-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                          {hobby.icon}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold">{hobby.title}</h3>
                          <p className="text-white/80">{hobby.subtitle}</p>
                        </div>
                      </div>
                    </div>

                    {/* Modal Content */}
                    <div className="p-6 space-y-6">
                      <p className="text-slate-600 leading-relaxed">{hobby.description}</p>

                      {/* Favorites */}
                      <div>
                        <h4 className="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                          <Heart className="w-5 h-5 text-red-500" />
                          Favorites
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {hobby.favorites.map((item, index) => (
                            <span
                              key={index}
                              className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                            >
                              {item}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Stats */}
                      <div>
                        <h4 className="font-semibold text-slate-800 mb-3 flex items-center gap-2">
                          <Star className="w-5 h-5 text-yellow-500" />
                          Stats
                        </h4>
                        <div className="grid grid-cols-3 gap-4">
                          {hobby.stats.map((stat, index) => (
                            <div key={index} className="text-center p-3 bg-slate-50 rounded-lg">
                              <div className="text-lg font-bold text-blue-600">{stat.value}</div>
                              <div className="text-xs text-slate-500">{stat.label}</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Connection to Development */}
                      <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <h4 className="font-semibold text-slate-800 mb-2">Connection to Development</h4>
                        <p className="text-slate-600 text-sm italic">{hobby.connection}</p>
                      </div>
                    </div>
                  </>
                );
              })()}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default ModernHobbies;
