import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Code, Database, Smartphone, Shield, TestTube, BarChart3 } from 'lucide-react'

const Skills = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const skillCategories = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Programming Languages",
      skills: [
        { name: "Python", level: 90 },
        { name: "JavaScript", level: 85 },
        { name: "TypeScript", level: 80 },
        { name: "Java", level: 85 },
        { name: "C/C++", level: 75 },
        { name: "C#", level: 70 }
      ]
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Frameworks & Tools",
      skills: [
        { name: "React/React Native", level: 90 },
        { name: "Node.js/Express", level: 85 },
        { name: "<PERSON>lask", level: 80 },
        { name: "Firebase", level: 85 },
        { name: "MongoDB", level: 80 },
        { name: "MySQL", level: 85 }
      ]
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile & Web",
      skills: [
        { name: "React Native", level: 90 },
        { name: "Expo", level: 85 },
        { name: "HTML/CSS", level: 90 },
        { name: "Responsive Design", level: 85 },
        { name: "RESTful APIs", level: 85 },
        { name: "JavaFX", level: 75 }
      ]
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Security & Auth",
      skills: [
        { name: "JWT Authentication", level: 85 },
        { name: "Firebase Auth", level: 80 },
        { name: "Bcrypt Hashing", level: 80 },
        { name: "Role-Based Access", level: 85 },
        { name: "Security Rules", level: 75 }
      ]
    },
    {
      icon: <TestTube className="w-8 h-8" />,
      title: "Testing & Quality",
      skills: [
        { name: "White Box Testing", level: 80 },
        { name: "Test Documentation", level: 85 },
        { name: "Git/GitHub", level: 90 },
        { name: "Agile Methodology", level: 80 },
        { name: "Code Reviews", level: 85 }
      ]
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Data Analysis",
      skills: [
        { name: "NumPy", level: 80 },
        { name: "Pandas", level: 85 },
        { name: "Matplotlib", level: 80 },
        { name: "Seaborn", level: 75 },
        { name: "Data Visualization", level: 80 }
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="skills" className="py-20 gradient-bg-3" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Skills
            </h2>
            <div className="w-24 h-1 bg-white/30 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              A comprehensive toolkit of modern technologies and frameworks
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={itemVariants}
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20"
              >
                <div className="flex items-center mb-6">
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                    className="bg-white/20 p-3 rounded-full mr-4"
                  >
                    {category.icon}
                  </motion.div>
                  <h3 className="text-xl font-semibold text-white">
                    {category.title}
                  </h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <div key={skillIndex} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-white/90 font-medium">
                          {skill.name}
                        </span>
                        <span className="text-white/70 text-sm">
                          {skill.level}%
                        </span>
                      </div>
                      <div className="w-full bg-white/20 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                          transition={{ 
                            duration: 1.5, 
                            delay: categoryIndex * 0.1 + skillIndex * 0.1,
                            ease: "easeOut"
                          }}
                          className="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>

          {/* Additional technologies */}
          <motion.div variants={itemVariants} className="mt-16 text-center">
            <h3 className="text-2xl font-semibold text-white mb-8">
              Additional Technologies & Tools
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              {[
                "Netlify", "GitHub", "Leaflet", "Mongoose", "Cloud Functions",
                "Cloud Messaging", "QR Code Scanning", "Geospatial Queries",
                "MVC Architecture", "Repository Pattern", "Context API"
              ].map((tech, index) => (
                <motion.span
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.1 }}
                  className="bg-white/20 backdrop-blur-md px-4 py-2 rounded-full text-white/90 border border-white/30"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Skills
