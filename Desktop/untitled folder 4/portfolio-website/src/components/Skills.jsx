import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Code, Database, Smartphone, Shield, TestTube, BarChart3 } from 'lucide-react'

const Skills = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const skillCategories = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Programming Languages",
      color: "from-blue-500 to-cyan-500",
      skills: [
        { name: "Python", level: 90, description: "Data Analysis, Flask, Automation", years: "3+" },
        { name: "JavaScript", level: 85, description: "ES6+, React, Node.js", years: "2+" },
        { name: "TypeScript", level: 80, description: "Type-safe development", years: "1+" },
        { name: "Java", level: 85, description: "OOP, JavaFX, Backend", years: "2+" },
        { name: "C/C++", level: 75, description: "System Programming, DSA", years: "2+" },
        { name: "C#", level: 70, description: ".NET, Desktop Apps", years: "1+" }
      ]
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Frameworks & Backend",
      color: "from-green-500 to-emerald-500",
      skills: [
        { name: "React/React Native", level: 90, description: "Cross-platform development", years: "2+" },
        { name: "Node.js/Express", level: 85, description: "RESTful APIs, Middleware", years: "2+" },
        { name: "Flask", level: 80, description: "Python web framework", years: "1+" },
        { name: "Firebase", level: 85, description: "Auth, Firestore, Functions", years: "2+" },
        { name: "MongoDB", level: 80, description: "NoSQL, Mongoose ODM", years: "1+" },
        { name: "MySQL", level: 85, description: "Relational DB, Complex queries", years: "2+" }
      ]
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile & Frontend",
      color: "from-purple-500 to-pink-500",
      skills: [
        { name: "React Native", level: 90, description: "iOS/Android apps", years: "2+" },
        { name: "Expo", level: 85, description: "Rapid prototyping", years: "2+" },
        { name: "HTML5/CSS3", level: 90, description: "Semantic markup, Flexbox/Grid", years: "3+" },
        { name: "Responsive Design", level: 85, description: "Mobile-first approach", years: "2+" },
        { name: "RESTful APIs", level: 85, description: "Integration & consumption", years: "2+" },
        { name: "JavaFX", level: 75, description: "Desktop GUI applications", years: "1+" }
      ]
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Security & Authentication",
      color: "from-red-500 to-orange-500",
      skills: [
        { name: "JWT Authentication", level: 85, description: "Secure token-based auth", years: "2+" },
        { name: "Firebase Auth", level: 80, description: "Social login, user management", years: "2+" },
        { name: "Bcrypt Hashing", level: 80, description: "Password security", years: "1+" },
        { name: "Role-Based Access", level: 85, description: "Permission systems", years: "2+" },
        { name: "Security Rules", level: 75, description: "Database security", years: "1+" }
      ]
    },
    {
      icon: <TestTube className="w-8 h-8" />,
      title: "Development & Collaboration",
      color: "from-indigo-500 to-blue-500",
      skills: [
        { name: "Git/GitHub", level: 90, description: "Version control, branching", years: "3+" },
        { name: "White Box Testing", level: 80, description: "Code coverage, unit tests", years: "1+" },
        { name: "Agile Methodology", level: 80, description: "Scrum, sprint planning", years: "2+" },
        { name: "Code Reviews", level: 85, description: "Peer review, best practices", years: "2+" },
        { name: "Documentation", level: 85, description: "Technical writing, APIs", years: "2+" }
      ]
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Data Science & Analytics",
      color: "from-yellow-500 to-orange-500",
      skills: [
        { name: "NumPy", level: 80, description: "Numerical computing", years: "2+" },
        { name: "Pandas", level: 85, description: "Data manipulation & analysis", years: "2+" },
        { name: "Matplotlib", level: 80, description: "Static visualizations", years: "2+" },
        { name: "Seaborn", level: 75, description: "Statistical data visualization", years: "1+" },
        { name: "Data Visualization", level: 80, description: "Interactive dashboards", years: "2+" }
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="skills" className="py-20 gradient-bg-3" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Technical Skills
            </h2>
            <div className="w-24 h-1 bg-white/30 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              A comprehensive toolkit of modern technologies and frameworks
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={categoryIndex}
                variants={itemVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 overflow-hidden relative"
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-5`}></div>

                <div className="relative z-10">
                  <div className="flex items-center mb-6">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.5 }}
                      className={`bg-gradient-to-r ${category.color} p-3 rounded-full mr-4 shadow-lg`}
                    >
                      {category.icon}
                    </motion.div>
                    <h3 className="text-xl font-semibold text-white">
                      {category.title}
                    </h3>
                  </div>

                  <div className="space-y-5">
                    {category.skills.map((skill, skillIndex) => (
                      <motion.div
                        key={skillIndex}
                        className="space-y-2"
                        whileHover={{ x: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-white/90 font-medium">
                                {skill.name}
                              </span>
                              <span className="text-xs bg-white/20 text-white/80 px-2 py-1 rounded-full">
                                {skill.years}
                              </span>
                            </div>
                            <p className="text-white/60 text-sm">
                              {skill.description}
                            </p>
                          </div>
                          <span className="text-white/70 text-sm font-semibold ml-4">
                            {skill.level}%
                          </span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden">
                          <motion.div
                            initial={{ width: 0 }}
                            animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                            transition={{
                              duration: 1.5,
                              delay: categoryIndex * 0.1 + skillIndex * 0.1,
                              ease: "easeOut"
                            }}
                            className={`bg-gradient-to-r ${category.color} h-2 rounded-full shadow-sm`}
                          />
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Additional technologies */}
          <motion.div variants={itemVariants} className="mt-16 text-center">
            <h3 className="text-2xl font-semibold text-white mb-8">
              Additional Technologies & Tools
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              {[
                "Netlify", "GitHub", "Leaflet", "Mongoose", "Cloud Functions",
                "Cloud Messaging", "QR Code Scanning", "Geospatial Queries",
                "MVC Architecture", "Repository Pattern", "Context API"
              ].map((tech, index) => (
                <motion.span
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.1 }}
                  className="bg-white/20 backdrop-blur-md px-4 py-2 rounded-full text-white/90 border border-white/30"
                >
                  {tech}
                </motion.span>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Skills
