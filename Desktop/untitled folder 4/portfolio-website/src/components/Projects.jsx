import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { ExternalLink, Github, Smartphone, Globe, Database, Gamepad2 } from 'lucide-react'

const Projects = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const projects = [
    {
      title: "MediRemind - Medication Management App",
      description: "A comprehensive cross-platform mobile application for managing medication schedules with real-time notifications and role-based dashboards.",
      icon: <Smartphone className="w-8 h-8" />,
      technologies: ["React Native", "Expo", "Firebase", "Cloud Functions", "JavaScript"],
      achievements: [
        "35% improvement in medication adherence",
        "40% reduction in admin time for providers",
        "Scalable architecture supporting thousands of users",
        "QR code scanning for prescription management"
      ],
      features: [
        "Cross-platform mobile app using React Native and Expo",
        "Firebase backend with authentication and Firestore database",
        "Automated medication reminders via Cloud Functions",
        "Role-based dashboards for doctors, patients, and caregivers",
        "Secure authentication with custom Firebase rules"
      ],
      gradient: "from-blue-500 to-purple-600"
    },
    {
      title: "Roadside Assistance Web Application",
      description: "Full-stack web application providing real-time location-based roadside assistance services with comprehensive user management.",
      icon: <Globe className="w-8 h-8" />,
      technologies: ["React", "TypeScript", "Node.js", "MongoDB", "Leaflet", "JWT"],
      achievements: [
        "Real-time location-based services",
        "Comprehensive white box testing coverage",
        "Clean MVC architecture implementation",
        "Successful team collaboration via GitHub"
      ],
      features: [
        "Secure JWT authentication with role-based access",
        "MongoDB geospatial indexing with Leaflet integration",
        "Serverless deployment on Netlify",
        "MVC, Repository, and Middleware patterns",
        "Comprehensive API documentation"
      ],
      gradient: "from-green-500 to-teal-600"
    },
    {
      title: "Restaurant Orders & Rewards Management",
      description: "Dynamic web application for managing restaurant operations with integrated rewards system and real-time order tracking.",
      icon: <Database className="w-8 h-8" />,
      technologies: ["Python", "Flask", "MySQL", "JavaScript", "HTML", "CSS"],
      achievements: [
        "Dynamic order management system",
        "Real-time rewards calculation",
        "Complex SQL queries and triggers",
        "Responsive user interface design"
      ],
      features: [
        "Backend API using Flask for data management",
        "MySQL database with persistent storage",
        "Responsive front-end with HTML, CSS, and JavaScript",
        "Complex SQL queries for rewards management",
        "Real-time order history and rewards tracking"
      ],
      gradient: "from-orange-500 to-red-600"
    },
    {
      title: "JavaFX Ball Game Application",
      description: "Interactive ball game with rich graphical interface, featuring collision detection and progressive difficulty levels.",
      icon: <Gamepad2 className="w-8 h-8" />,
      technologies: ["Java", "JavaFX"],
      achievements: [
        "Smooth gameplay experience",
        "Object-oriented design principles",
        "Modular and maintainable code",
        "Engaging visual experience"
      ],
      features: [
        "Rich graphical user interface using JavaFX",
        "Collision detection and score tracking",
        "Increasing difficulty levels",
        "Object-oriented architecture for easy enhancements",
        "Responsive and engaging gameplay"
      ],
      gradient: "from-purple-500 to-pink-600"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="projects" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
              Featured Projects
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Showcasing innovative solutions and technical expertise across various domains
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ 
                  scale: 1.02,
                  boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
                }}
                className="card overflow-hidden"
              >
                {/* Project header */}
                <div className={`bg-gradient-to-r ${project.gradient} p-6 text-white`}>
                  <div className="flex items-center mb-4">
                    <motion.div
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                      className="bg-white/20 p-3 rounded-full mr-4"
                    >
                      {project.icon}
                    </motion.div>
                    <h3 className="text-xl font-bold">{project.title}</h3>
                  </div>
                  <p className="text-white/90 leading-relaxed">
                    {project.description}
                  </p>
                </div>

                {/* Project content */}
                <div className="p-6">
                  {/* Technologies */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Technologies Used:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Key Features */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {project.features.slice(0, 3).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="text-gray-600 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Achievements */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-800 mb-3">Key Achievements:</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {project.achievements.map((achievement, achIndex) => (
                        <div key={achIndex} className="bg-green-50 text-green-700 px-3 py-2 rounded-lg text-sm">
                          {achievement}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-300"
                    >
                      <Github size={16} />
                      View Code
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300"
                    >
                      <ExternalLink size={16} />
                      Live Demo
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
