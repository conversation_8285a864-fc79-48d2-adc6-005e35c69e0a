import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { ExternalLink, Github, Smartphone, Globe, Database, Gamepad2 } from 'lucide-react'

const Projects = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const projects = [
    {
      title: "MediRemind - Healthcare Innovation Platform",
      subtitle: "Revolutionizing Medication Adherence Through Technology",
      description: "A comprehensive cross-platform mobile application that transforms medication management for patients, caregivers, and healthcare providers through intelligent automation and real-time monitoring.",
      icon: <Smartphone className="w-8 h-8" />,
      category: "Healthcare Technology",
      duration: "6 months",
      teamSize: "Solo Developer",
      status: "Production Ready",
      technologies: {
        frontend: ["React Native", "Expo", "JavaScript ES6+", "React Navigation"],
        backend: ["Firebase", "Cloud Functions", "Firestore", "Firebase Auth"],
        tools: ["Git", "VS Code", "Firebase Console", "Expo CLI"],
        apis: ["Firebase Cloud Messaging", "QR Code Scanner", "Push Notifications"]
      },
      achievements: [
        { metric: "35%", description: "Improvement in medication adherence rates" },
        { metric: "40%", description: "Reduction in administrative time for providers" },
        { metric: "1000+", description: "Potential users supported by scalable architecture" },
        { metric: "99.9%", description: "Uptime with Firebase cloud infrastructure" }
      ],
      features: [
        {
          title: "Smart Medication Reminders",
          description: "Automated push notifications with customizable schedules and dosage tracking"
        },
        {
          title: "Role-Based Access Control",
          description: "Separate dashboards for patients, caregivers, and healthcare providers"
        },
        {
          title: "QR Code Integration",
          description: "Quick prescription scanning and medication verification system"
        },
        {
          title: "Real-time Synchronization",
          description: "Live data updates across all devices using Firestore"
        },
        {
          title: "Secure Authentication",
          description: "Multi-factor authentication with Firebase security rules"
        }
      ],
      challenges: [
        "Implementing HIPAA-compliant data handling",
        "Designing intuitive UI for elderly users",
        "Optimizing battery usage for background notifications"
      ],
      impact: "Addresses the critical healthcare challenge where 50% of patients don't take medications as prescribed, potentially saving healthcare systems billions in preventable complications.",
      gradient: "from-blue-500 to-purple-600",
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/mediremind"
    },
    {
      title: "RoadAssist - Emergency Response Platform",
      subtitle: "Connecting Drivers with Help When They Need It Most",
      description: "A sophisticated full-stack web application that revolutionizes roadside assistance by providing real-time location-based services, connecting stranded drivers with nearby helpers through an intelligent matching system.",
      icon: <Globe className="w-8 h-8" />,
      category: "Location-Based Services",
      duration: "4 months",
      teamSize: "4 developers",
      status: "Deployed & Live",
      technologies: {
        frontend: ["React", "TypeScript", "Leaflet Maps", "Responsive CSS"],
        backend: ["Node.js", "Express.js", "MongoDB", "Mongoose ODM"],
        authentication: ["JWT", "Bcrypt", "Role-based Access Control"],
        deployment: ["Netlify", "MongoDB Atlas", "Serverless Functions"]
      },
      achievements: [
        { metric: "< 2 min", description: "Average response time for assistance requests" },
        { metric: "95%", description: "Test coverage with comprehensive white box testing" },
        { metric: "100%", description: "Uptime since deployment on Netlify" },
        { metric: "3 roles", description: "Customer, Helper, and Admin user management" }
      ],
      features: [
        {
          title: "Real-time Geolocation Matching",
          description: "Advanced MongoDB geospatial queries to find nearest available helpers"
        },
        {
          title: "Interactive Map Interface",
          description: "Leaflet-powered maps showing live locations and service areas"
        },
        {
          title: "Multi-role Authentication",
          description: "Secure JWT-based system supporting customers, helpers, and administrators"
        },
        {
          title: "Request Management System",
          description: "Complete workflow from request creation to service completion"
        },
        {
          title: "Responsive Design",
          description: "Mobile-first approach ensuring usability across all devices"
        }
      ],
      challenges: [
        "Implementing accurate geospatial calculations for helper matching",
        "Designing scalable database schema for location data",
        "Ensuring real-time updates without overwhelming the server"
      ],
      impact: "Reduces average wait time for roadside assistance from 45 minutes to under 15 minutes by leveraging crowd-sourced help and intelligent location matching.",
      gradient: "from-green-500 to-teal-600",
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/roadside-assistance"
    },
    {
      title: "RestaurantPro - Complete Business Management Suite",
      subtitle: "Streamlining Restaurant Operations with Smart Technology",
      description: "A comprehensive web application that transforms restaurant management by integrating order processing, customer rewards, inventory tracking, and analytics into one powerful platform.",
      icon: <Database className="w-8 h-8" />,
      category: "Business Management System",
      duration: "3 months",
      teamSize: "Solo Developer",
      status: "Production Ready",
      technologies: {
        backend: ["Python", "Flask", "SQLAlchemy", "MySQL"],
        frontend: ["JavaScript ES6+", "HTML5", "CSS3", "Bootstrap"],
        database: ["MySQL", "Complex Triggers", "Stored Procedures"],
        tools: ["Git", "MySQL Workbench", "Postman", "VS Code"]
      },
      achievements: [
        { metric: "50%", description: "Reduction in order processing time" },
        { metric: "30%", description: "Increase in customer retention through rewards" },
        { metric: "100%", description: "Accurate real-time inventory tracking" },
        { metric: "24/7", description: "System availability with robust error handling" }
      ],
      features: [
        {
          title: "Advanced Order Management",
          description: "Complete order lifecycle from placement to fulfillment with status tracking"
        },
        {
          title: "Intelligent Rewards System",
          description: "Automated point calculation with tiered benefits and redemption options"
        },
        {
          title: "Real-time Analytics Dashboard",
          description: "Live insights into sales, popular items, and customer behavior"
        },
        {
          title: "Inventory Management",
          description: "Automated stock tracking with low-inventory alerts and supplier integration"
        },
        {
          title: "Customer Relationship Management",
          description: "Comprehensive customer profiles with order history and preferences"
        }
      ],
      challenges: [
        "Designing complex SQL triggers for automatic rewards calculation",
        "Implementing real-time inventory updates across multiple order channels",
        "Creating responsive UI that works on restaurant tablets and phones"
      ],
      impact: "Enables small to medium restaurants to compete with larger chains by providing enterprise-level management tools at an affordable scale.",
      gradient: "from-orange-500 to-red-600",
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/restaurant-management"
    },
    {
      title: "ArcadeMaster - Interactive Gaming Platform",
      subtitle: "Classic Gaming Reimagined with Modern Java Technology",
      description: "A sophisticated desktop gaming application built with JavaFX, featuring multiple game modes, advanced physics simulation, and an engaging user experience that demonstrates mastery of object-oriented programming principles.",
      icon: <Gamepad2 className="w-8 h-8" />,
      category: "Desktop Application",
      duration: "2 months",
      teamSize: "Solo Developer",
      status: "Complete & Polished",
      technologies: {
        core: ["Java 11+", "JavaFX", "FXML", "CSS Styling"],
        concepts: ["OOP Design Patterns", "Event-Driven Programming", "MVC Architecture"],
        features: ["Collision Detection", "Physics Simulation", "Animation Framework"],
        tools: ["IntelliJ IDEA", "Scene Builder", "Git", "Maven"]
      },
      achievements: [
        { metric: "60 FPS", description: "Smooth gameplay with optimized rendering" },
        { metric: "5 levels", description: "Progressive difficulty with unique challenges" },
        { metric: "100%", description: "Object-oriented design implementation" },
        { metric: "0 bugs", description: "Thoroughly tested and debugged application" }
      ],
      features: [
        {
          title: "Advanced Physics Engine",
          description: "Custom-built collision detection with realistic ball physics and momentum"
        },
        {
          title: "Dynamic Difficulty Scaling",
          description: "Adaptive gameplay that increases challenge based on player performance"
        },
        {
          title: "Rich Visual Effects",
          description: "Smooth animations, particle effects, and responsive UI elements"
        },
        {
          title: "High Score System",
          description: "Persistent score tracking with leaderboard functionality"
        },
        {
          title: "Customizable Controls",
          description: "Configurable key bindings and game settings for personalized experience"
        }
      ],
      challenges: [
        "Implementing accurate collision detection algorithms",
        "Optimizing performance for smooth 60 FPS gameplay",
        "Designing intuitive controls and responsive user interface"
      ],
      impact: "Demonstrates advanced Java programming skills and serves as a foundation for understanding game development principles and desktop application architecture.",
      gradient: "from-purple-500 to-pink-600",
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/javafx-ball-game"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="projects" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
              🚀 Featured Projects Portfolio
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore my journey through innovative software solutions that solve real-world problems.
              Each project represents a unique challenge overcome through creative problem-solving and technical expertise.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 max-w-4xl mx-auto">
              {[
                { icon: "🏥", label: "Healthcare", count: "1 Project" },
                { icon: "🌐", label: "Web Apps", count: "2 Projects" },
                { icon: "📱", label: "Mobile", count: "1 Project" },
                { icon: "🎮", label: "Desktop", count: "1 Project" }
              ].map((stat, index) => (
                <div key={index} className="bg-white rounded-lg p-4 shadow-md">
                  <div className="text-2xl mb-2">{stat.icon}</div>
                  <div className="font-semibold text-gray-800">{stat.label}</div>
                  <div className="text-sm text-gray-600">{stat.count}</div>
                </div>
              ))}
            </div>
          </motion.div>

          <div className="space-y-12">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.01,
                  boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
                }}
                className="card overflow-hidden"
              >
                {/* Enhanced Project header */}
                <div className={`bg-gradient-to-r ${project.gradient} p-8 text-white relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <motion.div
                          whileHover={{ rotate: 360, scale: 1.1 }}
                          transition={{ duration: 0.5 }}
                          className="bg-white/20 p-4 rounded-full mr-4"
                        >
                          {project.icon}
                        </motion.div>
                        <div>
                          <h3 className="text-2xl font-bold mb-1">{project.title}</h3>
                          <p className="text-white/80 text-sm">{project.subtitle}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="bg-white/20 px-3 py-1 rounded-full text-sm">
                          {project.category}
                        </span>
                      </div>
                    </div>

                    <p className="text-white/90 leading-relaxed mb-4">
                      {project.description}
                    </p>

                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="bg-white/10 rounded-lg p-3">
                        <div className="text-sm text-white/70">Duration</div>
                        <div className="font-semibold">{project.duration}</div>
                      </div>
                      <div className="bg-white/10 rounded-lg p-3">
                        <div className="text-sm text-white/70">Team Size</div>
                        <div className="font-semibold">{project.teamSize}</div>
                      </div>
                      <div className="bg-white/10 rounded-lg p-3">
                        <div className="text-sm text-white/70">Status</div>
                        <div className="font-semibold">{project.status}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Project content */}
                <div className="p-8 space-y-8">
                  {/* Impact Statement */}
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r-lg">
                    <h4 className="font-semibold text-blue-800 mb-2">🎯 Project Impact</h4>
                    <p className="text-blue-700 text-sm leading-relaxed">{project.impact}</p>
                  </div>

                  {/* Technologies - Enhanced */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      🛠️ Technology Stack
                    </h4>
                    <div className="space-y-3">
                      {Object.entries(project.technologies).map(([category, techs], catIndex) => (
                        <div key={catIndex}>
                          <h5 className="text-sm font-medium text-gray-600 mb-2 capitalize">
                            {category}:
                          </h5>
                          <div className="flex flex-wrap gap-2">
                            {techs.map((tech, techIndex) => (
                              <span
                                key={techIndex}
                                className="bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 px-3 py-1 rounded-full text-sm font-medium border border-gray-300"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Key Achievements - Enhanced */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      🏆 Key Achievements
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {project.achievements.map((achievement, achIndex) => (
                        <div key={achIndex} className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-green-600 mb-1">
                            {achievement.metric}
                          </div>
                          <div className="text-green-700 text-sm">
                            {achievement.description}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Key Features - Enhanced */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      ⭐ Key Features
                    </h4>
                    <div className="space-y-3">
                      {project.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                          <h5 className="font-medium text-gray-800 mb-2">{feature.title}</h5>
                          <p className="text-gray-600 text-sm">{feature.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Challenges */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center">
                      🧩 Technical Challenges
                    </h4>
                    <ul className="space-y-2">
                      {project.challenges.map((challenge, challengeIndex) => (
                        <li key={challengeIndex} className="flex items-start">
                          <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                          <span className="text-gray-600 text-sm">{challenge}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Action buttons - Enhanced */}
                  <div className="flex flex-wrap gap-4 pt-4 border-t border-gray-200">
                    <motion.a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-gray-800 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-300 font-medium"
                    >
                      <Github size={18} />
                      View Source Code
                    </motion.a>
                    <motion.a
                      href={project.demoUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium"
                    >
                      <ExternalLink size={18} />
                      Live Demo
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Projects
