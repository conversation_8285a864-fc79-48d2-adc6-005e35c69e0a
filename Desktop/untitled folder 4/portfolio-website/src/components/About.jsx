import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { GraduationCap, Target, Code, Database } from 'lucide-react'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const highlights = [
    {
      icon: <GraduationCap className="w-8 h-8" />,
      title: "Education & Academic Excellence",
      description: "Senior Computer Science Student at Southeast Missouri State University",
      details: ["GPA: Strong Academic Performance", "Expected Graduation: 2024", "Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering"]
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Specialized Focus Areas",
      description: "Data Analysis, Full-Stack Development, Mobile Applications, Cloud Computing",
      details: ["Healthcare Technology Solutions", "Real-time Data Processing", "Cross-platform Mobile Development", "Scalable Web Applications"]
    },
    {
      icon: <Code className="w-8 h-8" />,
      title: "Development Expertise",
      description: "Modern Tech Stack: React Native, Firebase, Python, JavaScript, TypeScript",
      details: ["Frontend: React, HTML5, CSS3, JavaScript ES6+", "Backend: Node.js, Express, Flask, RESTful APIs", "Mobile: React Native, Expo, Cross-platform Development"]
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Data & Cloud Technologies",
      description: "Database Management, Cloud Architecture, API Development",
      details: ["Databases: MySQL, MongoDB, Firestore", "Cloud: Firebase, Google Cloud Functions", "Analytics: NumPy, Pandas, Data Visualization"]
    }
  ]

  const achievements = [
    {
      metric: "35%",
      label: "Medication Adherence Improvement",
      description: "Through MediRemind app development"
    },
    {
      metric: "40%",
      label: "Admin Time Reduction",
      description: "For healthcare providers using our solution"
    },
    {
      metric: "4+",
      label: "Major Projects Completed",
      description: "Full-stack applications with real impact"
    },
    {
      metric: "10+",
      label: "Technologies Mastered",
      description: "Modern frameworks and tools"
    }
  ]

  return (
    <section id="about" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
              About Me
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                🎯 Career Vision & Expertise
              </h3>
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border-l-4 border-blue-500">
                <p className="text-lg text-gray-700 leading-relaxed mb-4">
                  <strong>Motivated and detail-oriented senior Computer Science student</strong> with a proven track record
                  in developing impactful healthcare technology solutions. I specialize in bridging the gap between
                  complex technical challenges and user-friendly applications that make a real difference in people's lives.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed">
                  My passion lies in <strong>data-driven development</strong> and creating scalable solutions that improve
                  efficiency and user experience. From mobile health applications to full-stack web platforms,
                  I bring both technical expertise and creative problem-solving to every project.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                  <h4 className="text-xl font-bold text-gray-800 mb-3 flex items-center">
                    🚀 Current Focus
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">▶</span>
                      Healthcare Technology Innovation
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">▶</span>
                      Cross-platform Mobile Development
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">▶</span>
                      Cloud-based Data Solutions
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">▶</span>
                      Real-time Application Development
                    </li>
                  </ul>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
                  <h4 className="text-xl font-bold text-gray-800 mb-3 flex items-center">
                    🎓 Academic Excellence
                  </h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      Southeast Missouri State University
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      Bachelor of Science in Computer Science
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      Strong Foundation in Algorithms & Data Structures
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      Hands-on Project Experience
                    </li>
                  </ul>
                </div>
              </div>

              <div className="flex flex-wrap gap-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="inline-block"
                >
                  <a
                    href="#contact"
                    className="btn btn-primary"
                  >
                    💬 Let's Connect
                  </a>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="inline-block"
                >
                  <a
                    href="#projects"
                    className="btn btn-outline"
                  >
                    🚀 View Projects
                  </a>
                </motion.div>
              </div>
            </motion.div>

            {/* Right side - Highlights grid */}
            <motion.div variants={itemVariants} className="space-y-6">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                  }}
                  className="card p-6 border-l-4 border-gradient-to-b from-blue-500 to-purple-600"
                  style={{ borderLeftColor: index % 2 === 0 ? '#3B82F6' : '#8B5CF6' }}
                >
                  <div className="flex items-start space-x-4">
                    <motion.div
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.5 }}
                      className="flex-shrink-0 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center"
                    >
                      {highlight.icon}
                    </motion.div>
                    <div className="flex-1">
                      <h4 className="text-xl font-semibold text-gray-800 mb-2">
                        {highlight.title}
                      </h4>
                      <p className="text-gray-600 mb-3">
                        {highlight.description}
                      </p>
                      <ul className="space-y-1">
                        {highlight.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="text-sm text-gray-500 flex items-start">
                            <span className="text-blue-500 mr-2 text-xs">●</span>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Enhanced Stats section */}
          <motion.div
            variants={itemVariants}
            className="mt-16"
          >
            <h3 className="text-2xl font-bold text-center text-gray-800 mb-8">
              🏆 Impact & Achievements
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 text-center"
                >
                  <div className="text-4xl md:text-5xl font-bold gradient-text mb-2">
                    {achievement.metric}
                  </div>
                  <div className="text-gray-800 font-semibold mb-2">
                    {achievement.label}
                  </div>
                  <div className="text-gray-500 text-sm">
                    {achievement.description}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
