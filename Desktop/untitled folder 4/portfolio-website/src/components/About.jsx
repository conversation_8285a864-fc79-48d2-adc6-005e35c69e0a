import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { GraduationCap, Target, Code, Database } from 'lucide-react'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const highlights = [
    {
      icon: <GraduationCap className="w-8 h-8" />,
      title: "Education",
      description: "Senior Computer Science Student at Southeast Missouri State University"
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Focus Areas",
      description: "Data Analysis, Full-Stack Development, Mobile Applications"
    },
    {
      icon: <Code className="w-8 h-8" />,
      title: "Development",
      description: "React Native, Firebase, Python, JavaScript, TypeScript"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Data & Backend",
      description: "MySQL, MongoDB, RESTful APIs, Cloud Functions"
    }
  ]

  return (
    <section id="about" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
              About Me
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                Career Objective
              </h3>
              <p className="text-lg text-gray-600 leading-relaxed">
                Motivated and detail-oriented senior Computer Science student with a strong foundation in 
                data analysis and full-stack development. I am passionate about pursuing opportunities in 
                the field of data analysis, where I can apply my analytical skills and technical knowledge 
                to solve real-world problems.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                Proficient in Python, Java, Flask, MySQL, JavaScript, HTML, and CSS, with extensive 
                experience in developing comprehensive web applications and mobile solutions using 
                modern frameworks like React Native and Firebase.
              </p>
              
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="inline-block"
              >
                <a
                  href="#contact"
                  className="btn btn-primary mt-6"
                >
                  Let's Connect
                </a>
              </motion.div>
            </motion.div>

            {/* Right side - Highlights grid */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ 
                    scale: 1.05,
                    boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                  }}
                  className="card text-center p-6"
                >
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.5 }}
                    className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full mb-4"
                  >
                    {highlight.icon}
                  </motion.div>
                  <h4 className="text-xl font-semibold text-gray-800 mb-2">
                    {highlight.title}
                  </h4>
                  <p className="text-gray-600">
                    {highlight.description}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Stats section */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 text-center"
          >
            {[
              { number: "4+", label: "Major Projects" },
              { number: "10+", label: "Technologies" },
              { number: "35%", label: "Medication Adherence Improvement" },
              { number: "40%", label: "Admin Time Reduction" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ scale: 1.1 }}
                className="p-6"
              >
                <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
