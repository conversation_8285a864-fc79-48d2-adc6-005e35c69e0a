import React from 'react'
import { motion } from 'framer-motion'
import { ChevronDown, Github, Mail, Phone } from 'lucide-react'

const Hero = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="hero" className="min-h-screen flex items-center justify-center gradient-bg relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-2xl"
        />
      </div>

      <div className="container mx-auto px-4 text-center relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto"
        >
          <motion.h1
            variants={itemVariants}
            className="text-5xl md:text-7xl font-bold text-white mb-6"
          >
            Hi, I'm{' '}
            <span className="bg-gradient-to-r from-yellow-400 via-pink-500 to-orange-500 bg-clip-text text-transparent">
              Darshan Adhikari
            </span>
          </motion.h1>

          <motion.div
            variants={itemVariants}
            className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed"
          >
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              <span className="bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                🎓 Senior CS Student
              </span>
              <span className="bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                💻 Full-Stack Developer
              </span>
              <span className="bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                📊 Data Analyst
              </span>
            </div>
            <p className="text-center">Transforming Ideas into Digital Reality</p>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className="text-lg text-white/80 mb-8 max-w-3xl mx-auto"
          >
            🚀 Passionate about creating innovative solutions that bridge the gap between complex data insights
            and user-friendly applications. With expertise spanning from mobile development to cloud architecture,
            I specialize in building scalable, impactful software that makes a difference.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto"
          >
            {[
              { icon: "🏥", label: "Healthcare Tech", desc: "35% improvement" },
              { icon: "🌐", label: "Web Applications", desc: "4+ projects" },
              { icon: "📱", label: "Mobile Apps", desc: "Cross-platform" },
              { icon: "☁️", label: "Cloud Solutions", desc: "Firebase & AWS" }
            ].map((item, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center border border-white/20"
              >
                <div className="text-2xl mb-2">{item.icon}</div>
                <div className="text-white font-semibold text-sm">{item.label}</div>
                <div className="text-white/70 text-xs">{item.desc}</div>
              </motion.div>
            ))}
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <motion.a
              href="#projects"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-primary px-8 py-4 text-lg"
            >
              View My Work
            </motion.a>
            <motion.a
              href="#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-outline px-8 py-4 text-lg text-white border-white hover:bg-white hover:text-gray-800"
            >
              Get In Touch
            </motion.a>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex justify-center space-x-6"
          >
            <motion.a
              href="https://github.com/DarshanAdh"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, rotate: 5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Github size={32} />
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.2, rotate: -5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Mail size={32} />
            </motion.a>
            <motion.a
              href="tel:(*************"
              whileHover={{ scale: 1.2, rotate: 5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Phone size={32} />
            </motion.a>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <a href="#about" className="text-white/80 hover:text-white transition-colors duration-300">
          <ChevronDown size={32} />
        </a>
      </motion.div>
    </section>
  )
}

export default Hero
