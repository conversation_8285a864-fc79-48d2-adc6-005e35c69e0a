import React, { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Film, Music, Trophy, Play, Heart, Star, Users, Calendar } from 'lucide-react'

const Hobbies = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const hobbies = [
    {
      icon: <Film className="w-8 h-8" />,
      title: "Movies & Cinema",
      description: "Passionate about storytelling through film",
      gradient: "from-red-500 to-pink-600",
      details: {
        favoriteGenres: ["Sci-Fi", "Thriller", "Drama", "Action"],
        recentFavorites: ["Inception", "The Matrix", "Interstellar", "Blade Runner 2049", "Dune"],
        activity: "Movie nights and film analysis",
        connection: "Inspires creative problem-solving in coding"
      },
      stats: [
        { label: "Movies Watched", value: "200+" },
        { label: "Favorite Genre", value: "Sci-Fi" },
        { label: "Cinema Visits", value: "Monthly" }
      ]
    },
    {
      icon: <Music className="w-8 h-8" />,
      title: "Music & Audio",
      description: "Music fuels creativity and focus during coding",
      gradient: "from-purple-500 to-indigo-600",
      details: {
        favoriteGenres: ["Electronic", "Lo-fi", "Rock", "Instrumental", "Synthwave"],
        platforms: ["Spotify", "YouTube Music", "SoundCloud"],
        favoriteArtists: ["Nujabes", "Emancipator", "Tycho", "Boards of Canada"],
        activity: "Curating coding playlists and discovering new artists",
        connection: "Enhances concentration and creative flow while programming"
      },
      stats: [
        { label: "Hours Daily", value: "6+" },
        { label: "Playlists", value: "15+" },
        { label: "Favorite for Coding", value: "Lo-fi" }
      ]
    },
    {
      icon: <Trophy className="w-8 h-8" />,
      title: "Soccer & Sports",
      description: "Team sports teach collaboration and strategy",
      gradient: "from-green-500 to-emerald-600",
      details: {
        position: "Midfielder",
        experience: "10+ years playing",
        activity: "Weekend games and watching Premier League",
        connection: "Teamwork and strategic thinking translate to software development"
      },
      stats: [
        { label: "Years Playing", value: "10+" },
        { label: "Favorite Team", value: "Manchester United" },
        { label: "Games per Month", value: "4-6" }
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="hobbies" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-7xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
              🎯 Beyond the Code
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              When I'm not coding, you'll find me exploring these passions that fuel my creativity, 
              enhance my problem-solving skills, and keep me balanced as both a developer and a person.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {hobbies.map((hobby, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ 
                  scale: 1.03,
                  boxShadow: "0 25px 50px rgba(0,0,0,0.15)"
                }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg border border-gray-100"
              >
                {/* Header */}
                <div className={`bg-gradient-to-r ${hobby.gradient} p-6 text-white relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10">
                    <motion.div
                      whileHover={{ rotate: 360, scale: 1.1 }}
                      transition={{ duration: 0.5 }}
                      className="bg-white/20 p-4 rounded-full w-fit mb-4"
                    >
                      {hobby.icon}
                    </motion.div>
                    <h3 className="text-2xl font-bold mb-2">{hobby.title}</h3>
                    <p className="text-white/90">{hobby.description}</p>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 space-y-6">
                  {/* Stats */}
                  <div className="grid grid-cols-1 gap-3">
                    {hobby.stats.map((stat, statIndex) => (
                      <div key={statIndex} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-600 font-medium">{stat.label}</span>
                        <span className="font-bold text-gray-800">{stat.value}</span>
                      </div>
                    ))}
                  </div>

                  {/* Details */}
                  <div className="space-y-4">
                    {hobby.details.favoriteGenres && (
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                          <Star className="w-4 h-4 mr-2 text-yellow-500" />
                          Favorite Genres
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {hobby.details.favoriteGenres.map((genre, genreIndex) => (
                            <span
                              key={genreIndex}
                              className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                            >
                              {genre}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {hobby.details.recentFavorites && (
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                          <Heart className="w-4 h-4 mr-2 text-red-500" />
                          Recent Favorites
                        </h4>
                        <ul className="space-y-1">
                          {hobby.details.recentFavorites.map((item, itemIndex) => (
                            <li key={itemIndex} className="text-gray-600 text-sm flex items-center">
                              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {hobby.details.favoriteArtists && (
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                          <Music className="w-4 h-4 mr-2 text-purple-500" />
                          Favorite Artists
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {hobby.details.favoriteArtists.map((artist, artistIndex) => (
                            <span
                              key={artistIndex}
                              className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium"
                            >
                              {artist}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {hobby.details.activity && (
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                          <Play className="w-4 h-4 mr-2 text-green-500" />
                          Current Activity
                        </h4>
                        <p className="text-gray-600 text-sm">{hobby.details.activity}</p>
                      </div>
                    )}

                    {hobby.details.connection && (
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border-l-4 border-blue-500">
                        <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
                          <Users className="w-4 h-4 mr-2 text-purple-500" />
                          Connection to Development
                        </h4>
                        <p className="text-gray-700 text-sm italic">{hobby.details.connection}</p>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Fun Facts Section */}
          <motion.div variants={itemVariants} className="mt-16 text-center">
            <h3 className="text-2xl font-bold text-gray-800 mb-8">🎉 Fun Facts About Me</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[
                {
                  icon: "🎬",
                  fact: "Can quote entire scenes from The Matrix",
                  description: "Especially the ones about choice and reality!",
                  quote: '"There is no spoon" - My debugging philosophy'
                },
                {
                  icon: "🎵",
                  fact: "Have 15+ coding playlists on Spotify",
                  description: "Each one optimized for different types of programming tasks",
                  quote: "Lo-fi beats = bug-free code"
                },
                {
                  icon: "⚽",
                  fact: "Never missed a Manchester United match",
                  description: "Been a loyal fan for over 8 years through ups and downs",
                  quote: "Glory Glory Man United! 🔴"
                }
              ].map((fact, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 relative overflow-hidden"
                >
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -mr-10 -mt-10"></div>
                  <div className="relative z-10">
                    <div className="text-3xl mb-3">{fact.icon}</div>
                    <h4 className="font-semibold text-gray-800 mb-2">{fact.fact}</h4>
                    <p className="text-gray-600 text-sm mb-3">{fact.description}</p>
                    <div className="bg-gray-50 p-3 rounded-lg border-l-4 border-blue-500">
                      <p className="text-gray-700 text-xs italic">"{fact.quote}"</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Hobby Integration with Development */}
          <motion.div variants={itemVariants} className="mt-16">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-200">
              <h3 className="text-2xl font-bold text-center text-gray-800 mb-6">
                🔗 How My Hobbies Enhance My Development Skills
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[
                  {
                    hobby: "🎬 Movies",
                    skill: "Storytelling & UX",
                    connection: "Understanding narrative flow helps me design intuitive user journeys and create engaging interfaces."
                  },
                  {
                    hobby: "🎵 Music",
                    skill: "Pattern Recognition",
                    connection: "Musical patterns and rhythms enhance my ability to recognize code patterns and write clean, structured code."
                  },
                  {
                    hobby: "⚽ Soccer",
                    skill: "Team Collaboration",
                    connection: "Team sports teach communication, strategy, and working towards common goals - essential for agile development."
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    whileHover={{ scale: 1.02 }}
                    className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
                  >
                    <div className="text-center mb-4">
                      <div className="text-2xl mb-2">{item.hobby}</div>
                      <div className="text-lg font-semibold text-gray-800">{item.skill}</div>
                    </div>
                    <p className="text-gray-600 text-sm text-center leading-relaxed">
                      {item.connection}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Hobbies
