import React, { useRef, useState } from 'react'
import { motion, useInView } from 'framer-motion'
import { Mail, Phone, MapPin, Github, Send, User, MessageSquare, Linkedin, Briefcase, Heart } from 'lucide-react'

const Contact = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', message: '' })
  }

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email",
      value: "darshana<PERSON><EMAIL>",
      link: "mailto:darshana<PERSON><EMAIL>",
      description: "Best way to reach me for professional inquiries",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Phone",
      value: "(*************",
      link: "tel:(*************",
      description: "Available for calls Mon-Fri, 9 AM - 6 PM CST",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Location",
      value: "Cape Girardeau, MO",
      link: null,
      description: "Open to remote work and relocation opportunities",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: <Github className="w-6 h-6" />,
      title: "GitHub",
      value: "github.com/DarshanAdh",
      link: "https://github.com/DarshanAdh",
      description: "Check out my latest projects and contributions",
      color: "from-gray-600 to-gray-800"
    }
  ]

  const socialLinks = [
    {
      name: "LinkedIn",
      icon: <Linkedin className="w-5 h-5" />,
      url: "https://linkedin.com/in/darshan-adhikari",
      color: "bg-blue-600"
    },
    {
      name: "GitHub",
      icon: <Github className="w-5 h-5" />,
      url: "https://github.com/DarshanAdh",
      color: "bg-gray-800"
    },
    {
      name: "Email",
      icon: <Mail className="w-5 h-5" />,
      url: "mailto:<EMAIL>",
      color: "bg-red-600"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="contact" className="py-20 gradient-bg-2" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              💬 Let's Build Something Amazing Together
            </h2>
            <div className="w-24 h-1 bg-white/30 mx-auto mb-8"></div>
            <p className="text-xl text-white/90 max-w-3xl mx-auto mb-8">
              Ready to collaborate on your next project? Whether you're looking for a dedicated developer,
              a creative problem-solver, or a passionate team member, I'm excited to discuss how we can work together!
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[
                {
                  icon: "🚀",
                  title: "Full-Time Opportunities",
                  description: "Seeking entry-level to junior developer positions in healthcare tech, fintech, or innovative startups"
                },
                {
                  icon: "💼",
                  title: "Freelance Projects",
                  description: "Available for web development, mobile apps, and data analysis consulting projects"
                },
                {
                  icon: "🤝",
                  title: "Collaboration",
                  description: "Open to contributing to open-source projects and participating in hackathons"
                }
              ].map((opportunity, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -5 }}
                  className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20"
                >
                  <div className="text-3xl mb-3">{opportunity.icon}</div>
                  <h3 className="text-white font-semibold mb-2">{opportunity.title}</h3>
                  <p className="text-white/80 text-sm">{opportunity.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Enhanced Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                  📞 Contact Information
                </h3>
                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.02, x: 5 }}
                      className="group"
                    >
                      <div className="flex items-start space-x-4 p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-all duration-300">
                        <div className={`bg-gradient-to-r ${info.color} p-3 rounded-full shadow-lg`}>
                          {info.icon}
                        </div>
                        <div className="flex-1">
                          <h4 className="text-white font-semibold mb-1">{info.title}</h4>
                          {info.link ? (
                            <a
                              href={info.link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-white/90 hover:text-white transition-colors duration-300 font-medium block mb-2"
                            >
                              {info.value}
                            </a>
                          ) : (
                            <p className="text-white/90 font-medium mb-2">{info.value}</p>
                          )}
                          <p className="text-white/60 text-sm">{info.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Social Links */}
                <div className="mt-8 pt-6 border-t border-white/20">
                  <h4 className="text-white font-semibold mb-4">🌐 Connect with me:</h4>
                  <div className="flex space-x-4">
                    {socialLinks.map((social, index) => (
                      <motion.a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.1, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className={`${social.color} p-3 rounded-full text-white hover:shadow-lg transition-all duration-300`}
                        title={social.name}
                      >
                        {social.icon}
                      </motion.a>
                    ))}
                  </div>
                </div>
              </div>

              {/* Enhanced Call to Action */}
              <motion.div
                variants={itemVariants}
                className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10"></div>
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                    🚀 Let's Build Something Amazing Together!
                  </h3>
                  <p className="text-white/80 mb-6 leading-relaxed">
                    I'm actively seeking opportunities to contribute to innovative projects and grow as a developer.
                    Whether you're looking for a dedicated team member, a creative problem-solver, or someone passionate
                    about making a real impact through technology, I'd love to hear from you!
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    {[
                      { icon: <Briefcase className="w-5 h-5" />, title: "Full-time Roles", desc: "Ready to start immediately" },
                      { icon: <Heart className="w-5 h-5" />, title: "Passion Projects", desc: "Open to meaningful collaborations" },
                      { icon: <User className="w-5 h-5" />, title: "Mentorship", desc: "Always eager to learn and grow" }
                    ].map((item, index) => (
                      <div key={index} className="bg-white/10 rounded-lg p-4 text-center">
                        <div className="text-white mb-2 flex justify-center">{item.icon}</div>
                        <div className="text-white font-medium text-sm mb-1">{item.title}</div>
                        <div className="text-white/70 text-xs">{item.desc}</div>
                      </div>
                    ))}
                  </div>

                  <div className="flex flex-wrap gap-4">
                    <motion.a
                      href="mailto:<EMAIL>"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-white text-gray-800 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300 flex items-center gap-2"
                    >
                      <Mail className="w-4 h-4" />
                      Send Email
                    </motion.a>
                    <motion.a
                      href="https://github.com/DarshanAdh"
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-white/20 text-white px-6 py-3 rounded-full font-semibold hover:bg-white/30 transition-colors duration-300 flex items-center gap-2"
                    >
                      <Github className="w-4 h-4" />
                      View GitHub
                    </motion.a>
                    <motion.a
                      href="tel:(*************"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-full font-semibold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 flex items-center gap-2"
                    >
                      <Phone className="w-4 h-4" />
                      Call Now
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Contact Form */}
            <motion.div variants={itemVariants}>
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
                <h3 className="text-2xl font-bold text-white mb-6">
                  Send a Message
                </h3>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="block text-white/90 font-medium mb-2">
                      <User className="w-4 h-4 inline mr-2" />
                      Your Name
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300"
                      placeholder="Enter your name"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-white/90 font-medium mb-2">
                      <Mail className="w-4 h-4 inline mr-2" />
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300"
                      placeholder="Enter your email"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-white/90 font-medium mb-2">
                      <MessageSquare className="w-4 h-4 inline mr-2" />
                      Message
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300 resize-none"
                      placeholder="Tell me about your project or opportunity..."
                    />
                  </div>
                  
                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-white text-gray-800 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 flex items-center justify-center space-x-2"
                  >
                    <Send className="w-4 h-4" />
                    <span>Send Message</span>
                  </motion.button>
                </form>
              </div>
            </motion.div>
          </div>

          {/* Footer */}
          <motion.div variants={itemVariants} className="text-center mt-16">
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <p className="text-white/80">
                © 2024 Darshan Adhikari. Built with React, Framer Motion, and lots of ☕
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Contact
