import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ExternalLink, Github, Smartphone, Globe, Database, Gamepad2, Filter } from 'lucide-react';

const ModernProjects: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const filters = ['All', 'Mobile', 'Web', 'Healthcare', 'Desktop'];

  const projects = [
    {
      id: 1,
      title: "MediRemind",
      subtitle: "Healthcare Innovation Platform",
      description: "Cross-platform mobile app revolutionizing medication adherence through intelligent automation and real-time monitoring.",
      category: ["Mobile", "Healthcare"],
      image: "/api/placeholder/400/250",
      technologies: ["React Native", "Firebase", "Expo", "Cloud Functions"],
      metrics: [
        { label: "Adherence Improvement", value: "35%" },
        { label: "Admin Time Reduction", value: "40%" },
        { label: "User Capacity", value: "1000+" }
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/mediremind",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      id: 2,
      title: "RoadAssist",
      subtitle: "Emergency Response Platform",
      description: "Full-stack web application providing real-time location-based roadside assistance with intelligent helper matching.",
      category: ["Web"],
      image: "/api/placeholder/400/250",
      technologies: ["React", "TypeScript", "Node.js", "MongoDB"],
      metrics: [
        { label: "Response Time", value: "< 2 min" },
        { label: "Test Coverage", value: "95%" },
        { label: "Uptime", value: "100%" }
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/roadside-assistance",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      id: 3,
      title: "RestaurantPro",
      subtitle: "Business Management Suite",
      description: "Comprehensive web application transforming restaurant operations with integrated order processing and analytics.",
      category: ["Web"],
      image: "/api/placeholder/400/250",
      technologies: ["Python", "Flask", "MySQL", "JavaScript"],
      metrics: [
        { label: "Processing Speed", value: "50%" },
        { label: "Customer Retention", value: "30%" },
        { label: "Inventory Accuracy", value: "100%" }
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/restaurant-management",
      gradient: "from-orange-500 to-red-500"
    },
    {
      id: 4,
      title: "ArcadeMaster",
      subtitle: "Interactive Gaming Platform",
      description: "Sophisticated desktop gaming application with advanced physics simulation and engaging user experience.",
      category: ["Desktop"],
      image: "/api/placeholder/400/250",
      technologies: ["Java", "JavaFX", "OOP Design"],
      metrics: [
        { label: "Frame Rate", value: "60 FPS" },
        { label: "Difficulty Levels", value: "5" },
        { label: "Code Quality", value: "100%" }
      ],
      demoUrl: "#",
      githubUrl: "https://github.com/DarshanAdh/javafx-ball-game",
      gradient: "from-purple-500 to-pink-500"
    }
  ];

  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category.includes(activeFilter));

  const getIcon = (category: string[]) => {
    if (category.includes('Mobile')) return <Smartphone className="w-5 h-5" />;
    if (category.includes('Web')) return <Globe className="w-5 h-5" />;
    if (category.includes('Desktop')) return <Gamepad2 className="w-5 h-5" />;
    return <Database className="w-5 h-5" />;
  };

  return (
    <section className="py-20 bg-slate-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%233b82f6' fill-opacity='0.1'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20v-40c11.046 0 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '40px 40px'
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            Featured <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">Projects</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Explore my journey through innovative software solutions that solve real-world problems
          </p>
        </motion.div>

        {/* Project Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {filters.map((filter) => (
            <motion.button
              key={filter}
              onClick={() => setActiveFilter(filter)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeFilter === filter
                  ? 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg'
                  : 'bg-white text-slate-600 hover:bg-slate-100 shadow-md'
              }`}
            >
              <Filter size={16} />
              {filter}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div layout className="grid lg:grid-cols-2 gap-8">
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.4 }}
                className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300"
              >
                {/* Project Image */}
                <div className={`h-48 bg-gradient-to-br ${project.gradient} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/20"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                        {getIcon(project.category)}
                      </div>
                      <p className="text-sm opacity-80">Project Screenshot</p>
                      <p className="text-xs opacity-60">Coming Soon</p>
                    </div>
                  </div>
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/20 backdrop-blur-md text-white px-3 py-1 rounded-full text-sm font-medium">
                      {project.category.join(' • ')}
                    </span>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-slate-800 mb-2">{project.title}</h3>
                    <p className="text-blue-600 font-medium mb-3">{project.subtitle}</p>
                    <p className="text-slate-600 leading-relaxed">{project.description}</p>
                  </div>

                  {/* Technologies */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, index) => (
                        <span
                          key={index}
                          className="bg-slate-100 text-slate-700 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {project.metrics.map((metric, index) => (
                      <div key={index} className="text-center">
                        <div className="text-lg font-bold text-blue-600">{metric.value}</div>
                        <div className="text-xs text-slate-500">{metric.label}</div>
                      </div>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <motion.a
                      href={project.demoUrl}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-3 px-4 rounded-xl font-semibold text-center hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      <ExternalLink size={16} />
                      Live Demo
                    </motion.a>
                    <motion.a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex-1 bg-slate-800 text-white py-3 px-4 rounded-xl font-semibold text-center hover:bg-slate-700 transition-all duration-300 flex items-center justify-center gap-2"
                    >
                      <Github size={16} />
                      Source Code
                    </motion.a>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Project Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-blue-600 mb-2">4+</div>
              <div className="text-slate-600 text-sm">Major Projects</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-cyan-600 mb-2">15+</div>
              <div className="text-slate-600 text-sm">Technologies Used</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-blue-600 mb-2">1000+</div>
              <div className="text-slate-600 text-sm">Lines of Code</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="text-3xl font-bold text-cyan-600 mb-2">100%</div>
              <div className="text-slate-600 text-sm">Passion Driven</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernProjects;
