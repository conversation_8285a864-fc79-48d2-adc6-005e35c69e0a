import React from 'react';
import { motion } from 'framer-motion';
import { Code, Database, Smartphone, Brain, Target, Zap } from 'lucide-react';

const ModernAbout: React.FC = () => {
  const achievements = [
    { number: "35%", label: "Medication Adherence", desc: "Improvement through MediRemind" },
    { number: "4+", label: "Full-Stack Projects", desc: "Production-ready applications" },
    { number: "10+", label: "Technologies", desc: "Modern frameworks & tools" },
    { number: "2024", label: "Expected Graduation", desc: "Computer Science Degree" }
  ];

  const focusAreas = [
    {
      icon: <Code className="w-8 h-8" />,
      title: "Full-Stack Development",
      description: "Building end-to-end solutions with React, Node.js, and modern frameworks",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: <Database className="w-8 h-8" />,
      title: "Data Analysis",
      description: "Transforming complex data into actionable insights using Python and visualization tools",
      color: "from-cyan-500 to-blue-500"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Mobile Development",
      description: "Creating cross-platform mobile experiences with React Native and Expo",
      color: "from-blue-600 to-slate-600"
    }
  ];

  return (
    <section className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-slate-800 mb-6">
            About <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">Me</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mb-8"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Side - Polaroid Style Card */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="bg-white p-6 rounded-2xl shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-300">
              <div className="w-full h-80 bg-gradient-to-br from-slate-100 to-blue-100 rounded-xl mb-4 flex items-center justify-center relative overflow-hidden">
                {/* Coding Session Photo Placeholder */}
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
                    <Code className="w-12 h-12 text-white" />
                  </div>
                  <p className="text-slate-600 font-medium">Coding Session Photo</p>
                  <p className="text-slate-400 text-sm">Coming Soon</p>
                </div>
                
                {/* Floating Code Elements */}
                <motion.div
                  animate={{ y: [-5, 5, -5] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="absolute top-4 left-4 text-blue-400/30 font-mono text-xs"
                >
                  {"<Developer />"}
                </motion.div>
                <motion.div
                  animate={{ y: [5, -5, 5] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="absolute bottom-4 right-4 text-cyan-400/30 font-mono text-xs"
                >
                  console.log("Hello World!");
                </motion.div>
              </div>
              <div className="text-center">
                <p className="text-slate-700 font-handwriting text-lg italic">
                  "Building real solutions with a sci-fi playlist in the background."
                </p>
                <p className="text-slate-500 text-sm mt-2">- Darshan's coding philosophy</p>
              </div>
            </div>
          </motion.div>

          {/* Right Side - Career Vision */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-3xl font-bold text-slate-800 mb-4 flex items-center gap-3">
                <Brain className="w-8 h-8 text-blue-500" />
                My Journey
              </h3>
              <p className="text-lg text-slate-600 leading-relaxed mb-6">
                As a senior Computer Science student at Southeast Missouri State University, 
                I'm passionate about creating technology that makes a real difference. My journey 
                spans from healthcare innovation to full-stack development, always with a focus 
                on solving meaningful problems.
              </p>
              <p className="text-lg text-slate-600 leading-relaxed">
                When I'm not coding, you'll find me analyzing the latest sci-fi films, 
                curating the perfect coding playlist, or cheering for FC Barcelona. 
                These interests fuel my creativity and bring fresh perspectives to my development work.
              </p>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-xl border-l-4 border-blue-500">
              <h4 className="text-xl font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Target className="w-6 h-6 text-blue-500" />
                Current Focus
              </h4>
              <ul className="space-y-2 text-slate-600">
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  Healthcare technology innovation
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  Cross-platform mobile development
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  Data-driven application development
                </li>
                <li className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-500" />
                  Seeking full-time opportunities in 2024
                </li>
              </ul>
            </div>
          </motion.div>
        </div>

        {/* Achievement Stats */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {achievements.map((achievement, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05, y: -5 }}
              className="bg-white rounded-2xl p-6 text-center shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2">
                {achievement.number}
              </div>
              <div className="text-slate-800 font-semibold mb-1">{achievement.label}</div>
              <div className="text-slate-500 text-sm">{achievement.desc}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Focus Areas */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-3xl font-bold text-center text-slate-800 mb-12">
            What I'm <span className="bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">Passionate About</span>
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            {focusAreas.map((area, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -10 }}
                className="bg-white rounded-2xl p-8 shadow-lg border border-slate-100 hover:shadow-xl transition-all duration-300 relative overflow-hidden"
              >
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${area.color}`}></div>
                <div className={`inline-flex p-3 rounded-xl bg-gradient-to-r ${area.color} text-white mb-4`}>
                  {area.icon}
                </div>
                <h4 className="text-xl font-bold text-slate-800 mb-3">{area.title}</h4>
                <p className="text-slate-600 leading-relaxed">{area.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernAbout;
