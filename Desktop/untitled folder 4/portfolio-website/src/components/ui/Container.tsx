import React from 'react';
import { ContainerProps } from '../../types';

const Container: React.FC<ContainerProps> = ({
  size = 'lg',
  className = '',
  children,
}) => {
  const sizeClasses = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  };

  const classes = [
    'container',
    'mx-auto',
    'px-4',
    'sm:px-6',
    'lg:px-8',
    sizeClasses[size],
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export default Container;
