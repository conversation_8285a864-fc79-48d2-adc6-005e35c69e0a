import React from 'react';
import { motion } from 'framer-motion';
import { ButtonVariant } from '../../types';

interface ButtonProps extends ButtonVariant {
  children: React.ReactNode;
  onClick?: () => void;
  href?: string;
  target?: string;
  rel?: string;
  className?: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  rounded = 'lg',
  shadow = 'md',
  onClick,
  href,
  target,
  rel,
  className = '',
  disabled = false,
  icon,
  iconPosition = 'left',
  loading = false,
}) => {
  const baseClasses = 'btn';
  const variantClasses = `btn-${variant}`;
  const sizeClasses = `btn-${size}`;
  const roundedClasses = rounded !== 'lg' ? `rounded-${rounded}` : '';
  const shadowClasses = shadow !== 'md' ? `shadow-${shadow}` : '';
  
  const classes = [
    baseClasses,
    variantClasses,
    sizeClasses,
    roundedClasses,
    shadowClasses,
    className,
    disabled ? 'opacity-50 cursor-not-allowed' : '',
    loading ? 'cursor-wait' : ''
  ].filter(Boolean).join(' ');

  const content = (
    <>
      {loading && (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
        />
      )}
      {!loading && icon && iconPosition === 'left' && icon}
      {children}
      {!loading && icon && iconPosition === 'right' && icon}
    </>
  );

  const motionProps = {
    whileHover: disabled || loading ? {} : { scale: 1.02, y: -2 },
    whileTap: disabled || loading ? {} : { scale: 0.98 },
    transition: { duration: 0.2 }
  };

  if (href) {
    return (
      <motion.a
        href={href}
        target={target}
        rel={rel}
        className={classes}
        {...motionProps}
      >
        {content}
      </motion.a>
    );
  }

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled || loading}
      className={classes}
      {...motionProps}
    >
      {content}
    </motion.button>
  );
};

export default Button;
