import React from 'react';
import { motion } from 'framer-motion';
import { SectionProps } from '../../types';

interface EnhancedSectionProps extends SectionProps {
  pattern?: 'dots' | 'grid' | 'mesh' | 'waves' | 'none';
  overlay?: boolean;
  parallax?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

const Section: React.FC<EnhancedSectionProps> = ({
  id,
  className = '',
  background = 'light',
  pattern = 'none',
  overlay = false,
  parallax = false,
  padding = 'lg',
  children,
}) => {
  const baseClasses = 'relative';
  const backgroundClasses = {
    light: 'bg-gray-50',
    dark: 'bg-gray-900',
    gradient: 'section-gradient-1',
    pattern: 'section-pattern'
  };
  
  const paddingClasses = {
    none: '',
    sm: 'py-12',
    md: 'py-16',
    lg: 'py-20',
    xl: 'py-24'
  };

  const patternClasses = {
    dots: 'bg-dots-pattern',
    grid: 'bg-grid-pattern',
    mesh: 'section-mesh',
    waves: 'bg-waves-pattern',
    none: ''
  };

  const classes = [
    baseClasses,
    backgroundClasses[background],
    paddingClasses[padding],
    patternClasses[pattern],
    className
  ].filter(Boolean).join(' ');

  return (
    <motion.section
      id={id}
      className={classes}
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      {/* Background Pattern Elements */}
      {pattern === 'mesh' && (
        <>
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360] 
              }}
              transition={{ 
                duration: 20, 
                repeat: Infinity, 
                ease: "linear" 
              }}
            />
            <motion.div
              className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-pink-400/20 to-yellow-600/20 rounded-full blur-3xl"
              animate={{ 
                scale: [1.2, 1, 1.2],
                rotate: [360, 180, 0] 
              }}
              transition={{ 
                duration: 15, 
                repeat: Infinity, 
                ease: "linear" 
              }}
            />
          </div>
        </>
      )}

      {/* Overlay */}
      {overlay && (
        <div className="absolute inset-0 bg-black/10 backdrop-blur-sm" />
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.section>
  );
};

export default Section;
