import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'glass' | 'gradient';
  hover?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  border?: boolean;
  gradient?: string;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  variant = 'default',
  hover = true,
  padding = 'lg',
  rounded = '2xl',
  shadow = 'md',
  border = true,
  gradient,
  onClick,
}) => {
  const baseClasses = 'card';
  const variantClasses = variant !== 'default' ? `card-${variant}` : '';
  const paddingClasses = padding !== 'none' ? `p-${padding === 'sm' ? '4' : padding === 'md' ? '6' : padding === 'lg' ? '8' : '10'}` : '';
  const roundedClasses = rounded !== '2xl' ? `rounded-${rounded}` : '';
  const shadowClasses = shadow !== 'md' ? `shadow-${shadow}` : '';
  const borderClasses = border ? 'border border-gray-200' : '';
  const cursorClasses = onClick ? 'cursor-pointer' : '';
  
  const classes = [
    baseClasses,
    variantClasses,
    paddingClasses,
    roundedClasses,
    shadowClasses,
    borderClasses,
    cursorClasses,
    className
  ].filter(Boolean).join(' ');

  const motionProps = hover ? {
    whileHover: { 
      scale: 1.02, 
      y: -8,
      boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
    },
    transition: { duration: 0.3 }
  } : {};

  const cardStyle = gradient ? {
    background: gradient,
    color: 'white'
  } : {};

  return (
    <motion.div
      className={classes}
      style={cardStyle}
      onClick={onClick}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
};

export default Card;
