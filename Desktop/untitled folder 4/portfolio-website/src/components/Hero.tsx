import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Github, Mail, Phone, Sparkles, Code, Database, Smartphone } from 'lucide-react';
import Button from './ui/Button';
import Section from './ui/Section';
import Container from './ui/Container';

const Hero: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section
      id="hero"
      className="min-h-screen flex items-center justify-center overflow-hidden relative"
    >
      {/* Enhanced Animated background elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            rotate: [0, 180, 360],
            x: [0, 100, 0],
            y: [0, -50, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-br from-blue-400/20 to-white/10 rounded-full blur-2xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.4],
            rotate: [360, 180, 0],
            x: [0, -80, 0],
            y: [0, 60, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-tl from-white/15 to-slate-400/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            rotate: [0, 90, 180],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-full blur-3xl"
        />
      </div>

      <Container size="xl" className="text-center relative z-10 py-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto"
        >
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute -top-4 -right-4 text-yellow-300"
            >
              <Sparkles size={32} />
            </motion.div>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-4 leading-tight">
              Hi, I'm{' '}
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-blue-400 via-blue-300 to-white bg-clip-text text-transparent animate-pulse">
                  Darshan Adhikari
                </span>
                <motion.div
                  animate={{ scaleX: [0, 1, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-white rounded-full"
                />
              </span>
            </h1>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed"
          >
            <div className="flex flex-wrap justify-center gap-3 mb-6">
              {[
                { icon: "🎓", text: "Senior CS Student", color: "from-blue-400 to-blue-600" },
                { icon: "💻", text: "Full-Stack Developer", color: "from-slate-400 to-slate-600" },
                { icon: "📊", text: "Data Analyst", color: "from-blue-300 to-blue-500" }
              ].map((badge, index) => (
                <motion.span
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.05, y: -2 }}
                  className={`glass px-6 py-3 rounded-full border border-white/30 bg-gradient-to-r ${badge.color} bg-opacity-20 backdrop-blur-md`}
                >
                  <span className="mr-2">{badge.icon}</span>
                  {badge.text}
                </motion.span>
              ))}
            </div>
            <motion.p
              className="text-center text-2xl md:text-3xl font-semibold text-glow"
              animate={{ opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              Transforming Ideas into Digital Reality
            </motion.p>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className="text-lg text-white/80 mb-8 max-w-3xl mx-auto"
          >
            🚀 Passionate about creating innovative solutions that bridge the gap between complex data insights
            and user-friendly applications. With expertise spanning from mobile development to cloud architecture,
            I specialize in building scalable, impactful software that makes a difference.
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-5xl mx-auto"
          >
            {[
              {
                icon: <Code className="w-8 h-8" />,
                label: "Healthcare Tech",
                desc: "35% improvement",
                gradient: "from-blue-400 to-blue-600"
              },
              {
                icon: <Database className="w-8 h-8" />,
                label: "Web Applications",
                desc: "4+ projects",
                gradient: "from-slate-400 to-slate-600"
              },
              {
                icon: <Smartphone className="w-8 h-8" />,
                label: "Mobile Apps",
                desc: "Cross-platform",
                gradient: "from-blue-300 to-blue-500"
              },
              {
                icon: "☁️",
                label: "Cloud Solutions",
                desc: "Firebase & AWS",
                gradient: "from-slate-500 to-slate-700"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ scale: 1.08, y: -8, rotateY: 5 }}
                className={`glass-dark rounded-2xl p-6 text-center border border-white/20 bg-gradient-to-br ${item.gradient} bg-opacity-10 relative overflow-hidden group`}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                />
                <div className="relative z-10">
                  <motion.div
                    className="text-3xl mb-3 flex justify-center"
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    {typeof item.icon === 'string' ? item.icon : item.icon}
                  </motion.div>
                  <div className="text-white font-bold text-sm mb-1">{item.label}</div>
                  <div className="text-white/80 text-xs">{item.desc}</div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Personal Touch - Hobbies Preview */}
          <motion.div
            variants={itemVariants}
            className="mb-12"
          >
            <p className="text-white/80 text-center mb-4">When I'm not coding, you'll find me:</p>
            <div className="flex justify-center space-x-6">
              {[
                { icon: "🎬", label: "Watching Sci-Fi" },
                { icon: "🎵", label: "Curating Playlists" },
                { icon: "⚽", label: "Supporting Barça" }
              ].map((hobby, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="flex items-center space-x-2 bg-white/10 backdrop-blur-md px-4 py-2 rounded-full border border-white/20"
                >
                  <span className="text-lg">{hobby.icon}</span>
                  <span className="text-white/90 text-sm font-medium">{hobby.label}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
          >
            <Button
              variant="gradient"
              size="lg"
              href="#projects"
              className="px-10 py-4 text-lg font-bold shadow-2xl"
              icon={<Code className="w-5 h-5" />}
            >
              View My Work
            </Button>
            <Button
              variant="ghost"
              size="lg"
              href="#contact"
              className="px-10 py-4 text-lg font-bold text-white border-2 border-white/30"
              icon={<Mail className="w-5 h-5" />}
            >
              Get In Touch
            </Button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex justify-center space-x-6"
          >
            <motion.a
              href="https://github.com/DarshanAdh"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.2, rotate: 5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Github size={32} />
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.2, rotate: -5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Mail size={32} />
            </motion.a>
            <motion.a
              href="tel:(*************"
              whileHover={{ scale: 1.2, rotate: 5 }}
              className="text-white/80 hover:text-white transition-colors duration-300"
            >
              <Phone size={32} />
            </motion.a>
          </motion.div>
        </motion.div>
      </Container>

      {/* Enhanced Scroll indicator */}
      <motion.div
        animate={{ y: [0, 15, 0] }}
        transition={{ duration: 2.5, repeat: Infinity, ease: "easeInOut" }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.a
          href="#about"
          className="flex flex-col items-center text-white/80 hover:text-white transition-colors duration-300 group"
          whileHover={{ scale: 1.1 }}
        >
          <span className="text-sm mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            Scroll to explore
          </span>
          <motion.div
            animate={{ rotate: [0, 0, 180, 180, 0] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            className="p-2 rounded-full border border-white/30 backdrop-blur-sm"
          >
            <ChevronDown size={24} />
          </motion.div>
        </motion.a>
      </motion.div>
    </section>
  )
}

export default Hero
