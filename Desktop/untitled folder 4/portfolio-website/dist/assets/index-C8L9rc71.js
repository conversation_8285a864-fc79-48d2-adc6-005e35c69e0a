(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))o(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&o(h)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function o(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();var gr={exports:{}},il={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm;function l1(){if(Cm)return il;Cm=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(o,c,d){var h=null;if(d!==void 0&&(h=""+d),c.key!==void 0&&(h=""+c.key),"key"in c){d={};for(var g in c)g!=="key"&&(d[g]=c[g])}else d=c;return c=d.ref,{$$typeof:i,type:o,key:h,ref:c!==void 0?c:null,props:d}}return il.Fragment=l,il.jsx=u,il.jsxs=u,il}var jm;function s1(){return jm||(jm=1,gr.exports=l1()),gr.exports}var M=s1(),vr={exports:{}},lt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vm;function u1(){if(Vm)return lt;Vm=1;var i=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),x=Symbol.iterator;function A(S){return S===null||typeof S!="object"?null:(S=x&&S[x]||S["@@iterator"],typeof S=="function"?S:null)}var z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,Q={};function G(S,_,X){this.props=S,this.context=_,this.refs=Q,this.updater=X||z}G.prototype.isReactComponent={},G.prototype.setState=function(S,_){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,_,"setState")},G.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function q(){}q.prototype=G.prototype;function K(S,_,X){this.props=S,this.context=_,this.refs=Q,this.updater=X||z}var L=K.prototype=new q;L.constructor=K,B(L,G.prototype),L.isPureReactComponent=!0;var at=Array.isArray,H={H:null,A:null,T:null,S:null,V:null},st=Object.prototype.hasOwnProperty;function ht(S,_,X,Y,F,dt){return X=dt.ref,{$$typeof:i,type:S,key:_,ref:X!==void 0?X:null,props:dt}}function $(S,_){return ht(S.type,_,void 0,void 0,void 0,S.props)}function Mt(S){return typeof S=="object"&&S!==null&&S.$$typeof===i}function qt(S){var _={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(X){return _[X]})}var It=/\/+/g;function Yt(S,_){return typeof S=="object"&&S!==null&&S.key!=null?qt(""+S.key):_.toString(36)}function Je(){}function He(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(Je,Je):(S.status="pending",S.then(function(_){S.status==="pending"&&(S.status="fulfilled",S.value=_)},function(_){S.status==="pending"&&(S.status="rejected",S.reason=_)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Jt(S,_,X,Y,F){var dt=typeof S;(dt==="undefined"||dt==="boolean")&&(S=null);var it=!1;if(S===null)it=!0;else switch(dt){case"bigint":case"string":case"number":it=!0;break;case"object":switch(S.$$typeof){case i:case l:it=!0;break;case v:return it=S._init,Jt(it(S._payload),_,X,Y,F)}}if(it)return F=F(S),it=Y===""?"."+Yt(S,0):Y,at(F)?(X="",it!=null&&(X=it.replace(It,"$&/")+"/"),Jt(F,_,X,"",function(dn){return dn})):F!=null&&(Mt(F)&&(F=$(F,X+(F.key==null||S&&S.key===F.key?"":(""+F.key).replace(It,"$&/")+"/")+it)),_.push(F)),1;it=0;var he=Y===""?".":Y+":";if(at(S))for(var Et=0;Et<S.length;Et++)Y=S[Et],dt=he+Yt(Y,Et),it+=Jt(Y,_,X,dt,F);else if(Et=A(S),typeof Et=="function")for(S=Et.call(S),Et=0;!(Y=S.next()).done;)Y=Y.value,dt=he+Yt(Y,Et++),it+=Jt(Y,_,X,dt,F);else if(dt==="object"){if(typeof S.then=="function")return Jt(He(S),_,X,Y,F);throw _=String(S),Error("Objects are not valid as a React child (found: "+(_==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":_)+"). If you meant to render a collection of children, use an array instead.")}return it}function C(S,_,X){if(S==null)return S;var Y=[],F=0;return Jt(S,Y,"","",function(dt){return _.call(X,dt,F++)}),Y}function U(S){if(S._status===-1){var _=S._result;_=_(),_.then(function(X){(S._status===0||S._status===-1)&&(S._status=1,S._result=X)},function(X){(S._status===0||S._status===-1)&&(S._status=2,S._result=X)}),S._status===-1&&(S._status=0,S._result=_)}if(S._status===1)return S._result.default;throw S._result}var k=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var _=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(_))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function pt(){}return lt.Children={map:C,forEach:function(S,_,X){C(S,function(){_.apply(this,arguments)},X)},count:function(S){var _=0;return C(S,function(){_++}),_},toArray:function(S){return C(S,function(_){return _})||[]},only:function(S){if(!Mt(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},lt.Component=G,lt.Fragment=u,lt.Profiler=c,lt.PureComponent=K,lt.StrictMode=o,lt.Suspense=y,lt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=H,lt.__COMPILER_RUNTIME={__proto__:null,c:function(S){return H.H.useMemoCache(S)}},lt.cache=function(S){return function(){return S.apply(null,arguments)}},lt.cloneElement=function(S,_,X){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var Y=B({},S.props),F=S.key,dt=void 0;if(_!=null)for(it in _.ref!==void 0&&(dt=void 0),_.key!==void 0&&(F=""+_.key),_)!st.call(_,it)||it==="key"||it==="__self"||it==="__source"||it==="ref"&&_.ref===void 0||(Y[it]=_[it]);var it=arguments.length-2;if(it===1)Y.children=X;else if(1<it){for(var he=Array(it),Et=0;Et<it;Et++)he[Et]=arguments[Et+2];Y.children=he}return ht(S.type,F,void 0,void 0,dt,Y)},lt.createContext=function(S){return S={$$typeof:h,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:d,_context:S},S},lt.createElement=function(S,_,X){var Y,F={},dt=null;if(_!=null)for(Y in _.key!==void 0&&(dt=""+_.key),_)st.call(_,Y)&&Y!=="key"&&Y!=="__self"&&Y!=="__source"&&(F[Y]=_[Y]);var it=arguments.length-2;if(it===1)F.children=X;else if(1<it){for(var he=Array(it),Et=0;Et<it;Et++)he[Et]=arguments[Et+2];F.children=he}if(S&&S.defaultProps)for(Y in it=S.defaultProps,it)F[Y]===void 0&&(F[Y]=it[Y]);return ht(S,dt,void 0,void 0,null,F)},lt.createRef=function(){return{current:null}},lt.forwardRef=function(S){return{$$typeof:g,render:S}},lt.isValidElement=Mt,lt.lazy=function(S){return{$$typeof:v,_payload:{_status:-1,_result:S},_init:U}},lt.memo=function(S,_){return{$$typeof:m,type:S,compare:_===void 0?null:_}},lt.startTransition=function(S){var _=H.T,X={};H.T=X;try{var Y=S(),F=H.S;F!==null&&F(X,Y),typeof Y=="object"&&Y!==null&&typeof Y.then=="function"&&Y.then(pt,k)}catch(dt){k(dt)}finally{H.T=_}},lt.unstable_useCacheRefresh=function(){return H.H.useCacheRefresh()},lt.use=function(S){return H.H.use(S)},lt.useActionState=function(S,_,X){return H.H.useActionState(S,_,X)},lt.useCallback=function(S,_){return H.H.useCallback(S,_)},lt.useContext=function(S){return H.H.useContext(S)},lt.useDebugValue=function(){},lt.useDeferredValue=function(S,_){return H.H.useDeferredValue(S,_)},lt.useEffect=function(S,_,X){var Y=H.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Y.useEffect(S,_)},lt.useId=function(){return H.H.useId()},lt.useImperativeHandle=function(S,_,X){return H.H.useImperativeHandle(S,_,X)},lt.useInsertionEffect=function(S,_){return H.H.useInsertionEffect(S,_)},lt.useLayoutEffect=function(S,_){return H.H.useLayoutEffect(S,_)},lt.useMemo=function(S,_){return H.H.useMemo(S,_)},lt.useOptimistic=function(S,_){return H.H.useOptimistic(S,_)},lt.useReducer=function(S,_,X){return H.H.useReducer(S,_,X)},lt.useRef=function(S){return H.H.useRef(S)},lt.useState=function(S){return H.H.useState(S)},lt.useSyncExternalStore=function(S,_,X){return H.H.useSyncExternalStore(S,_,X)},lt.useTransition=function(){return H.H.useTransition()},lt.version="19.1.0",lt}var zm;function ac(){return zm||(zm=1,vr.exports=u1()),vr.exports}var P=ac(),br={exports:{}},ll={},xr={exports:{}},Sr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _m;function o1(){return _m||(_m=1,function(i){function l(C,U){var k=C.length;C.push(U);t:for(;0<k;){var pt=k-1>>>1,S=C[pt];if(0<c(S,U))C[pt]=U,C[k]=S,k=pt;else break t}}function u(C){return C.length===0?null:C[0]}function o(C){if(C.length===0)return null;var U=C[0],k=C.pop();if(k!==U){C[0]=k;t:for(var pt=0,S=C.length,_=S>>>1;pt<_;){var X=2*(pt+1)-1,Y=C[X],F=X+1,dt=C[F];if(0>c(Y,k))F<S&&0>c(dt,Y)?(C[pt]=dt,C[F]=k,pt=F):(C[pt]=Y,C[X]=k,pt=X);else if(F<S&&0>c(dt,k))C[pt]=dt,C[F]=k,pt=F;else break t}}return U}function c(C,U){var k=C.sortIndex-U.sortIndex;return k!==0?k:C.id-U.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;i.unstable_now=function(){return d.now()}}else{var h=Date,g=h.now();i.unstable_now=function(){return h.now()-g}}var y=[],m=[],v=1,x=null,A=3,z=!1,B=!1,Q=!1,G=!1,q=typeof setTimeout=="function"?setTimeout:null,K=typeof clearTimeout=="function"?clearTimeout:null,L=typeof setImmediate<"u"?setImmediate:null;function at(C){for(var U=u(m);U!==null;){if(U.callback===null)o(m);else if(U.startTime<=C)o(m),U.sortIndex=U.expirationTime,l(y,U);else break;U=u(m)}}function H(C){if(Q=!1,at(C),!B)if(u(y)!==null)B=!0,st||(st=!0,Yt());else{var U=u(m);U!==null&&Jt(H,U.startTime-C)}}var st=!1,ht=-1,$=5,Mt=-1;function qt(){return G?!0:!(i.unstable_now()-Mt<$)}function It(){if(G=!1,st){var C=i.unstable_now();Mt=C;var U=!0;try{t:{B=!1,Q&&(Q=!1,K(ht),ht=-1),z=!0;var k=A;try{e:{for(at(C),x=u(y);x!==null&&!(x.expirationTime>C&&qt());){var pt=x.callback;if(typeof pt=="function"){x.callback=null,A=x.priorityLevel;var S=pt(x.expirationTime<=C);if(C=i.unstable_now(),typeof S=="function"){x.callback=S,at(C),U=!0;break e}x===u(y)&&o(y),at(C)}else o(y);x=u(y)}if(x!==null)U=!0;else{var _=u(m);_!==null&&Jt(H,_.startTime-C),U=!1}}break t}finally{x=null,A=k,z=!1}U=void 0}}finally{U?Yt():st=!1}}}var Yt;if(typeof L=="function")Yt=function(){L(It)};else if(typeof MessageChannel<"u"){var Je=new MessageChannel,He=Je.port2;Je.port1.onmessage=It,Yt=function(){He.postMessage(null)}}else Yt=function(){q(It,0)};function Jt(C,U){ht=q(function(){C(i.unstable_now())},U)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(C){C.callback=null},i.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<C?Math.floor(1e3/C):5},i.unstable_getCurrentPriorityLevel=function(){return A},i.unstable_next=function(C){switch(A){case 1:case 2:case 3:var U=3;break;default:U=A}var k=A;A=U;try{return C()}finally{A=k}},i.unstable_requestPaint=function(){G=!0},i.unstable_runWithPriority=function(C,U){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var k=A;A=C;try{return U()}finally{A=k}},i.unstable_scheduleCallback=function(C,U,k){var pt=i.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?pt+k:pt):k=pt,C){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=k+S,C={id:v++,callback:U,priorityLevel:C,startTime:k,expirationTime:S,sortIndex:-1},k>pt?(C.sortIndex=k,l(m,C),u(y)===null&&C===u(m)&&(Q?(K(ht),ht=-1):Q=!0,Jt(H,k-pt))):(C.sortIndex=S,l(y,C),B||z||(B=!0,st||(st=!0,Yt()))),C},i.unstable_shouldYield=qt,i.unstable_wrapCallback=function(C){var U=A;return function(){var k=A;A=U;try{return C.apply(this,arguments)}finally{A=k}}}}(Sr)),Sr}var Um;function r1(){return Um||(Um=1,xr.exports=o1()),xr.exports}var Tr={exports:{}},ne={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function c1(){if(Bm)return ne;Bm=1;var i=ac();function l(y){var m="https://react.dev/errors/"+y;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(y,m,v){var x=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:x==null?null:""+x,children:y,containerInfo:m,implementation:v}}var h=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(y,m){if(y==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return ne.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,ne.createPortal=function(y,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(l(299));return d(y,m,null,v)},ne.flushSync=function(y){var m=h.T,v=o.p;try{if(h.T=null,o.p=2,y)return y()}finally{h.T=m,o.p=v,o.d.f()}},ne.preconnect=function(y,m){typeof y=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,o.d.C(y,m))},ne.prefetchDNS=function(y){typeof y=="string"&&o.d.D(y)},ne.preinit=function(y,m){if(typeof y=="string"&&m&&typeof m.as=="string"){var v=m.as,x=g(v,m.crossOrigin),A=typeof m.integrity=="string"?m.integrity:void 0,z=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?o.d.S(y,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:x,integrity:A,fetchPriority:z}):v==="script"&&o.d.X(y,{crossOrigin:x,integrity:A,fetchPriority:z,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},ne.preinitModule=function(y,m){if(typeof y=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=g(m.as,m.crossOrigin);o.d.M(y,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&o.d.M(y)},ne.preload=function(y,m){if(typeof y=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,x=g(v,m.crossOrigin);o.d.L(y,v,{crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},ne.preloadModule=function(y,m){if(typeof y=="string")if(m){var v=g(m.as,m.crossOrigin);o.d.m(y,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else o.d.m(y)},ne.requestFormReset=function(y){o.d.r(y)},ne.unstable_batchedUpdates=function(y,m){return y(m)},ne.useFormState=function(y,m,v){return h.H.useFormState(y,m,v)},ne.useFormStatus=function(){return h.H.useHostTransitionStatus()},ne.version="19.1.0",ne}var Hm;function f1(){if(Hm)return Tr.exports;Hm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),Tr.exports=c1(),Tr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lm;function h1(){if(Lm)return ll;Lm=1;var i=r1(),l=ac(),u=f1();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function h(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function g(t){if(d(t)!==t)throw Error(o(188))}function y(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,a=e;;){var s=n.return;if(s===null)break;var r=s.alternate;if(r===null){if(a=s.return,a!==null){n=a;continue}break}if(s.child===r.child){for(r=s.child;r;){if(r===n)return g(s),t;if(r===a)return g(s),e;r=r.sibling}throw Error(o(188))}if(n.return!==a.return)n=s,a=r;else{for(var f=!1,p=s.child;p;){if(p===n){f=!0,n=s,a=r;break}if(p===a){f=!0,a=s,n=r;break}p=p.sibling}if(!f){for(p=r.child;p;){if(p===n){f=!0,n=r,a=s;break}if(p===a){f=!0,a=r,n=s;break}p=p.sibling}if(!f)throw Error(o(189))}}if(n.alternate!==a)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,x=Symbol.for("react.element"),A=Symbol.for("react.transitional.element"),z=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),Q=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),K=Symbol.for("react.consumer"),L=Symbol.for("react.context"),at=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),st=Symbol.for("react.suspense_list"),ht=Symbol.for("react.memo"),$=Symbol.for("react.lazy"),Mt=Symbol.for("react.activity"),qt=Symbol.for("react.memo_cache_sentinel"),It=Symbol.iterator;function Yt(t){return t===null||typeof t!="object"?null:(t=It&&t[It]||t["@@iterator"],typeof t=="function"?t:null)}var Je=Symbol.for("react.client.reference");function He(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Je?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case G:return"Profiler";case Q:return"StrictMode";case H:return"Suspense";case st:return"SuspenseList";case Mt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case z:return"Portal";case L:return(t.displayName||"Context")+".Provider";case K:return(t._context.displayName||"Context")+".Consumer";case at:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ht:return e=t.displayName||null,e!==null?e:He(t.type)||"Memo";case $:e=t._payload,t=t._init;try{return He(t(e))}catch{}}return null}var Jt=Array.isArray,C=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k={pending:!1,data:null,method:null,action:null},pt=[],S=-1;function _(t){return{current:t}}function X(t){0>S||(t.current=pt[S],pt[S]=null,S--)}function Y(t,e){S++,pt[S]=t.current,t.current=e}var F=_(null),dt=_(null),it=_(null),he=_(null);function Et(t,e){switch(Y(it,e),Y(dt,t),Y(F,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?lm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=lm(e),t=sm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}X(F),Y(F,t)}function dn(){X(F),X(dt),X(it)}function eu(t){t.memoizedState!==null&&Y(he,t);var e=F.current,n=sm(e,t.type);e!==n&&(Y(dt,t),Y(F,n))}function Ml(t){dt.current===t&&(X(F),X(dt)),he.current===t&&(X(he),Ii._currentValue=k)}var nu=Object.prototype.hasOwnProperty,au=i.unstable_scheduleCallback,iu=i.unstable_cancelCallback,By=i.unstable_shouldYield,Hy=i.unstable_requestPaint,Le=i.unstable_now,Ly=i.unstable_getCurrentPriorityLevel,Hc=i.unstable_ImmediatePriority,Lc=i.unstable_UserBlockingPriority,El=i.unstable_NormalPriority,qy=i.unstable_LowPriority,qc=i.unstable_IdlePriority,Yy=i.log,Gy=i.unstable_setDisableYieldValue,ui=null,de=null;function mn(t){if(typeof Yy=="function"&&Gy(t),de&&typeof de.setStrictMode=="function")try{de.setStrictMode(ui,t)}catch{}}var me=Math.clz32?Math.clz32:Zy,Xy=Math.log,Qy=Math.LN2;function Zy(t){return t>>>=0,t===0?32:31-(Xy(t)/Qy|0)|0}var Dl=256,Rl=4194304;function Gn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Nl(t,e,n){var a=t.pendingLanes;if(a===0)return 0;var s=0,r=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var p=a&134217727;return p!==0?(a=p&~r,a!==0?s=Gn(a):(f&=p,f!==0?s=Gn(f):n||(n=p&~t,n!==0&&(s=Gn(n))))):(p=a&~r,p!==0?s=Gn(p):f!==0?s=Gn(f):n||(n=a&~t,n!==0&&(s=Gn(n)))),s===0?0:e!==0&&e!==s&&(e&r)===0&&(r=s&-s,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:s}function oi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Ky(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Yc(){var t=Dl;return Dl<<=1,(Dl&4194048)===0&&(Dl=256),t}function Gc(){var t=Rl;return Rl<<=1,(Rl&62914560)===0&&(Rl=4194304),t}function lu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ri(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function ky(t,e,n,a,s,r){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var p=t.entanglements,b=t.expirationTimes,R=t.hiddenUpdates;for(n=f&~n;0<n;){var O=31-me(n),V=1<<O;p[O]=0,b[O]=-1;var N=R[O];if(N!==null)for(R[O]=null,O=0;O<N.length;O++){var w=N[O];w!==null&&(w.lane&=-536870913)}n&=~V}a!==0&&Xc(t,a,0),r!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=r&~(f&~e))}function Xc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-me(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|n&4194090}function Qc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var a=31-me(n),s=1<<a;s&e|t[a]&e&&(t[a]|=e),n&=~s}}function su(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function uu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Zc(){var t=U.p;return t!==0?t:(t=window.event,t===void 0?32:Em(t.type))}function Jy(t,e){var n=U.p;try{return U.p=t,e()}finally{U.p=n}}var pn=Math.random().toString(36).slice(2),te="__reactFiber$"+pn,se="__reactProps$"+pn,ha="__reactContainer$"+pn,ou="__reactEvents$"+pn,Py="__reactListeners$"+pn,Fy="__reactHandles$"+pn,Kc="__reactResources$"+pn,ci="__reactMarker$"+pn;function ru(t){delete t[te],delete t[se],delete t[ou],delete t[Py],delete t[Fy]}function da(t){var e=t[te];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ha]||n[te]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=cm(t);t!==null;){if(n=t[te])return n;t=cm(t)}return e}t=n,n=t.parentNode}return null}function ma(t){if(t=t[te]||t[ha]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function pa(t){var e=t[Kc];return e||(e=t[Kc]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Qt(t){t[ci]=!0}var kc=new Set,Jc={};function Xn(t,e){ya(t,e),ya(t+"Capture",e)}function ya(t,e){for(Jc[t]=e,t=0;t<e.length;t++)kc.add(e[t])}var Wy=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Pc={},Fc={};function $y(t){return nu.call(Fc,t)?!0:nu.call(Pc,t)?!1:Wy.test(t)?Fc[t]=!0:(Pc[t]=!0,!1)}function wl(t,e,n){if($y(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ol(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Pe(t,e,n,a){if(a===null)t.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+a)}}var cu,Wc;function ga(t){if(cu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);cu=e&&e[1]||"",Wc=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cu+t+Wc}var fu=!1;function hu(t,e){if(!t||fu)return"";fu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(w){var N=w}Reflect.construct(t,[],V)}else{try{V.call()}catch(w){N=w}t.call(V.prototype)}}else{try{throw Error()}catch(w){N=w}(V=t())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(w){if(w&&N&&typeof w.stack=="string")return[w.stack,N.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=a.DetermineComponentFrameRoot(),f=r[0],p=r[1];if(f&&p){var b=f.split(`
`),R=p.split(`
`);for(s=a=0;a<b.length&&!b[a].includes("DetermineComponentFrameRoot");)a++;for(;s<R.length&&!R[s].includes("DetermineComponentFrameRoot");)s++;if(a===b.length||s===R.length)for(a=b.length-1,s=R.length-1;1<=a&&0<=s&&b[a]!==R[s];)s--;for(;1<=a&&0<=s;a--,s--)if(b[a]!==R[s]){if(a!==1||s!==1)do if(a--,s--,0>s||b[a]!==R[s]){var O=`
`+b[a].replace(" at new "," at ");return t.displayName&&O.includes("<anonymous>")&&(O=O.replace("<anonymous>",t.displayName)),O}while(1<=a&&0<=s);break}}}finally{fu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ga(n):""}function Iy(t){switch(t.tag){case 26:case 27:case 5:return ga(t.type);case 16:return ga("Lazy");case 13:return ga("Suspense");case 19:return ga("SuspenseList");case 0:case 15:return hu(t.type,!1);case 11:return hu(t.type.render,!1);case 1:return hu(t.type,!0);case 31:return ga("Activity");default:return""}}function $c(t){try{var e="";do e+=Iy(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ic(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function tg(t){var e=Ic(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(f){a=""+f,r.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Cl(t){t._valueTracker||(t._valueTracker=tg(t))}function tf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),a="";return t&&(a=Ic(t)?t.checked?"true":"false":t.value),t=a,t!==n?(e.setValue(t),!0):!1}function jl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var eg=/[\n"\\]/g;function Ae(t){return t.replace(eg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function du(t,e,n,a,s,r,f,p){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?mu(t,f,Te(e)):n!=null?mu(t,f,Te(n)):a!=null&&t.removeAttribute("value"),s==null&&r!=null&&(t.defaultChecked=!!r),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?t.name=""+Te(p):t.removeAttribute("name")}function ef(t,e,n,a,s,r,f,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,p||e===t.value||(t.value=e),t.defaultValue=e}a=a??s,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=p?t.checked:!!a,t.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function mu(t,e,n){e==="number"&&jl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function va(t,e,n,a){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&a&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,a&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function nf(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function af(t,e,n,a){if(e==null){if(a!=null){if(n!=null)throw Error(o(92));if(Jt(a)){if(1<a.length)throw Error(o(93));a=a[0]}n=a}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,a=t.textContent,a===n&&a!==""&&a!==null&&(t.value=a)}function ba(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var ng=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function lf(t,e,n){var a=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,n):typeof n!="number"||n===0||ng.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function sf(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var s in e)a=e[s],e.hasOwnProperty(s)&&n[s]!==a&&lf(t,s,a)}else for(var r in e)e.hasOwnProperty(r)&&lf(t,r,e[r])}function pu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ag=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ig=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Vl(t){return ig.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var yu=null;function gu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var xa=null,Sa=null;function uf(t){var e=ma(t);if(e&&(t=e.stateNode)){var n=t[se]||null;t:switch(t=e.stateNode,e.type){case"input":if(du(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ae(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var a=n[e];if(a!==t&&a.form===t.form){var s=a[se]||null;if(!s)throw Error(o(90));du(a,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)a=n[e],a.form===t.form&&tf(a)}break t;case"textarea":nf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&va(t,!!n.multiple,e,!1)}}}var vu=!1;function of(t,e,n){if(vu)return t(e,n);vu=!0;try{var a=t(e);return a}finally{if(vu=!1,(xa!==null||Sa!==null)&&(vs(),xa&&(e=xa,t=Sa,Sa=xa=null,uf(e),t)))for(e=0;e<t.length;e++)uf(t[e])}}function hi(t,e){var n=t.stateNode;if(n===null)return null;var a=n[se]||null;if(a===null)return null;n=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var Fe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bu=!1;if(Fe)try{var di={};Object.defineProperty(di,"passive",{get:function(){bu=!0}}),window.addEventListener("test",di,di),window.removeEventListener("test",di,di)}catch{bu=!1}var yn=null,xu=null,zl=null;function rf(){if(zl)return zl;var t,e=xu,n=e.length,a,s="value"in yn?yn.value:yn.textContent,r=s.length;for(t=0;t<n&&e[t]===s[t];t++);var f=n-t;for(a=1;a<=f&&e[n-a]===s[r-a];a++);return zl=s.slice(t,1<a?1-a:void 0)}function _l(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ul(){return!0}function cf(){return!1}function ue(t){function e(n,a,s,r,f){this._reactName=n,this._targetInst=s,this.type=a,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var p in t)t.hasOwnProperty(p)&&(n=t[p],this[p]=n?n(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ul:cf,this.isPropagationStopped=cf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ul)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ul)},persist:function(){},isPersistent:Ul}),e}var Qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bl=ue(Qn),mi=v({},Qn,{view:0,detail:0}),lg=ue(mi),Su,Tu,pi,Hl=v({},mi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==pi&&(pi&&t.type==="mousemove"?(Su=t.screenX-pi.screenX,Tu=t.screenY-pi.screenY):Tu=Su=0,pi=t),Su)},movementY:function(t){return"movementY"in t?t.movementY:Tu}}),ff=ue(Hl),sg=v({},Hl,{dataTransfer:0}),ug=ue(sg),og=v({},mi,{relatedTarget:0}),Au=ue(og),rg=v({},Qn,{animationName:0,elapsedTime:0,pseudoElement:0}),cg=ue(rg),fg=v({},Qn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),hg=ue(fg),dg=v({},Qn,{data:0}),hf=ue(dg),mg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},pg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},yg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gg(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=yg[t])?!!e[t]:!1}function Mu(){return gg}var vg=v({},mi,{key:function(t){if(t.key){var e=mg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=_l(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?pg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mu,charCode:function(t){return t.type==="keypress"?_l(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?_l(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),bg=ue(vg),xg=v({},Hl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),df=ue(xg),Sg=v({},mi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mu}),Tg=ue(Sg),Ag=v({},Qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Mg=ue(Ag),Eg=v({},Hl,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Dg=ue(Eg),Rg=v({},Qn,{newState:0,oldState:0}),Ng=ue(Rg),wg=[9,13,27,32],Eu=Fe&&"CompositionEvent"in window,yi=null;Fe&&"documentMode"in document&&(yi=document.documentMode);var Og=Fe&&"TextEvent"in window&&!yi,mf=Fe&&(!Eu||yi&&8<yi&&11>=yi),pf=" ",yf=!1;function gf(t,e){switch(t){case"keyup":return wg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ta=!1;function Cg(t,e){switch(t){case"compositionend":return vf(e);case"keypress":return e.which!==32?null:(yf=!0,pf);case"textInput":return t=e.data,t===pf&&yf?null:t;default:return null}}function jg(t,e){if(Ta)return t==="compositionend"||!Eu&&gf(t,e)?(t=rf(),zl=xu=yn=null,Ta=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return mf&&e.locale!=="ko"?null:e.data;default:return null}}var Vg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Vg[t.type]:e==="textarea"}function xf(t,e,n,a){xa?Sa?Sa.push(a):Sa=[a]:xa=a,e=Ms(e,"onChange"),0<e.length&&(n=new Bl("onChange","change",null,n,a),t.push({event:n,listeners:e}))}var gi=null,vi=null;function zg(t){tm(t,0)}function Ll(t){var e=fi(t);if(tf(e))return t}function Sf(t,e){if(t==="change")return e}var Tf=!1;if(Fe){var Du;if(Fe){var Ru="oninput"in document;if(!Ru){var Af=document.createElement("div");Af.setAttribute("oninput","return;"),Ru=typeof Af.oninput=="function"}Du=Ru}else Du=!1;Tf=Du&&(!document.documentMode||9<document.documentMode)}function Mf(){gi&&(gi.detachEvent("onpropertychange",Ef),vi=gi=null)}function Ef(t){if(t.propertyName==="value"&&Ll(vi)){var e=[];xf(e,vi,t,gu(t)),of(zg,e)}}function _g(t,e,n){t==="focusin"?(Mf(),gi=e,vi=n,gi.attachEvent("onpropertychange",Ef)):t==="focusout"&&Mf()}function Ug(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Ll(vi)}function Bg(t,e){if(t==="click")return Ll(e)}function Hg(t,e){if(t==="input"||t==="change")return Ll(e)}function Lg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var pe=typeof Object.is=="function"?Object.is:Lg;function bi(t,e){if(pe(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),a=Object.keys(e);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var s=n[a];if(!nu.call(e,s)||!pe(t[s],e[s]))return!1}return!0}function Df(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Rf(t,e){var n=Df(t);t=0;for(var a;n;){if(n.nodeType===3){if(a=t+n.textContent.length,t<=e&&a>=e)return{node:n,offset:e-t};t=a}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Df(n)}}function Nf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Nf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function wf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=jl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=jl(t.document)}return e}function Nu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var qg=Fe&&"documentMode"in document&&11>=document.documentMode,Aa=null,wu=null,xi=null,Ou=!1;function Of(t,e,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ou||Aa==null||Aa!==jl(a)||(a=Aa,"selectionStart"in a&&Nu(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),xi&&bi(xi,a)||(xi=a,a=Ms(wu,"onSelect"),0<a.length&&(e=new Bl("onSelect","select",null,e,n),t.push({event:e,listeners:a}),e.target=Aa)))}function Zn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ma={animationend:Zn("Animation","AnimationEnd"),animationiteration:Zn("Animation","AnimationIteration"),animationstart:Zn("Animation","AnimationStart"),transitionrun:Zn("Transition","TransitionRun"),transitionstart:Zn("Transition","TransitionStart"),transitioncancel:Zn("Transition","TransitionCancel"),transitionend:Zn("Transition","TransitionEnd")},Cu={},Cf={};Fe&&(Cf=document.createElement("div").style,"AnimationEvent"in window||(delete Ma.animationend.animation,delete Ma.animationiteration.animation,delete Ma.animationstart.animation),"TransitionEvent"in window||delete Ma.transitionend.transition);function Kn(t){if(Cu[t])return Cu[t];if(!Ma[t])return t;var e=Ma[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Cf)return Cu[t]=e[n];return t}var jf=Kn("animationend"),Vf=Kn("animationiteration"),zf=Kn("animationstart"),Yg=Kn("transitionrun"),Gg=Kn("transitionstart"),Xg=Kn("transitioncancel"),_f=Kn("transitionend"),Uf=new Map,ju="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ju.push("scrollEnd");function ze(t,e){Uf.set(t,e),Xn(e,[t])}var Bf=new WeakMap;function Me(t,e){if(typeof t=="object"&&t!==null){var n=Bf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:$c(e)},Bf.set(t,e),e)}return{value:t,source:e,stack:$c(e)}}var Ee=[],Ea=0,Vu=0;function ql(){for(var t=Ea,e=Vu=Ea=0;e<t;){var n=Ee[e];Ee[e++]=null;var a=Ee[e];Ee[e++]=null;var s=Ee[e];Ee[e++]=null;var r=Ee[e];if(Ee[e++]=null,a!==null&&s!==null){var f=a.pending;f===null?s.next=s:(s.next=f.next,f.next=s),a.pending=s}r!==0&&Hf(n,s,r)}}function Yl(t,e,n,a){Ee[Ea++]=t,Ee[Ea++]=e,Ee[Ea++]=n,Ee[Ea++]=a,Vu|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function zu(t,e,n,a){return Yl(t,e,n,a),Gl(t)}function Da(t,e){return Yl(t,null,null,e),Gl(t)}function Hf(t,e,n){t.lanes|=n;var a=t.alternate;a!==null&&(a.lanes|=n);for(var s=!1,r=t.return;r!==null;)r.childLanes|=n,a=r.alternate,a!==null&&(a.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(s=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,s&&e!==null&&(s=31-me(n),t=r.hiddenUpdates,a=t[s],a===null?t[s]=[e]:a.push(e),e.lane=n|536870912),r):null}function Gl(t){if(50<Zi)throw Zi=0,Yo=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ra={};function Qg(t,e,n,a){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ye(t,e,n,a){return new Qg(t,e,n,a)}function _u(t){return t=t.prototype,!(!t||!t.isReactComponent)}function We(t,e){var n=t.alternate;return n===null?(n=ye(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Lf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xl(t,e,n,a,s,r){var f=0;if(a=t,typeof t=="function")_u(t)&&(f=1);else if(typeof t=="string")f=Kv(t,n,F.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Mt:return t=ye(31,n,e,s),t.elementType=Mt,t.lanes=r,t;case B:return kn(n.children,s,r,e);case Q:f=8,s|=24;break;case G:return t=ye(12,n,e,s|2),t.elementType=G,t.lanes=r,t;case H:return t=ye(13,n,e,s),t.elementType=H,t.lanes=r,t;case st:return t=ye(19,n,e,s),t.elementType=st,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case q:case L:f=10;break t;case K:f=9;break t;case at:f=11;break t;case ht:f=14;break t;case $:f=16,a=null;break t}f=29,n=Error(o(130,t===null?"null":typeof t,"")),a=null}return e=ye(f,n,e,s),e.elementType=t,e.type=a,e.lanes=r,e}function kn(t,e,n,a){return t=ye(7,t,a,e),t.lanes=n,t}function Uu(t,e,n){return t=ye(6,t,null,e),t.lanes=n,t}function Bu(t,e,n){return e=ye(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Na=[],wa=0,Ql=null,Zl=0,De=[],Re=0,Jn=null,$e=1,Ie="";function Pn(t,e){Na[wa++]=Zl,Na[wa++]=Ql,Ql=t,Zl=e}function qf(t,e,n){De[Re++]=$e,De[Re++]=Ie,De[Re++]=Jn,Jn=t;var a=$e;t=Ie;var s=32-me(a)-1;a&=~(1<<s),n+=1;var r=32-me(e)+s;if(30<r){var f=s-s%5;r=(a&(1<<f)-1).toString(32),a>>=f,s-=f,$e=1<<32-me(e)+s|n<<s|a,Ie=r+t}else $e=1<<r|n<<s|a,Ie=t}function Hu(t){t.return!==null&&(Pn(t,1),qf(t,1,0))}function Lu(t){for(;t===Ql;)Ql=Na[--wa],Na[wa]=null,Zl=Na[--wa],Na[wa]=null;for(;t===Jn;)Jn=De[--Re],De[Re]=null,Ie=De[--Re],De[Re]=null,$e=De[--Re],De[Re]=null}var ie=null,Ot=null,yt=!1,Fn=null,qe=!1,qu=Error(o(519));function Wn(t){var e=Error(o(418,""));throw Ai(Me(e,t)),qu}function Yf(t){var e=t.stateNode,n=t.type,a=t.memoizedProps;switch(e[te]=t,e[se]=a,n){case"dialog":ct("cancel",e),ct("close",e);break;case"iframe":case"object":case"embed":ct("load",e);break;case"video":case"audio":for(n=0;n<ki.length;n++)ct(ki[n],e);break;case"source":ct("error",e);break;case"img":case"image":case"link":ct("error",e),ct("load",e);break;case"details":ct("toggle",e);break;case"input":ct("invalid",e),ef(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Cl(e);break;case"select":ct("invalid",e);break;case"textarea":ct("invalid",e),af(e,a.value,a.defaultValue,a.children),Cl(e)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||a.suppressHydrationWarning===!0||im(e.textContent,n)?(a.popover!=null&&(ct("beforetoggle",e),ct("toggle",e)),a.onScroll!=null&&ct("scroll",e),a.onScrollEnd!=null&&ct("scrollend",e),a.onClick!=null&&(e.onclick=Es),e=!0):e=!1,e||Wn(t)}function Gf(t){for(ie=t.return;ie;)switch(ie.tag){case 5:case 13:qe=!1;return;case 27:case 3:qe=!0;return;default:ie=ie.return}}function Si(t){if(t!==ie)return!1;if(!yt)return Gf(t),yt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ar(t.type,t.memoizedProps)),n=!n),n&&Ot&&Wn(t),Gf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Ot=Ue(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Ot=null}}else e===27?(e=Ot,jn(t.type)?(t=ur,ur=null,Ot=t):Ot=e):Ot=ie?Ue(t.stateNode.nextSibling):null;return!0}function Ti(){Ot=ie=null,yt=!1}function Xf(){var t=Fn;return t!==null&&(ce===null?ce=t:ce.push.apply(ce,t),Fn=null),t}function Ai(t){Fn===null?Fn=[t]:Fn.push(t)}var Yu=_(null),$n=null,tn=null;function gn(t,e,n){Y(Yu,e._currentValue),e._currentValue=n}function en(t){t._currentValue=Yu.current,X(Yu)}function Gu(t,e,n){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===n)break;t=t.return}}function Xu(t,e,n,a){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var r=s.dependencies;if(r!==null){var f=s.child;r=r.firstContext;t:for(;r!==null;){var p=r;r=s;for(var b=0;b<e.length;b++)if(p.context===e[b]){r.lanes|=n,p=r.alternate,p!==null&&(p.lanes|=n),Gu(r.return,n,t),a||(f=null);break t}r=p.next}}else if(s.tag===18){if(f=s.return,f===null)throw Error(o(341));f.lanes|=n,r=f.alternate,r!==null&&(r.lanes|=n),Gu(f,n,t),f=null}else f=s.child;if(f!==null)f.return=s;else for(f=s;f!==null;){if(f===t){f=null;break}if(s=f.sibling,s!==null){s.return=f.return,f=s;break}f=f.return}s=f}}function Mi(t,e,n,a){t=null;for(var s=e,r=!1;s!==null;){if(!r){if((s.flags&524288)!==0)r=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var f=s.alternate;if(f===null)throw Error(o(387));if(f=f.memoizedProps,f!==null){var p=s.type;pe(s.pendingProps.value,f.value)||(t!==null?t.push(p):t=[p])}}else if(s===he.current){if(f=s.alternate,f===null)throw Error(o(387));f.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(Ii):t=[Ii])}s=s.return}t!==null&&Xu(e,t,n,a),e.flags|=262144}function Kl(t){for(t=t.firstContext;t!==null;){if(!pe(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function In(t){$n=t,tn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ee(t){return Qf($n,t)}function kl(t,e){return $n===null&&In(t),Qf(t,e)}function Qf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},tn===null){if(t===null)throw Error(o(308));tn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else tn=tn.next=e;return n}var Zg=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Kg=i.unstable_scheduleCallback,kg=i.unstable_NormalPriority,Gt={$$typeof:L,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Qu(){return{controller:new Zg,data:new Map,refCount:0}}function Ei(t){t.refCount--,t.refCount===0&&Kg(kg,function(){t.controller.abort()})}var Di=null,Zu=0,Oa=0,Ca=null;function Jg(t,e){if(Di===null){var n=Di=[];Zu=0,Oa=Jo(),Ca={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Zu++,e.then(Zf,Zf),e}function Zf(){if(--Zu===0&&Di!==null){Ca!==null&&(Ca.status="fulfilled");var t=Di;Di=null,Oa=0,Ca=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Pg(t,e){var n=[],a={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(a.status="rejected",a.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),a}var Kf=C.S;C.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Jg(t,e),Kf!==null&&Kf(t,e)};var ta=_(null);function Ku(){var t=ta.current;return t!==null?t:At.pooledCache}function Jl(t,e){e===null?Y(ta,ta.current):Y(ta,e.pool)}function kf(){var t=Ku();return t===null?null:{parent:Gt._currentValue,pool:t}}var Ri=Error(o(460)),Jf=Error(o(474)),Pl=Error(o(542)),ku={then:function(){}};function Pf(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Fl(){}function Ff(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Fl,Fl),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,$f(t),t;default:if(typeof e.status=="string")e.then(Fl,Fl);else{if(t=At,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=a}},function(a){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,$f(t),t}throw Ni=e,Ri}}var Ni=null;function Wf(){if(Ni===null)throw Error(o(459));var t=Ni;return Ni=null,t}function $f(t){if(t===Ri||t===Pl)throw Error(o(483))}var vn=!1;function Ju(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function bn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function xn(t,e,n){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(gt&2)!==0){var s=a.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),a.pending=e,e=Gl(t),Hf(t,null,n),e}return Yl(t,a,e,n),Gl(t)}function wi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Qc(t,n)}}function Fu(t,e){var n=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var s=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?s=r=f:r=r.next=f,n=n.next}while(n!==null);r===null?s=r=e:r=r.next=e}else s=r=e;n={baseState:a.baseState,firstBaseUpdate:s,lastBaseUpdate:r,shared:a.shared,callbacks:a.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Wu=!1;function Oi(){if(Wu){var t=Ca;if(t!==null)throw t}}function Ci(t,e,n,a){Wu=!1;var s=t.updateQueue;vn=!1;var r=s.firstBaseUpdate,f=s.lastBaseUpdate,p=s.shared.pending;if(p!==null){s.shared.pending=null;var b=p,R=b.next;b.next=null,f===null?r=R:f.next=R,f=b;var O=t.alternate;O!==null&&(O=O.updateQueue,p=O.lastBaseUpdate,p!==f&&(p===null?O.firstBaseUpdate=R:p.next=R,O.lastBaseUpdate=b))}if(r!==null){var V=s.baseState;f=0,O=R=b=null,p=r;do{var N=p.lane&-536870913,w=N!==p.lane;if(w?(ft&N)===N:(a&N)===N){N!==0&&N===Oa&&(Wu=!0),O!==null&&(O=O.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});t:{var et=t,W=p;N=e;var St=n;switch(W.tag){case 1:if(et=W.payload,typeof et=="function"){V=et.call(St,V,N);break t}V=et;break t;case 3:et.flags=et.flags&-65537|128;case 0:if(et=W.payload,N=typeof et=="function"?et.call(St,V,N):et,N==null)break t;V=v({},V,N);break t;case 2:vn=!0}}N=p.callback,N!==null&&(t.flags|=64,w&&(t.flags|=8192),w=s.callbacks,w===null?s.callbacks=[N]:w.push(N))}else w={lane:N,tag:p.tag,payload:p.payload,callback:p.callback,next:null},O===null?(R=O=w,b=V):O=O.next=w,f|=N;if(p=p.next,p===null){if(p=s.shared.pending,p===null)break;w=p,p=w.next,w.next=null,s.lastBaseUpdate=w,s.shared.pending=null}}while(!0);O===null&&(b=V),s.baseState=b,s.firstBaseUpdate=R,s.lastBaseUpdate=O,r===null&&(s.shared.lanes=0),Nn|=f,t.lanes=f,t.memoizedState=V}}function If(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function th(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)If(n[t],e)}var ja=_(null),Wl=_(0);function eh(t,e){t=rn,Y(Wl,t),Y(ja,e),rn=t|e.baseLanes}function $u(){Y(Wl,rn),Y(ja,ja.current)}function Iu(){rn=Wl.current,X(ja),X(Wl)}var Sn=0,ut=null,bt=null,Bt=null,$l=!1,Va=!1,ea=!1,Il=0,ji=0,za=null,Fg=0;function Vt(){throw Error(o(321))}function to(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!pe(t[n],e[n]))return!1;return!0}function eo(t,e,n,a,s,r){return Sn=r,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,C.H=t===null||t.memoizedState===null?Hh:Lh,ea=!1,r=n(a,s),ea=!1,Va&&(r=ah(e,n,a,s)),nh(t),r}function nh(t){C.H=ls;var e=bt!==null&&bt.next!==null;if(Sn=0,Bt=bt=ut=null,$l=!1,ji=0,za=null,e)throw Error(o(300));t===null||Zt||(t=t.dependencies,t!==null&&Kl(t)&&(Zt=!0))}function ah(t,e,n,a){ut=t;var s=0;do{if(Va&&(za=null),ji=0,Va=!1,25<=s)throw Error(o(301));if(s+=1,Bt=bt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}C.H=av,r=e(n,a)}while(Va);return r}function Wg(){var t=C.H,e=t.useState()[0];return e=typeof e.then=="function"?Vi(e):e,t=t.useState()[0],(bt!==null?bt.memoizedState:null)!==t&&(ut.flags|=1024),e}function no(){var t=Il!==0;return Il=0,t}function ao(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function io(t){if($l){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}$l=!1}Sn=0,Bt=bt=ut=null,Va=!1,ji=Il=0,za=null}function oe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t,Bt}function Ht(){if(bt===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=bt.next;var e=Bt===null?ut.memoizedState:Bt.next;if(e!==null)Bt=e,bt=t;else{if(t===null)throw ut.alternate===null?Error(o(467)):Error(o(310));bt=t,t={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t}return Bt}function lo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Vi(t){var e=ji;return ji+=1,za===null&&(za=[]),t=Ff(za,t,e),e=ut,(Bt===null?e.memoizedState:Bt.next)===null&&(e=e.alternate,C.H=e===null||e.memoizedState===null?Hh:Lh),t}function ts(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Vi(t);if(t.$$typeof===L)return ee(t)}throw Error(o(438,String(t)))}function so(t){var e=null,n=ut.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var a=ut.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=lo(),ut.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),a=0;a<t;a++)n[a]=qt;return e.index++,n}function nn(t,e){return typeof e=="function"?e(t):e}function es(t){var e=Ht();return uo(e,bt,t)}function uo(t,e,n){var a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=n;var s=t.baseQueue,r=a.pending;if(r!==null){if(s!==null){var f=s.next;s.next=r.next,r.next=f}e.baseQueue=s=r,a.pending=null}if(r=t.baseState,s===null)t.memoizedState=r;else{e=s.next;var p=f=null,b=null,R=e,O=!1;do{var V=R.lane&-536870913;if(V!==R.lane?(ft&V)===V:(Sn&V)===V){var N=R.revertLane;if(N===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),V===Oa&&(O=!0);else if((Sn&N)===N){R=R.next,N===Oa&&(O=!0);continue}else V={lane:0,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},b===null?(p=b=V,f=r):b=b.next=V,ut.lanes|=N,Nn|=N;V=R.action,ea&&n(r,V),r=R.hasEagerState?R.eagerState:n(r,V)}else N={lane:V,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},b===null?(p=b=N,f=r):b=b.next=N,ut.lanes|=V,Nn|=V;R=R.next}while(R!==null&&R!==e);if(b===null?f=r:b.next=p,!pe(r,t.memoizedState)&&(Zt=!0,O&&(n=Ca,n!==null)))throw n;t.memoizedState=r,t.baseState=f,t.baseQueue=b,a.lastRenderedState=r}return s===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function oo(t){var e=Ht(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var a=n.dispatch,s=n.pending,r=e.memoizedState;if(s!==null){n.pending=null;var f=s=s.next;do r=t(r,f.action),f=f.next;while(f!==s);pe(r,e.memoizedState)||(Zt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,a]}function ih(t,e,n){var a=ut,s=Ht(),r=yt;if(r){if(n===void 0)throw Error(o(407));n=n()}else n=e();var f=!pe((bt||s).memoizedState,n);f&&(s.memoizedState=n,Zt=!0),s=s.queue;var p=uh.bind(null,a,s,t);if(zi(2048,8,p,[t]),s.getSnapshot!==e||f||Bt!==null&&Bt.memoizedState.tag&1){if(a.flags|=2048,_a(9,ns(),sh.bind(null,a,s,n,e),null),At===null)throw Error(o(349));r||(Sn&124)!==0||lh(a,e,n)}return n}function lh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ut.updateQueue,e===null?(e=lo(),ut.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function sh(t,e,n,a){e.value=n,e.getSnapshot=a,oh(e)&&rh(t)}function uh(t,e,n){return n(function(){oh(e)&&rh(t)})}function oh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!pe(t,n)}catch{return!0}}function rh(t){var e=Da(t,2);e!==null&&Se(e,t,2)}function ro(t){var e=oe();if(typeof t=="function"){var n=t;if(t=n(),ea){mn(!0);try{n()}finally{mn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:t},e}function ch(t,e,n,a){return t.baseState=n,uo(t,bt,typeof a=="function"?a:nn)}function $g(t,e,n,a,s){if(is(t))throw Error(o(485));if(t=e.action,t!==null){var r={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};C.T!==null?n(!0):r.isTransition=!1,a(r),n=e.pending,n===null?(r.next=e.pending=r,fh(e,r)):(r.next=n.next,e.pending=n.next=r)}}function fh(t,e){var n=e.action,a=e.payload,s=t.state;if(e.isTransition){var r=C.T,f={};C.T=f;try{var p=n(s,a),b=C.S;b!==null&&b(f,p),hh(t,e,p)}catch(R){co(t,e,R)}finally{C.T=r}}else try{r=n(s,a),hh(t,e,r)}catch(R){co(t,e,R)}}function hh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){dh(t,e,a)},function(a){return co(t,e,a)}):dh(t,e,n)}function dh(t,e,n){e.status="fulfilled",e.value=n,mh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,fh(t,n)))}function co(t,e,n){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=n,mh(e),e=e.next;while(e!==a)}t.action=null}function mh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function ph(t,e){return e}function yh(t,e){if(yt){var n=At.formState;if(n!==null){t:{var a=ut;if(yt){if(Ot){e:{for(var s=Ot,r=qe;s.nodeType!==8;){if(!r){s=null;break e}if(s=Ue(s.nextSibling),s===null){s=null;break e}}r=s.data,s=r==="F!"||r==="F"?s:null}if(s){Ot=Ue(s.nextSibling),a=s.data==="F!";break t}}Wn(a)}a=!1}a&&(e=n[0])}}return n=oe(),n.memoizedState=n.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ph,lastRenderedState:e},n.queue=a,n=_h.bind(null,ut,a),a.dispatch=n,a=ro(!1),r=yo.bind(null,ut,!1,a.queue),a=oe(),s={state:e,dispatch:null,action:t,pending:null},a.queue=s,n=$g.bind(null,ut,s,r,n),s.dispatch=n,a.memoizedState=t,[e,n,!1]}function gh(t){var e=Ht();return vh(e,bt,t)}function vh(t,e,n){if(e=uo(t,e,ph)[0],t=es(nn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Vi(e)}catch(f){throw f===Ri?Pl:f}else a=e;e=Ht();var s=e.queue,r=s.dispatch;return n!==e.memoizedState&&(ut.flags|=2048,_a(9,ns(),Ig.bind(null,s,n),null)),[a,r,t]}function Ig(t,e){t.action=e}function bh(t){var e=Ht(),n=bt;if(n!==null)return vh(e,n,t);Ht(),e=e.memoizedState,n=Ht();var a=n.queue.dispatch;return n.memoizedState=t,[e,a,!1]}function _a(t,e,n,a){return t={tag:t,create:n,deps:a,inst:e,next:null},e=ut.updateQueue,e===null&&(e=lo(),ut.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(a=n.next,n.next=t,t.next=a,e.lastEffect=t),t}function ns(){return{destroy:void 0,resource:void 0}}function xh(){return Ht().memoizedState}function as(t,e,n,a){var s=oe();a=a===void 0?null:a,ut.flags|=t,s.memoizedState=_a(1|e,ns(),n,a)}function zi(t,e,n,a){var s=Ht();a=a===void 0?null:a;var r=s.memoizedState.inst;bt!==null&&a!==null&&to(a,bt.memoizedState.deps)?s.memoizedState=_a(e,r,n,a):(ut.flags|=t,s.memoizedState=_a(1|e,r,n,a))}function Sh(t,e){as(8390656,8,t,e)}function Th(t,e){zi(2048,8,t,e)}function Ah(t,e){return zi(4,2,t,e)}function Mh(t,e){return zi(4,4,t,e)}function Eh(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Dh(t,e,n){n=n!=null?n.concat([t]):null,zi(4,4,Eh.bind(null,e,t),n)}function fo(){}function Rh(t,e){var n=Ht();e=e===void 0?null:e;var a=n.memoizedState;return e!==null&&to(e,a[1])?a[0]:(n.memoizedState=[t,e],t)}function Nh(t,e){var n=Ht();e=e===void 0?null:e;var a=n.memoizedState;if(e!==null&&to(e,a[1]))return a[0];if(a=t(),ea){mn(!0);try{t()}finally{mn(!1)}}return n.memoizedState=[a,e],a}function ho(t,e,n){return n===void 0||(Sn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Cd(),ut.lanes|=t,Nn|=t,n)}function wh(t,e,n,a){return pe(n,e)?n:ja.current!==null?(t=ho(t,n,a),pe(t,e)||(Zt=!0),t):(Sn&42)===0?(Zt=!0,t.memoizedState=n):(t=Cd(),ut.lanes|=t,Nn|=t,e)}function Oh(t,e,n,a,s){var r=U.p;U.p=r!==0&&8>r?r:8;var f=C.T,p={};C.T=p,yo(t,!1,e,n);try{var b=s(),R=C.S;if(R!==null&&R(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var O=Pg(b,a);_i(t,e,O,xe(t))}else _i(t,e,a,xe(t))}catch(V){_i(t,e,{then:function(){},status:"rejected",reason:V},xe())}finally{U.p=r,C.T=f}}function tv(){}function mo(t,e,n,a){if(t.tag!==5)throw Error(o(476));var s=Ch(t).queue;Oh(t,s,e,k,n===null?tv:function(){return jh(t),n(a)})}function Ch(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:k,baseState:k,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:k},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:nn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function jh(t){var e=Ch(t).next.queue;_i(t,e,{},xe())}function po(){return ee(Ii)}function Vh(){return Ht().memoizedState}function zh(){return Ht().memoizedState}function ev(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=xe();t=bn(n);var a=xn(e,t,n);a!==null&&(Se(a,e,n),wi(a,e,n)),e={cache:Qu()},t.payload=e;return}e=e.return}}function nv(t,e,n){var a=xe();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},is(t)?Uh(e,n):(n=zu(t,e,n,a),n!==null&&(Se(n,t,a),Bh(n,e,a)))}function _h(t,e,n){var a=xe();_i(t,e,n,a)}function _i(t,e,n,a){var s={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(is(t))Uh(e,s);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var f=e.lastRenderedState,p=r(f,n);if(s.hasEagerState=!0,s.eagerState=p,pe(p,f))return Yl(t,e,s,0),At===null&&ql(),!1}catch{}finally{}if(n=zu(t,e,s,a),n!==null)return Se(n,t,a),Bh(n,e,a),!0}return!1}function yo(t,e,n,a){if(a={lane:2,revertLane:Jo(),action:a,hasEagerState:!1,eagerState:null,next:null},is(t)){if(e)throw Error(o(479))}else e=zu(t,n,a,2),e!==null&&Se(e,t,2)}function is(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function Uh(t,e){Va=$l=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Bh(t,e,n){if((n&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,n|=a,e.lanes=n,Qc(t,n)}}var ls={readContext:ee,use:ts,useCallback:Vt,useContext:Vt,useEffect:Vt,useImperativeHandle:Vt,useLayoutEffect:Vt,useInsertionEffect:Vt,useMemo:Vt,useReducer:Vt,useRef:Vt,useState:Vt,useDebugValue:Vt,useDeferredValue:Vt,useTransition:Vt,useSyncExternalStore:Vt,useId:Vt,useHostTransitionStatus:Vt,useFormState:Vt,useActionState:Vt,useOptimistic:Vt,useMemoCache:Vt,useCacheRefresh:Vt},Hh={readContext:ee,use:ts,useCallback:function(t,e){return oe().memoizedState=[t,e===void 0?null:e],t},useContext:ee,useEffect:Sh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,as(4194308,4,Eh.bind(null,e,t),n)},useLayoutEffect:function(t,e){return as(4194308,4,t,e)},useInsertionEffect:function(t,e){as(4,2,t,e)},useMemo:function(t,e){var n=oe();e=e===void 0?null:e;var a=t();if(ea){mn(!0);try{t()}finally{mn(!1)}}return n.memoizedState=[a,e],a},useReducer:function(t,e,n){var a=oe();if(n!==void 0){var s=n(e);if(ea){mn(!0);try{n(e)}finally{mn(!1)}}}else s=e;return a.memoizedState=a.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},a.queue=t,t=t.dispatch=nv.bind(null,ut,t),[a.memoizedState,t]},useRef:function(t){var e=oe();return t={current:t},e.memoizedState=t},useState:function(t){t=ro(t);var e=t.queue,n=_h.bind(null,ut,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:fo,useDeferredValue:function(t,e){var n=oe();return ho(n,t,e)},useTransition:function(){var t=ro(!1);return t=Oh.bind(null,ut,t.queue,!0,!1),oe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var a=ut,s=oe();if(yt){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),At===null)throw Error(o(349));(ft&124)!==0||lh(a,e,n)}s.memoizedState=n;var r={value:n,getSnapshot:e};return s.queue=r,Sh(uh.bind(null,a,r,t),[t]),a.flags|=2048,_a(9,ns(),sh.bind(null,a,r,n,e),null),n},useId:function(){var t=oe(),e=At.identifierPrefix;if(yt){var n=Ie,a=$e;n=(a&~(1<<32-me(a)-1)).toString(32)+n,e="«"+e+"R"+n,n=Il++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Fg++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:po,useFormState:yh,useActionState:yh,useOptimistic:function(t){var e=oe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=yo.bind(null,ut,!0,n),n.dispatch=e,[t,e]},useMemoCache:so,useCacheRefresh:function(){return oe().memoizedState=ev.bind(null,ut)}},Lh={readContext:ee,use:ts,useCallback:Rh,useContext:ee,useEffect:Th,useImperativeHandle:Dh,useInsertionEffect:Ah,useLayoutEffect:Mh,useMemo:Nh,useReducer:es,useRef:xh,useState:function(){return es(nn)},useDebugValue:fo,useDeferredValue:function(t,e){var n=Ht();return wh(n,bt.memoizedState,t,e)},useTransition:function(){var t=es(nn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Vi(t),e]},useSyncExternalStore:ih,useId:Vh,useHostTransitionStatus:po,useFormState:gh,useActionState:gh,useOptimistic:function(t,e){var n=Ht();return ch(n,bt,t,e)},useMemoCache:so,useCacheRefresh:zh},av={readContext:ee,use:ts,useCallback:Rh,useContext:ee,useEffect:Th,useImperativeHandle:Dh,useInsertionEffect:Ah,useLayoutEffect:Mh,useMemo:Nh,useReducer:oo,useRef:xh,useState:function(){return oo(nn)},useDebugValue:fo,useDeferredValue:function(t,e){var n=Ht();return bt===null?ho(n,t,e):wh(n,bt.memoizedState,t,e)},useTransition:function(){var t=oo(nn)[0],e=Ht().memoizedState;return[typeof t=="boolean"?t:Vi(t),e]},useSyncExternalStore:ih,useId:Vh,useHostTransitionStatus:po,useFormState:bh,useActionState:bh,useOptimistic:function(t,e){var n=Ht();return bt!==null?ch(n,bt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:so,useCacheRefresh:zh},Ua=null,Ui=0;function ss(t){var e=Ui;return Ui+=1,Ua===null&&(Ua=[]),Ff(Ua,t,e)}function Bi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function us(t,e){throw e.$$typeof===x?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function qh(t){var e=t._init;return e(t._payload)}function Yh(t){function e(E,T){if(t){var D=E.deletions;D===null?(E.deletions=[T],E.flags|=16):D.push(T)}}function n(E,T){if(!t)return null;for(;T!==null;)e(E,T),T=T.sibling;return null}function a(E){for(var T=new Map;E!==null;)E.key!==null?T.set(E.key,E):T.set(E.index,E),E=E.sibling;return T}function s(E,T){return E=We(E,T),E.index=0,E.sibling=null,E}function r(E,T,D){return E.index=D,t?(D=E.alternate,D!==null?(D=D.index,D<T?(E.flags|=67108866,T):D):(E.flags|=67108866,T)):(E.flags|=1048576,T)}function f(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function p(E,T,D,j){return T===null||T.tag!==6?(T=Uu(D,E.mode,j),T.return=E,T):(T=s(T,D),T.return=E,T)}function b(E,T,D,j){var Z=D.type;return Z===B?O(E,T,D.props.children,j,D.key):T!==null&&(T.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===$&&qh(Z)===T.type)?(T=s(T,D.props),Bi(T,D),T.return=E,T):(T=Xl(D.type,D.key,D.props,null,E.mode,j),Bi(T,D),T.return=E,T)}function R(E,T,D,j){return T===null||T.tag!==4||T.stateNode.containerInfo!==D.containerInfo||T.stateNode.implementation!==D.implementation?(T=Bu(D,E.mode,j),T.return=E,T):(T=s(T,D.children||[]),T.return=E,T)}function O(E,T,D,j,Z){return T===null||T.tag!==7?(T=kn(D,E.mode,j,Z),T.return=E,T):(T=s(T,D),T.return=E,T)}function V(E,T,D){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=Uu(""+T,E.mode,D),T.return=E,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case A:return D=Xl(T.type,T.key,T.props,null,E.mode,D),Bi(D,T),D.return=E,D;case z:return T=Bu(T,E.mode,D),T.return=E,T;case $:var j=T._init;return T=j(T._payload),V(E,T,D)}if(Jt(T)||Yt(T))return T=kn(T,E.mode,D,null),T.return=E,T;if(typeof T.then=="function")return V(E,ss(T),D);if(T.$$typeof===L)return V(E,kl(E,T),D);us(E,T)}return null}function N(E,T,D,j){var Z=T!==null?T.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return Z!==null?null:p(E,T,""+D,j);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case A:return D.key===Z?b(E,T,D,j):null;case z:return D.key===Z?R(E,T,D,j):null;case $:return Z=D._init,D=Z(D._payload),N(E,T,D,j)}if(Jt(D)||Yt(D))return Z!==null?null:O(E,T,D,j,null);if(typeof D.then=="function")return N(E,T,ss(D),j);if(D.$$typeof===L)return N(E,T,kl(E,D),j);us(E,D)}return null}function w(E,T,D,j,Z){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return E=E.get(D)||null,p(T,E,""+j,Z);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case A:return E=E.get(j.key===null?D:j.key)||null,b(T,E,j,Z);case z:return E=E.get(j.key===null?D:j.key)||null,R(T,E,j,Z);case $:var ot=j._init;return j=ot(j._payload),w(E,T,D,j,Z)}if(Jt(j)||Yt(j))return E=E.get(D)||null,O(T,E,j,Z,null);if(typeof j.then=="function")return w(E,T,D,ss(j),Z);if(j.$$typeof===L)return w(E,T,D,kl(T,j),Z);us(T,j)}return null}function et(E,T,D,j){for(var Z=null,ot=null,J=T,I=T=0,kt=null;J!==null&&I<D.length;I++){J.index>I?(kt=J,J=null):kt=J.sibling;var mt=N(E,J,D[I],j);if(mt===null){J===null&&(J=kt);break}t&&J&&mt.alternate===null&&e(E,J),T=r(mt,T,I),ot===null?Z=mt:ot.sibling=mt,ot=mt,J=kt}if(I===D.length)return n(E,J),yt&&Pn(E,I),Z;if(J===null){for(;I<D.length;I++)J=V(E,D[I],j),J!==null&&(T=r(J,T,I),ot===null?Z=J:ot.sibling=J,ot=J);return yt&&Pn(E,I),Z}for(J=a(J);I<D.length;I++)kt=w(J,E,I,D[I],j),kt!==null&&(t&&kt.alternate!==null&&J.delete(kt.key===null?I:kt.key),T=r(kt,T,I),ot===null?Z=kt:ot.sibling=kt,ot=kt);return t&&J.forEach(function(Bn){return e(E,Bn)}),yt&&Pn(E,I),Z}function W(E,T,D,j){if(D==null)throw Error(o(151));for(var Z=null,ot=null,J=T,I=T=0,kt=null,mt=D.next();J!==null&&!mt.done;I++,mt=D.next()){J.index>I?(kt=J,J=null):kt=J.sibling;var Bn=N(E,J,mt.value,j);if(Bn===null){J===null&&(J=kt);break}t&&J&&Bn.alternate===null&&e(E,J),T=r(Bn,T,I),ot===null?Z=Bn:ot.sibling=Bn,ot=Bn,J=kt}if(mt.done)return n(E,J),yt&&Pn(E,I),Z;if(J===null){for(;!mt.done;I++,mt=D.next())mt=V(E,mt.value,j),mt!==null&&(T=r(mt,T,I),ot===null?Z=mt:ot.sibling=mt,ot=mt);return yt&&Pn(E,I),Z}for(J=a(J);!mt.done;I++,mt=D.next())mt=w(J,E,I,mt.value,j),mt!==null&&(t&&mt.alternate!==null&&J.delete(mt.key===null?I:mt.key),T=r(mt,T,I),ot===null?Z=mt:ot.sibling=mt,ot=mt);return t&&J.forEach(function(i1){return e(E,i1)}),yt&&Pn(E,I),Z}function St(E,T,D,j){if(typeof D=="object"&&D!==null&&D.type===B&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case A:t:{for(var Z=D.key;T!==null;){if(T.key===Z){if(Z=D.type,Z===B){if(T.tag===7){n(E,T.sibling),j=s(T,D.props.children),j.return=E,E=j;break t}}else if(T.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===$&&qh(Z)===T.type){n(E,T.sibling),j=s(T,D.props),Bi(j,D),j.return=E,E=j;break t}n(E,T);break}else e(E,T);T=T.sibling}D.type===B?(j=kn(D.props.children,E.mode,j,D.key),j.return=E,E=j):(j=Xl(D.type,D.key,D.props,null,E.mode,j),Bi(j,D),j.return=E,E=j)}return f(E);case z:t:{for(Z=D.key;T!==null;){if(T.key===Z)if(T.tag===4&&T.stateNode.containerInfo===D.containerInfo&&T.stateNode.implementation===D.implementation){n(E,T.sibling),j=s(T,D.children||[]),j.return=E,E=j;break t}else{n(E,T);break}else e(E,T);T=T.sibling}j=Bu(D,E.mode,j),j.return=E,E=j}return f(E);case $:return Z=D._init,D=Z(D._payload),St(E,T,D,j)}if(Jt(D))return et(E,T,D,j);if(Yt(D)){if(Z=Yt(D),typeof Z!="function")throw Error(o(150));return D=Z.call(D),W(E,T,D,j)}if(typeof D.then=="function")return St(E,T,ss(D),j);if(D.$$typeof===L)return St(E,T,kl(E,D),j);us(E,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,T!==null&&T.tag===6?(n(E,T.sibling),j=s(T,D),j.return=E,E=j):(n(E,T),j=Uu(D,E.mode,j),j.return=E,E=j),f(E)):n(E,T)}return function(E,T,D,j){try{Ui=0;var Z=St(E,T,D,j);return Ua=null,Z}catch(J){if(J===Ri||J===Pl)throw J;var ot=ye(29,J,null,E.mode);return ot.lanes=j,ot.return=E,ot}finally{}}}var Ba=Yh(!0),Gh=Yh(!1),Ne=_(null),Ye=null;function Tn(t){var e=t.alternate;Y(Xt,Xt.current&1),Y(Ne,t),Ye===null&&(e===null||ja.current!==null||e.memoizedState!==null)&&(Ye=t)}function Xh(t){if(t.tag===22){if(Y(Xt,Xt.current),Y(Ne,t),Ye===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ye=t)}}else An()}function An(){Y(Xt,Xt.current),Y(Ne,Ne.current)}function an(t){X(Ne),Ye===t&&(Ye=null),X(Xt)}var Xt=_(0);function os(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||sr(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function go(t,e,n,a){e=t.memoizedState,n=n(a,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var vo={enqueueSetState:function(t,e,n){t=t._reactInternals;var a=xe(),s=bn(a);s.payload=e,n!=null&&(s.callback=n),e=xn(t,s,a),e!==null&&(Se(e,t,a),wi(e,t,a))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var a=xe(),s=bn(a);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=xn(t,s,a),e!==null&&(Se(e,t,a),wi(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=xe(),a=bn(n);a.tag=2,e!=null&&(a.callback=e),e=xn(t,a,n),e!==null&&(Se(e,t,n),wi(e,t,n))}};function Qh(t,e,n,a,s,r,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,r,f):e.prototype&&e.prototype.isPureReactComponent?!bi(n,a)||!bi(s,r):!0}function Zh(t,e,n,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,a),e.state!==t&&vo.enqueueReplaceState(e,e.state,null)}function na(t,e){var n=e;if("ref"in e){n={};for(var a in e)a!=="ref"&&(n[a]=e[a])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var rs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Kh(t){rs(t)}function kh(t){console.error(t)}function Jh(t){rs(t)}function cs(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function Ph(t,e,n){try{var a=t.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function bo(t,e,n){return n=bn(n),n.tag=3,n.payload={element:null},n.callback=function(){cs(t,e)},n}function Fh(t){return t=bn(t),t.tag=3,t}function Wh(t,e,n,a){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var r=a.value;t.payload=function(){return s(r)},t.callback=function(){Ph(e,n,a)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Ph(e,n,a),typeof s!="function"&&(wn===null?wn=new Set([this]):wn.add(this));var p=a.stack;this.componentDidCatch(a.value,{componentStack:p!==null?p:""})})}function iv(t,e,n,a,s){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=n.alternate,e!==null&&Mi(e,n,s,!0),n=Ne.current,n!==null){switch(n.tag){case 13:return Ye===null?Xo():n.alternate===null&&Ct===0&&(Ct=3),n.flags&=-257,n.flags|=65536,n.lanes=s,a===ku?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([a]):e.add(a),Zo(t,a,s)),!1;case 22:return n.flags|=65536,a===ku?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([a]):n.add(a)),Zo(t,a,s)),!1}throw Error(o(435,n.tag))}return Zo(t,a,s),Xo(),!1}if(yt)return e=Ne.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,a!==qu&&(t=Error(o(422),{cause:a}),Ai(Me(t,n)))):(a!==qu&&(e=Error(o(423),{cause:a}),Ai(Me(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,a=Me(a,n),s=bo(t.stateNode,a,s),Fu(t,s),Ct!==4&&(Ct=2)),!1;var r=Error(o(520),{cause:a});if(r=Me(r,n),Qi===null?Qi=[r]:Qi.push(r),Ct!==4&&(Ct=2),e===null)return!0;a=Me(a,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=bo(n.stateNode,a,t),Fu(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(wn===null||!wn.has(r))))return n.flags|=65536,s&=-s,n.lanes|=s,s=Fh(s),Wh(s,t,n,a),Fu(n,s),!1}n=n.return}while(n!==null);return!1}var $h=Error(o(461)),Zt=!1;function Pt(t,e,n,a){e.child=t===null?Gh(e,null,n,a):Ba(e,t.child,n,a)}function Ih(t,e,n,a,s){n=n.render;var r=e.ref;if("ref"in a){var f={};for(var p in a)p!=="ref"&&(f[p]=a[p])}else f=a;return In(e),a=eo(t,e,n,f,r,s),p=no(),t!==null&&!Zt?(ao(t,e,s),ln(t,e,s)):(yt&&p&&Hu(e),e.flags|=1,Pt(t,e,a,s),e.child)}function td(t,e,n,a,s){if(t===null){var r=n.type;return typeof r=="function"&&!_u(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,ed(t,e,r,a,s)):(t=Xl(n.type,null,a,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!Ro(t,s)){var f=r.memoizedProps;if(n=n.compare,n=n!==null?n:bi,n(f,a)&&t.ref===e.ref)return ln(t,e,s)}return e.flags|=1,t=We(r,a),t.ref=e.ref,t.return=e,e.child=t}function ed(t,e,n,a,s){if(t!==null){var r=t.memoizedProps;if(bi(r,a)&&t.ref===e.ref)if(Zt=!1,e.pendingProps=a=r,Ro(t,s))(t.flags&131072)!==0&&(Zt=!0);else return e.lanes=t.lanes,ln(t,e,s)}return xo(t,e,n,a,s)}function nd(t,e,n){var a=e.pendingProps,s=a.children,r=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=r!==null?r.baseLanes|n:n,t!==null){for(s=e.child=t.child,r=0;s!==null;)r=r|s.lanes|s.childLanes,s=s.sibling;e.childLanes=r&~a}else e.childLanes=0,e.child=null;return ad(t,e,a,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Jl(e,r!==null?r.cachePool:null),r!==null?eh(e,r):$u(),Xh(e);else return e.lanes=e.childLanes=536870912,ad(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Jl(e,r.cachePool),eh(e,r),An(),e.memoizedState=null):(t!==null&&Jl(e,null),$u(),An());return Pt(t,e,s,n),e.child}function ad(t,e,n,a){var s=Ku();return s=s===null?null:{parent:Gt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Jl(e,null),$u(),Xh(e),t!==null&&Mi(t,e,a,!0),null}function fs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function xo(t,e,n,a,s){return In(e),n=eo(t,e,n,a,void 0,s),a=no(),t!==null&&!Zt?(ao(t,e,s),ln(t,e,s)):(yt&&a&&Hu(e),e.flags|=1,Pt(t,e,n,s),e.child)}function id(t,e,n,a,s,r){return In(e),e.updateQueue=null,n=ah(e,a,n,s),nh(t),a=no(),t!==null&&!Zt?(ao(t,e,r),ln(t,e,r)):(yt&&a&&Hu(e),e.flags|=1,Pt(t,e,n,r),e.child)}function ld(t,e,n,a,s){if(In(e),e.stateNode===null){var r=Ra,f=n.contextType;typeof f=="object"&&f!==null&&(r=ee(f)),r=new n(a,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=vo,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=a,r.state=e.memoizedState,r.refs={},Ju(e),f=n.contextType,r.context=typeof f=="object"&&f!==null?ee(f):Ra,r.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(go(e,n,f,a),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&vo.enqueueReplaceState(r,r.state,null),Ci(e,a,r,s),Oi(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){r=e.stateNode;var p=e.memoizedProps,b=na(n,p);r.props=b;var R=r.context,O=n.contextType;f=Ra,typeof O=="object"&&O!==null&&(f=ee(O));var V=n.getDerivedStateFromProps;O=typeof V=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=e.pendingProps!==p,O||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||R!==f)&&Zh(e,r,a,f),vn=!1;var N=e.memoizedState;r.state=N,Ci(e,a,r,s),Oi(),R=e.memoizedState,p||N!==R||vn?(typeof V=="function"&&(go(e,n,V,a),R=e.memoizedState),(b=vn||Qh(e,n,b,a,N,R,f))?(O||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=R),r.props=a,r.state=R,r.context=f,a=b):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{r=e.stateNode,Pu(t,e),f=e.memoizedProps,O=na(n,f),r.props=O,V=e.pendingProps,N=r.context,R=n.contextType,b=Ra,typeof R=="object"&&R!==null&&(b=ee(R)),p=n.getDerivedStateFromProps,(R=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==V||N!==b)&&Zh(e,r,a,b),vn=!1,N=e.memoizedState,r.state=N,Ci(e,a,r,s),Oi();var w=e.memoizedState;f!==V||N!==w||vn||t!==null&&t.dependencies!==null&&Kl(t.dependencies)?(typeof p=="function"&&(go(e,n,p,a),w=e.memoizedState),(O=vn||Qh(e,n,O,a,N,w,b)||t!==null&&t.dependencies!==null&&Kl(t.dependencies))?(R||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(a,w,b),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(a,w,b)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&N===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&N===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=w),r.props=a,r.state=w,r.context=b,a=O):(typeof r.componentDidUpdate!="function"||f===t.memoizedProps&&N===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&N===t.memoizedState||(e.flags|=1024),a=!1)}return r=a,fs(t,e),a=(e.flags&128)!==0,r||a?(r=e.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&a?(e.child=Ba(e,t.child,null,s),e.child=Ba(e,null,n,s)):Pt(t,e,n,s),e.memoizedState=r.state,t=e.child):t=ln(t,e,s),t}function sd(t,e,n,a){return Ti(),e.flags|=256,Pt(t,e,n,a),e.child}var So={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function To(t){return{baseLanes:t,cachePool:kf()}}function Ao(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=we),t}function ud(t,e,n){var a=e.pendingProps,s=!1,r=(e.flags&128)!==0,f;if((f=r)||(f=t!==null&&t.memoizedState===null?!1:(Xt.current&2)!==0),f&&(s=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(s?Tn(e):An(),yt){var p=Ot,b;if(b=p){t:{for(b=p,p=qe;b.nodeType!==8;){if(!p){p=null;break t}if(b=Ue(b.nextSibling),b===null){p=null;break t}}p=b}p!==null?(e.memoizedState={dehydrated:p,treeContext:Jn!==null?{id:$e,overflow:Ie}:null,retryLane:536870912,hydrationErrors:null},b=ye(18,null,null,0),b.stateNode=p,b.return=e,e.child=b,ie=e,Ot=null,b=!0):b=!1}b||Wn(e)}if(p=e.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return sr(p)?e.lanes=32:e.lanes=536870912,null;an(e)}return p=a.children,a=a.fallback,s?(An(),s=e.mode,p=hs({mode:"hidden",children:p},s),a=kn(a,s,n,null),p.return=e,a.return=e,p.sibling=a,e.child=p,s=e.child,s.memoizedState=To(n),s.childLanes=Ao(t,f,n),e.memoizedState=So,a):(Tn(e),Mo(e,p))}if(b=t.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(r)e.flags&256?(Tn(e),e.flags&=-257,e=Eo(t,e,n)):e.memoizedState!==null?(An(),e.child=t.child,e.flags|=128,e=null):(An(),s=a.fallback,p=e.mode,a=hs({mode:"visible",children:a.children},p),s=kn(s,p,n,null),s.flags|=2,a.return=e,s.return=e,a.sibling=s,e.child=a,Ba(e,t.child,null,n),a=e.child,a.memoizedState=To(n),a.childLanes=Ao(t,f,n),e.memoizedState=So,e=s);else if(Tn(e),sr(p)){if(f=p.nextSibling&&p.nextSibling.dataset,f)var R=f.dgst;f=R,a=Error(o(419)),a.stack="",a.digest=f,Ai({value:a,source:null,stack:null}),e=Eo(t,e,n)}else if(Zt||Mi(t,e,n,!1),f=(n&t.childLanes)!==0,Zt||f){if(f=At,f!==null&&(a=n&-n,a=(a&42)!==0?1:su(a),a=(a&(f.suspendedLanes|n))!==0?0:a,a!==0&&a!==b.retryLane))throw b.retryLane=a,Da(t,a),Se(f,t,a),$h;p.data==="$?"||Xo(),e=Eo(t,e,n)}else p.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=b.treeContext,Ot=Ue(p.nextSibling),ie=e,yt=!0,Fn=null,qe=!1,t!==null&&(De[Re++]=$e,De[Re++]=Ie,De[Re++]=Jn,$e=t.id,Ie=t.overflow,Jn=e),e=Mo(e,a.children),e.flags|=4096);return e}return s?(An(),s=a.fallback,p=e.mode,b=t.child,R=b.sibling,a=We(b,{mode:"hidden",children:a.children}),a.subtreeFlags=b.subtreeFlags&65011712,R!==null?s=We(R,s):(s=kn(s,p,n,null),s.flags|=2),s.return=e,a.return=e,a.sibling=s,e.child=a,a=s,s=e.child,p=t.child.memoizedState,p===null?p=To(n):(b=p.cachePool,b!==null?(R=Gt._currentValue,b=b.parent!==R?{parent:R,pool:R}:b):b=kf(),p={baseLanes:p.baseLanes|n,cachePool:b}),s.memoizedState=p,s.childLanes=Ao(t,f,n),e.memoizedState=So,a):(Tn(e),n=t.child,t=n.sibling,n=We(n,{mode:"visible",children:a.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function Mo(t,e){return e=hs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hs(t,e){return t=ye(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Eo(t,e,n){return Ba(e,t.child,null,n),t=Mo(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function od(t,e,n){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Gu(t.return,e,n)}function Do(t,e,n,a,s){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:s}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=a,r.tail=n,r.tailMode=s)}function rd(t,e,n){var a=e.pendingProps,s=a.revealOrder,r=a.tail;if(Pt(t,e,a.children,n),a=Xt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&od(t,n,e);else if(t.tag===19)od(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(Y(Xt,a),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&os(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Do(e,!1,s,n,r);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&os(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Do(e,!0,n,null,r);break;case"together":Do(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function ln(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Nn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Mi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=We(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=We(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Ro(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Kl(t)))}function lv(t,e,n){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),gn(e,Gt,t.memoizedState.cache),Ti();break;case 27:case 5:eu(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:gn(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(Tn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?ud(t,e,n):(Tn(e),t=ln(t,e,n),t!==null?t.sibling:null);Tn(e);break;case 19:var s=(t.flags&128)!==0;if(a=(n&e.childLanes)!==0,a||(Mi(t,e,n,!1),a=(n&e.childLanes)!==0),s){if(a)return rd(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Y(Xt,Xt.current),a)break;return null;case 22:case 23:return e.lanes=0,nd(t,e,n);case 24:gn(e,Gt,t.memoizedState.cache)}return ln(t,e,n)}function cd(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Zt=!0;else{if(!Ro(t,n)&&(e.flags&128)===0)return Zt=!1,lv(t,e,n);Zt=(t.flags&131072)!==0}else Zt=!1,yt&&(e.flags&1048576)!==0&&qf(e,Zl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,s=a._init;if(a=s(a._payload),e.type=a,typeof a=="function")_u(a)?(t=na(a,t),e.tag=1,e=ld(null,e,a,t,n)):(e.tag=0,e=xo(null,e,a,t,n));else{if(a!=null){if(s=a.$$typeof,s===at){e.tag=11,e=Ih(null,e,a,t,n);break t}else if(s===ht){e.tag=14,e=td(null,e,a,t,n);break t}}throw e=He(a)||a,Error(o(306,e,""))}}return e;case 0:return xo(t,e,e.type,e.pendingProps,n);case 1:return a=e.type,s=na(a,e.pendingProps),ld(t,e,a,s,n);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(o(387));a=e.pendingProps;var r=e.memoizedState;s=r.element,Pu(t,e),Ci(e,a,null,n);var f=e.memoizedState;if(a=f.cache,gn(e,Gt,a),a!==r.cache&&Xu(e,[Gt],n,!0),Oi(),a=f.element,r.isDehydrated)if(r={element:a,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=sd(t,e,a,n);break t}else if(a!==s){s=Me(Error(o(424)),e),Ai(s),e=sd(t,e,a,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ot=Ue(t.firstChild),ie=e,yt=!0,Fn=null,qe=!0,n=Gh(e,null,a,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ti(),a===s){e=ln(t,e,n);break t}Pt(t,e,a,n)}e=e.child}return e;case 26:return fs(t,e),t===null?(n=mm(e.type,null,e.pendingProps,null))?e.memoizedState=n:yt||(n=e.type,t=e.pendingProps,a=Ds(it.current).createElement(n),a[te]=e,a[se]=t,Wt(a,n,t),Qt(a),e.stateNode=a):e.memoizedState=mm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return eu(e),t===null&&yt&&(a=e.stateNode=fm(e.type,e.pendingProps,it.current),ie=e,qe=!0,s=Ot,jn(e.type)?(ur=s,Ot=Ue(a.firstChild)):Ot=s),Pt(t,e,e.pendingProps.children,n),fs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((s=a=Ot)&&(a=Vv(a,e.type,e.pendingProps,qe),a!==null?(e.stateNode=a,ie=e,Ot=Ue(a.firstChild),qe=!1,s=!0):s=!1),s||Wn(e)),eu(e),s=e.type,r=e.pendingProps,f=t!==null?t.memoizedProps:null,a=r.children,ar(s,r)?a=null:f!==null&&ar(s,f)&&(e.flags|=32),e.memoizedState!==null&&(s=eo(t,e,Wg,null,null,n),Ii._currentValue=s),fs(t,e),Pt(t,e,a,n),e.child;case 6:return t===null&&yt&&((t=n=Ot)&&(n=zv(n,e.pendingProps,qe),n!==null?(e.stateNode=n,ie=e,Ot=null,t=!0):t=!1),t||Wn(e)),null;case 13:return ud(t,e,n);case 4:return Et(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ba(e,null,a,n):Pt(t,e,a,n),e.child;case 11:return Ih(t,e,e.type,e.pendingProps,n);case 7:return Pt(t,e,e.pendingProps,n),e.child;case 8:return Pt(t,e,e.pendingProps.children,n),e.child;case 12:return Pt(t,e,e.pendingProps.children,n),e.child;case 10:return a=e.pendingProps,gn(e,e.type,a.value),Pt(t,e,a.children,n),e.child;case 9:return s=e.type._context,a=e.pendingProps.children,In(e),s=ee(s),a=a(s),e.flags|=1,Pt(t,e,a,n),e.child;case 14:return td(t,e,e.type,e.pendingProps,n);case 15:return ed(t,e,e.type,e.pendingProps,n);case 19:return rd(t,e,n);case 31:return a=e.pendingProps,n=e.mode,a={mode:a.mode,children:a.children},t===null?(n=hs(a,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=We(t.child,a),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return nd(t,e,n);case 24:return In(e),a=ee(Gt),t===null?(s=Ku(),s===null&&(s=At,r=Qu(),s.pooledCache=r,r.refCount++,r!==null&&(s.pooledCacheLanes|=n),s=r),e.memoizedState={parent:a,cache:s},Ju(e),gn(e,Gt,s)):((t.lanes&n)!==0&&(Pu(t,e),Ci(e,null,null,n),Oi()),s=t.memoizedState,r=e.memoizedState,s.parent!==a?(s={parent:a,cache:a},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),gn(e,Gt,a)):(a=r.cache,gn(e,Gt,a),a!==s.cache&&Xu(e,[Gt],n,!0))),Pt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function sn(t){t.flags|=4}function fd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!bm(e)){if(e=Ne.current,e!==null&&((ft&4194048)===ft?Ye!==null:(ft&62914560)!==ft&&(ft&536870912)===0||e!==Ye))throw Ni=ku,Jf;t.flags|=8192}}function ds(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Gc():536870912,t.lanes|=e,Ya|=e)}function Hi(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Nt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,a=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags&65011712,a|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,a|=s.subtreeFlags,a|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=a,t.childLanes=n,e}function sv(t,e,n){var a=e.pendingProps;switch(Lu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Nt(e),null;case 1:return Nt(e),null;case 3:return n=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),en(Gt),dn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Si(e)?sn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Xf())),Nt(e),null;case 26:return n=e.memoizedState,t===null?(sn(e),n!==null?(Nt(e),fd(e,n)):(Nt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(sn(e),Nt(e),fd(e,n)):(Nt(e),e.flags&=-16777217):(t.memoizedProps!==a&&sn(e),Nt(e),e.flags&=-16777217),null;case 27:Ml(e),n=it.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Nt(e),null}t=F.current,Si(e)?Yf(e):(t=fm(s,a,n),e.stateNode=t,sn(e))}return Nt(e),null;case 5:if(Ml(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(!a){if(e.stateNode===null)throw Error(o(166));return Nt(e),null}if(t=F.current,Si(e))Yf(e);else{switch(s=Ds(it.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?s.createElement("select",{is:a.is}):s.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?s.createElement(n,{is:a.is}):s.createElement(n)}}t[te]=e,t[se]=a;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Wt(t,n,a),n){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&sn(e)}}return Nt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&sn(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(o(166));if(t=it.current,Si(e)){if(t=e.stateNode,n=e.memoizedProps,a=null,s=ie,s!==null)switch(s.tag){case 27:case 5:a=s.memoizedProps}t[te]=e,t=!!(t.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||im(t.nodeValue,n)),t||Wn(e)}else t=Ds(t).createTextNode(a),t[te]=e,e.stateNode=t}return Nt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=Si(e),a!==null&&a.dehydrated!==null){if(t===null){if(!s)throw Error(o(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[te]=e}else Ti(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Nt(e),s=!1}else s=Xf(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(an(e),e):(an(e),null)}if(an(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=a!==null,t=t!==null&&t.memoizedState!==null,n){a=e.child,s=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(s=a.alternate.memoizedState.cachePool.pool);var r=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(r=a.memoizedState.cachePool.pool),r!==s&&(a.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ds(e,e.updateQueue),Nt(e),null;case 4:return dn(),t===null&&$o(e.stateNode.containerInfo),Nt(e),null;case 10:return en(e.type),Nt(e),null;case 19:if(X(Xt),s=e.memoizedState,s===null)return Nt(e),null;if(a=(e.flags&128)!==0,r=s.rendering,r===null)if(a)Hi(s,!1);else{if(Ct!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(r=os(t),r!==null){for(e.flags|=128,Hi(s,!1),t=r.updateQueue,e.updateQueue=t,ds(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Lf(n,t),n=n.sibling;return Y(Xt,Xt.current&1|2),e.child}t=t.sibling}s.tail!==null&&Le()>ys&&(e.flags|=128,a=!0,Hi(s,!1),e.lanes=4194304)}else{if(!a)if(t=os(r),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ds(e,t),Hi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!r.alternate&&!yt)return Nt(e),null}else 2*Le()-s.renderingStartTime>ys&&n!==536870912&&(e.flags|=128,a=!0,Hi(s,!1),e.lanes=4194304);s.isBackwards?(r.sibling=e.child,e.child=r):(t=s.last,t!==null?t.sibling=r:e.child=r,s.last=r)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=Le(),e.sibling=null,t=Xt.current,Y(Xt,a?t&1|2:t&1),e):(Nt(e),null);case 22:case 23:return an(e),Iu(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(n&536870912)!==0&&(e.flags&128)===0&&(Nt(e),e.subtreeFlags&6&&(e.flags|=8192)):Nt(e),n=e.updateQueue,n!==null&&ds(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==n&&(e.flags|=2048),t!==null&&X(ta),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),en(Gt),Nt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function uv(t,e){switch(Lu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return en(Gt),dn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ml(e),null;case 13:if(an(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));Ti()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return X(Xt),null;case 4:return dn(),null;case 10:return en(e.type),null;case 22:case 23:return an(e),Iu(),t!==null&&X(ta),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return en(Gt),null;case 25:return null;default:return null}}function hd(t,e){switch(Lu(e),e.tag){case 3:en(Gt),dn();break;case 26:case 27:case 5:Ml(e);break;case 4:dn();break;case 13:an(e);break;case 19:X(Xt);break;case 10:en(e.type);break;case 22:case 23:an(e),Iu(),t!==null&&X(ta);break;case 24:en(Gt)}}function Li(t,e){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var s=a.next;n=s;do{if((n.tag&t)===t){a=void 0;var r=n.create,f=n.inst;a=r(),f.destroy=a}n=n.next}while(n!==s)}}catch(p){Tt(e,e.return,p)}}function Mn(t,e,n){try{var a=e.updateQueue,s=a!==null?a.lastEffect:null;if(s!==null){var r=s.next;a=r;do{if((a.tag&t)===t){var f=a.inst,p=f.destroy;if(p!==void 0){f.destroy=void 0,s=e;var b=n,R=p;try{R()}catch(O){Tt(s,b,O)}}}a=a.next}while(a!==r)}}catch(O){Tt(e,e.return,O)}}function dd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{th(e,n)}catch(a){Tt(t,t.return,a)}}}function md(t,e,n){n.props=na(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(a){Tt(t,e,a)}}function qi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof n=="function"?t.refCleanup=n(a):n.current=a}}catch(s){Tt(t,e,s)}}function Ge(t,e){var n=t.ref,a=t.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(s){Tt(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){Tt(t,e,s)}else n.current=null}function pd(t){var e=t.type,n=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break t;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(s){Tt(t,t.return,s)}}function No(t,e,n){try{var a=t.stateNode;Nv(a,t.type,n,e),a[se]=e}catch(s){Tt(t,t.return,s)}}function yd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&jn(t.type)||t.tag===4}function wo(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||yd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&jn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Oo(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Es));else if(a!==4&&(a===27&&jn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Oo(t,e,n),t=t.sibling;t!==null;)Oo(t,e,n),t=t.sibling}function ms(t,e,n){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(a!==4&&(a===27&&jn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function gd(t){var e=t.stateNode,n=t.memoizedProps;try{for(var a=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Wt(e,a,n),e[te]=t,e[se]=n}catch(r){Tt(t,t.return,r)}}var un=!1,zt=!1,Co=!1,vd=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function ov(t,e){if(t=t.containerInfo,er=js,t=wf(t),Nu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var s=a.anchorOffset,r=a.focusNode;a=a.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break t}var f=0,p=-1,b=-1,R=0,O=0,V=t,N=null;e:for(;;){for(var w;V!==n||s!==0&&V.nodeType!==3||(p=f+s),V!==r||a!==0&&V.nodeType!==3||(b=f+a),V.nodeType===3&&(f+=V.nodeValue.length),(w=V.firstChild)!==null;)N=V,V=w;for(;;){if(V===t)break e;if(N===n&&++R===s&&(p=f),N===r&&++O===a&&(b=f),(w=V.nextSibling)!==null)break;V=N,N=V.parentNode}V=w}n=p===-1||b===-1?null:{start:p,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(nr={focusedElem:t,selectionRange:n},js=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&r!==null){t=void 0,n=e,s=r.memoizedProps,r=r.memoizedState,a=n.stateNode;try{var et=na(n.type,s,n.elementType===n.type);t=a.getSnapshotBeforeUpdate(et,r),a.__reactInternalSnapshotBeforeUpdate=t}catch(W){Tt(n,n.return,W)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)lr(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":lr(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function bd(t,e,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),a&4&&Li(5,n);break;case 1:if(En(t,n),a&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){Tt(n,n.return,f)}else{var s=na(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){Tt(n,n.return,f)}}a&64&&dd(n),a&512&&qi(n,n.return);break;case 3:if(En(t,n),a&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{th(t,e)}catch(f){Tt(n,n.return,f)}}break;case 27:e===null&&a&4&&gd(n);case 26:case 5:En(t,n),e===null&&a&4&&pd(n),a&512&&qi(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),a&4&&Td(t,n),a&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=gv.bind(null,n),_v(t,n))));break;case 22:if(a=n.memoizedState!==null||un,!a){e=e!==null&&e.memoizedState!==null||zt,s=un;var r=zt;un=a,(zt=e)&&!r?Dn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),un=s,zt=r}break;case 30:break;default:En(t,n)}}function xd(t){var e=t.alternate;e!==null&&(t.alternate=null,xd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ru(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Dt=null,re=!1;function on(t,e,n){for(n=n.child;n!==null;)Sd(t,e,n),n=n.sibling}function Sd(t,e,n){if(de&&typeof de.onCommitFiberUnmount=="function")try{de.onCommitFiberUnmount(ui,n)}catch{}switch(n.tag){case 26:zt||Ge(n,e),on(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:zt||Ge(n,e);var a=Dt,s=re;jn(n.type)&&(Dt=n.stateNode,re=!1),on(t,e,n),Pi(n.stateNode),Dt=a,re=s;break;case 5:zt||Ge(n,e);case 6:if(a=Dt,s=re,Dt=null,on(t,e,n),Dt=a,re=s,Dt!==null)if(re)try{(Dt.nodeType===9?Dt.body:Dt.nodeName==="HTML"?Dt.ownerDocument.body:Dt).removeChild(n.stateNode)}catch(r){Tt(n,e,r)}else try{Dt.removeChild(n.stateNode)}catch(r){Tt(n,e,r)}break;case 18:Dt!==null&&(re?(t=Dt,rm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),al(t)):rm(Dt,n.stateNode));break;case 4:a=Dt,s=re,Dt=n.stateNode.containerInfo,re=!0,on(t,e,n),Dt=a,re=s;break;case 0:case 11:case 14:case 15:zt||Mn(2,n,e),zt||Mn(4,n,e),on(t,e,n);break;case 1:zt||(Ge(n,e),a=n.stateNode,typeof a.componentWillUnmount=="function"&&md(n,e,a)),on(t,e,n);break;case 21:on(t,e,n);break;case 22:zt=(a=zt)||n.memoizedState!==null,on(t,e,n),zt=a;break;default:on(t,e,n)}}function Td(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{al(t)}catch(n){Tt(e,e.return,n)}}function rv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new vd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new vd),e;default:throw Error(o(435,t.tag))}}function jo(t,e){var n=rv(t);e.forEach(function(a){var s=vv.bind(null,t,a);n.has(a)||(n.add(a),a.then(s,s))})}function ge(t,e){var n=e.deletions;if(n!==null)for(var a=0;a<n.length;a++){var s=n[a],r=t,f=e,p=f;t:for(;p!==null;){switch(p.tag){case 27:if(jn(p.type)){Dt=p.stateNode,re=!1;break t}break;case 5:Dt=p.stateNode,re=!1;break t;case 3:case 4:Dt=p.stateNode.containerInfo,re=!0;break t}p=p.return}if(Dt===null)throw Error(o(160));Sd(r,f,s),Dt=null,re=!1,r=s.alternate,r!==null&&(r.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Ad(e,t),e=e.sibling}var _e=null;function Ad(t,e){var n=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ge(e,t),ve(t),a&4&&(Mn(3,t,t.return),Li(3,t),Mn(5,t,t.return));break;case 1:ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),a&64&&un&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var s=_e;if(ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),a&4){var r=n!==null?n.memoizedState:null;if(a=t.memoizedState,n===null)if(a===null)if(t.stateNode===null){t:{a=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(a){case"title":r=s.getElementsByTagName("title")[0],(!r||r[ci]||r[te]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=s.createElement(a),s.head.insertBefore(r,s.querySelector("head > title"))),Wt(r,a,n),r[te]=t,Qt(r),a=r;break t;case"link":var f=gm("link","href",s).get(a+(n.href||""));if(f){for(var p=0;p<f.length;p++)if(r=f[p],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(p,1);break e}}r=s.createElement(a),Wt(r,a,n),s.head.appendChild(r);break;case"meta":if(f=gm("meta","content",s).get(a+(n.content||""))){for(p=0;p<f.length;p++)if(r=f[p],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(p,1);break e}}r=s.createElement(a),Wt(r,a,n),s.head.appendChild(r);break;default:throw Error(o(468,a))}r[te]=t,Qt(r),a=r}t.stateNode=a}else vm(s,t.type,t.stateNode);else t.stateNode=ym(s,a,t.memoizedProps);else r!==a?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,a===null?vm(s,t.type,t.stateNode):ym(s,a,t.memoizedProps)):a===null&&t.stateNode!==null&&No(t,t.memoizedProps,n.memoizedProps)}break;case 27:ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),n!==null&&a&4&&No(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ge(e,t),ve(t),a&512&&(zt||n===null||Ge(n,n.return)),t.flags&32){s=t.stateNode;try{ba(s,"")}catch(w){Tt(t,t.return,w)}}a&4&&t.stateNode!=null&&(s=t.memoizedProps,No(t,s,n!==null?n.memoizedProps:s)),a&1024&&(Co=!0);break;case 6:if(ge(e,t),ve(t),a&4){if(t.stateNode===null)throw Error(o(162));a=t.memoizedProps,n=t.stateNode;try{n.nodeValue=a}catch(w){Tt(t,t.return,w)}}break;case 3:if(ws=null,s=_e,_e=Rs(e.containerInfo),ge(e,t),_e=s,ve(t),a&4&&n!==null&&n.memoizedState.isDehydrated)try{al(e.containerInfo)}catch(w){Tt(t,t.return,w)}Co&&(Co=!1,Md(t));break;case 4:a=_e,_e=Rs(t.stateNode.containerInfo),ge(e,t),ve(t),_e=a;break;case 12:ge(e,t),ve(t);break;case 13:ge(e,t),ve(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Ho=Le()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,jo(t,a)));break;case 22:s=t.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,R=un,O=zt;if(un=R||s,zt=O||b,ge(e,t),zt=O,un=R,ve(t),a&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||b||un||zt||aa(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){b=n=e;try{if(r=b.stateNode,s)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{p=b.stateNode;var V=b.memoizedProps.style,N=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=N==null||typeof N=="boolean"?"":(""+N).trim()}}catch(w){Tt(b,b.return,w)}}}else if(e.tag===6){if(n===null){b=e;try{b.stateNode.nodeValue=s?"":b.memoizedProps}catch(w){Tt(b,b.return,w)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,jo(t,n))));break;case 19:ge(e,t),ve(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,jo(t,a)));break;case 30:break;case 21:break;default:ge(e,t),ve(t)}}function ve(t){var e=t.flags;if(e&2){try{for(var n,a=t.return;a!==null;){if(yd(a)){n=a;break}a=a.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var s=n.stateNode,r=wo(t);ms(t,r,s);break;case 5:var f=n.stateNode;n.flags&32&&(ba(f,""),n.flags&=-33);var p=wo(t);ms(t,p,f);break;case 3:case 4:var b=n.stateNode.containerInfo,R=wo(t);Oo(t,R,b);break;default:throw Error(o(161))}}catch(O){Tt(t,t.return,O)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Md(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Md(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)bd(t,e.alternate,e),e=e.sibling}function aa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Mn(4,e,e.return),aa(e);break;case 1:Ge(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&md(e,e.return,n),aa(e);break;case 27:Pi(e.stateNode);case 26:case 5:Ge(e,e.return),aa(e);break;case 22:e.memoizedState===null&&aa(e);break;case 30:aa(e);break;default:aa(e)}t=t.sibling}}function Dn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,s=t,r=e,f=r.flags;switch(r.tag){case 0:case 11:case 15:Dn(s,r,n),Li(4,r);break;case 1:if(Dn(s,r,n),a=r,s=a.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(R){Tt(a,a.return,R)}if(a=r,s=a.updateQueue,s!==null){var p=a.stateNode;try{var b=s.shared.hiddenCallbacks;if(b!==null)for(s.shared.hiddenCallbacks=null,s=0;s<b.length;s++)If(b[s],p)}catch(R){Tt(a,a.return,R)}}n&&f&64&&dd(r),qi(r,r.return);break;case 27:gd(r);case 26:case 5:Dn(s,r,n),n&&a===null&&f&4&&pd(r),qi(r,r.return);break;case 12:Dn(s,r,n);break;case 13:Dn(s,r,n),n&&f&4&&Td(s,r);break;case 22:r.memoizedState===null&&Dn(s,r,n),qi(r,r.return);break;case 30:break;default:Dn(s,r,n)}e=e.sibling}}function Vo(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Ei(n))}function zo(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ei(t))}function Xe(t,e,n,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ed(t,e,n,a),e=e.sibling}function Ed(t,e,n,a){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Xe(t,e,n,a),s&2048&&Li(9,e);break;case 1:Xe(t,e,n,a);break;case 3:Xe(t,e,n,a),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ei(t)));break;case 12:if(s&2048){Xe(t,e,n,a),t=e.stateNode;try{var r=e.memoizedProps,f=r.id,p=r.onPostCommit;typeof p=="function"&&p(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(b){Tt(e,e.return,b)}}else Xe(t,e,n,a);break;case 13:Xe(t,e,n,a);break;case 23:break;case 22:r=e.stateNode,f=e.alternate,e.memoizedState!==null?r._visibility&2?Xe(t,e,n,a):Yi(t,e):r._visibility&2?Xe(t,e,n,a):(r._visibility|=2,Ha(t,e,n,a,(e.subtreeFlags&10256)!==0)),s&2048&&Vo(f,e);break;case 24:Xe(t,e,n,a),s&2048&&zo(e.alternate,e);break;default:Xe(t,e,n,a)}}function Ha(t,e,n,a,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,f=e,p=n,b=a,R=f.flags;switch(f.tag){case 0:case 11:case 15:Ha(r,f,p,b,s),Li(8,f);break;case 23:break;case 22:var O=f.stateNode;f.memoizedState!==null?O._visibility&2?Ha(r,f,p,b,s):Yi(r,f):(O._visibility|=2,Ha(r,f,p,b,s)),s&&R&2048&&Vo(f.alternate,f);break;case 24:Ha(r,f,p,b,s),s&&R&2048&&zo(f.alternate,f);break;default:Ha(r,f,p,b,s)}e=e.sibling}}function Yi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,a=e,s=a.flags;switch(a.tag){case 22:Yi(n,a),s&2048&&Vo(a.alternate,a);break;case 24:Yi(n,a),s&2048&&zo(a.alternate,a);break;default:Yi(n,a)}e=e.sibling}}var Gi=8192;function La(t){if(t.subtreeFlags&Gi)for(t=t.child;t!==null;)Dd(t),t=t.sibling}function Dd(t){switch(t.tag){case 26:La(t),t.flags&Gi&&t.memoizedState!==null&&Jv(_e,t.memoizedState,t.memoizedProps);break;case 5:La(t);break;case 3:case 4:var e=_e;_e=Rs(t.stateNode.containerInfo),La(t),_e=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Gi,Gi=16777216,La(t),Gi=e):La(t));break;default:La(t)}}function Rd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Kt=a,wd(a,t)}Rd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Nd(t),t=t.sibling}function Nd(t){switch(t.tag){case 0:case 11:case 15:Xi(t),t.flags&2048&&Mn(9,t,t.return);break;case 3:Xi(t);break;case 12:Xi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ps(t)):Xi(t);break;default:Xi(t)}}function ps(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var a=e[n];Kt=a,wd(a,t)}Rd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Mn(8,e,e.return),ps(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ps(e));break;default:ps(e)}t=t.sibling}}function wd(t,e){for(;Kt!==null;){var n=Kt;switch(n.tag){case 0:case 11:case 15:Mn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Ei(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,Kt=a;else t:for(n=t;Kt!==null;){a=Kt;var s=a.sibling,r=a.return;if(xd(a),a===n){Kt=null;break t}if(s!==null){s.return=r,Kt=s;break t}Kt=r}}}var cv={getCacheForType:function(t){var e=ee(Gt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},fv=typeof WeakMap=="function"?WeakMap:Map,gt=0,At=null,rt=null,ft=0,vt=0,be=null,Rn=!1,qa=!1,_o=!1,rn=0,Ct=0,Nn=0,ia=0,Uo=0,we=0,Ya=0,Qi=null,ce=null,Bo=!1,Ho=0,ys=1/0,gs=null,wn=null,Ft=0,On=null,Ga=null,Xa=0,Lo=0,qo=null,Od=null,Zi=0,Yo=null;function xe(){if((gt&2)!==0&&ft!==0)return ft&-ft;if(C.T!==null){var t=Oa;return t!==0?t:Jo()}return Zc()}function Cd(){we===0&&(we=(ft&536870912)===0||yt?Yc():536870912);var t=Ne.current;return t!==null&&(t.flags|=32),we}function Se(t,e,n){(t===At&&(vt===2||vt===9)||t.cancelPendingCommit!==null)&&(Qa(t,0),Cn(t,ft,we,!1)),ri(t,n),((gt&2)===0||t!==At)&&(t===At&&((gt&2)===0&&(ia|=n),Ct===4&&Cn(t,ft,we,!1)),Qe(t))}function jd(t,e,n){if((gt&6)!==0)throw Error(o(327));var a=!n&&(e&124)===0&&(e&t.expiredLanes)===0||oi(t,e),s=a?mv(t,e):Qo(t,e,!0),r=a;do{if(s===0){qa&&!a&&Cn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!hv(n)){s=Qo(t,e,!1),r=!1;continue}if(s===2){if(r=e,t.errorRecoveryDisabledLanes&r)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var p=t;s=Qi;var b=p.current.memoizedState.isDehydrated;if(b&&(Qa(p,f).flags|=256),f=Qo(p,f,!1),f!==2){if(_o&&!b){p.errorRecoveryDisabledLanes|=r,ia|=r,s=4;break t}r=ce,ce=s,r!==null&&(ce===null?ce=r:ce.push.apply(ce,r))}s=f}if(r=!1,s!==2)continue}}if(s===1){Qa(t,0),Cn(t,e,0,!0);break}t:{switch(a=t,r=s,r){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:Cn(a,e,we,!Rn);break t;case 2:ce=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(s=Ho+300-Le(),10<s)){if(Cn(a,e,we,!Rn),Nl(a,0,!0)!==0)break t;a.timeoutHandle=um(Vd.bind(null,a,n,ce,gs,Bo,e,we,ia,Ya,Rn,r,2,-0,0),s);break t}Vd(a,n,ce,gs,Bo,e,we,ia,Ya,Rn,r,0,-0,0)}}break}while(!0);Qe(t)}function Vd(t,e,n,a,s,r,f,p,b,R,O,V,N,w){if(t.timeoutHandle=-1,V=e.subtreeFlags,(V&8192||(V&16785408)===16785408)&&($i={stylesheets:null,count:0,unsuspend:kv},Dd(e),V=Pv(),V!==null)){t.cancelPendingCommit=V(qd.bind(null,t,e,r,n,a,s,f,p,b,O,1,N,w)),Cn(t,r,f,!R);return}qd(t,e,r,n,a,s,f,p,b)}function hv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var s=n[a],r=s.getSnapshot;s=s.value;try{if(!pe(r(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Cn(t,e,n,a){e&=~Uo,e&=~ia,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var s=e;0<s;){var r=31-me(s),f=1<<r;a[r]=-1,s&=~f}n!==0&&Xc(t,n,e)}function vs(){return(gt&6)===0?(Ki(0),!1):!0}function Go(){if(rt!==null){if(vt===0)var t=rt.return;else t=rt,tn=$n=null,io(t),Ua=null,Ui=0,t=rt;for(;t!==null;)hd(t.alternate,t),t=t.return;rt=null}}function Qa(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Ov(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Go(),At=t,rt=n=We(t.current,null),ft=e,vt=0,be=null,Rn=!1,qa=oi(t,e),_o=!1,Ya=we=Uo=ia=Nn=Ct=0,ce=Qi=null,Bo=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var s=31-me(a),r=1<<s;e|=t[s],a&=~r}return rn=e,ql(),n}function zd(t,e){ut=null,C.H=ls,e===Ri||e===Pl?(e=Wf(),vt=3):e===Jf?(e=Wf(),vt=4):vt=e===$h?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,be=e,rt===null&&(Ct=1,cs(t,Me(e,t.current)))}function _d(){var t=C.H;return C.H=ls,t===null?ls:t}function Ud(){var t=C.A;return C.A=cv,t}function Xo(){Ct=4,Rn||(ft&4194048)!==ft&&Ne.current!==null||(qa=!0),(Nn&134217727)===0&&(ia&134217727)===0||At===null||Cn(At,ft,we,!1)}function Qo(t,e,n){var a=gt;gt|=2;var s=_d(),r=Ud();(At!==t||ft!==e)&&(gs=null,Qa(t,e)),e=!1;var f=Ct;t:do try{if(vt!==0&&rt!==null){var p=rt,b=be;switch(vt){case 8:Go(),f=6;break t;case 3:case 2:case 9:case 6:Ne.current===null&&(e=!0);var R=vt;if(vt=0,be=null,Za(t,p,b,R),n&&qa){f=0;break t}break;default:R=vt,vt=0,be=null,Za(t,p,b,R)}}dv(),f=Ct;break}catch(O){zd(t,O)}while(!0);return e&&t.shellSuspendCounter++,tn=$n=null,gt=a,C.H=s,C.A=r,rt===null&&(At=null,ft=0,ql()),f}function dv(){for(;rt!==null;)Bd(rt)}function mv(t,e){var n=gt;gt|=2;var a=_d(),s=Ud();At!==t||ft!==e?(gs=null,ys=Le()+500,Qa(t,e)):qa=oi(t,e);t:do try{if(vt!==0&&rt!==null){e=rt;var r=be;e:switch(vt){case 1:vt=0,be=null,Za(t,e,r,1);break;case 2:case 9:if(Pf(r)){vt=0,be=null,Hd(e);break}e=function(){vt!==2&&vt!==9||At!==t||(vt=7),Qe(t)},r.then(e,e);break t;case 3:vt=7;break t;case 4:vt=5;break t;case 7:Pf(r)?(vt=0,be=null,Hd(e)):(vt=0,be=null,Za(t,e,r,7));break;case 5:var f=null;switch(rt.tag){case 26:f=rt.memoizedState;case 5:case 27:var p=rt;if(!f||bm(f)){vt=0,be=null;var b=p.sibling;if(b!==null)rt=b;else{var R=p.return;R!==null?(rt=R,bs(R)):rt=null}break e}}vt=0,be=null,Za(t,e,r,5);break;case 6:vt=0,be=null,Za(t,e,r,6);break;case 8:Go(),Ct=6;break t;default:throw Error(o(462))}}pv();break}catch(O){zd(t,O)}while(!0);return tn=$n=null,C.H=a,C.A=s,gt=n,rt!==null?0:(At=null,ft=0,ql(),Ct)}function pv(){for(;rt!==null&&!By();)Bd(rt)}function Bd(t){var e=cd(t.alternate,t,rn);t.memoizedProps=t.pendingProps,e===null?bs(t):rt=e}function Hd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=id(n,e,e.pendingProps,e.type,void 0,ft);break;case 11:e=id(n,e,e.pendingProps,e.type.render,e.ref,ft);break;case 5:io(e);default:hd(n,e),e=rt=Lf(e,rn),e=cd(n,e,rn)}t.memoizedProps=t.pendingProps,e===null?bs(t):rt=e}function Za(t,e,n,a){tn=$n=null,io(e),Ua=null,Ui=0;var s=e.return;try{if(iv(t,s,e,n,ft)){Ct=1,cs(t,Me(n,t.current)),rt=null;return}}catch(r){if(s!==null)throw rt=s,r;Ct=1,cs(t,Me(n,t.current)),rt=null;return}e.flags&32768?(yt||a===1?t=!0:qa||(ft&536870912)!==0?t=!1:(Rn=t=!0,(a===2||a===9||a===3||a===6)&&(a=Ne.current,a!==null&&a.tag===13&&(a.flags|=16384))),Ld(e,t)):bs(e)}function bs(t){var e=t;do{if((e.flags&32768)!==0){Ld(e,Rn);return}t=e.return;var n=sv(e.alternate,e,rn);if(n!==null){rt=n;return}if(e=e.sibling,e!==null){rt=e;return}rt=e=t}while(e!==null);Ct===0&&(Ct=5)}function Ld(t,e){do{var n=uv(t.alternate,t);if(n!==null){n.flags&=32767,rt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){rt=t;return}rt=t=n}while(t!==null);Ct=6,rt=null}function qd(t,e,n,a,s,r,f,p,b){t.cancelPendingCommit=null;do xs();while(Ft!==0);if((gt&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(r=e.lanes|e.childLanes,r|=Vu,ky(t,n,r,f,p,b),t===At&&(rt=At=null,ft=0),Ga=e,On=t,Xa=n,Lo=r,qo=s,Od=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,bv(El,function(){return Zd(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=C.T,C.T=null,s=U.p,U.p=2,f=gt,gt|=4;try{ov(t,e,n)}finally{gt=f,U.p=s,C.T=a}}Ft=1,Yd(),Gd(),Xd()}}function Yd(){if(Ft===1){Ft=0;var t=On,e=Ga,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=C.T,C.T=null;var a=U.p;U.p=2;var s=gt;gt|=4;try{Ad(e,t);var r=nr,f=wf(t.containerInfo),p=r.focusedElem,b=r.selectionRange;if(f!==p&&p&&p.ownerDocument&&Nf(p.ownerDocument.documentElement,p)){if(b!==null&&Nu(p)){var R=b.start,O=b.end;if(O===void 0&&(O=R),"selectionStart"in p)p.selectionStart=R,p.selectionEnd=Math.min(O,p.value.length);else{var V=p.ownerDocument||document,N=V&&V.defaultView||window;if(N.getSelection){var w=N.getSelection(),et=p.textContent.length,W=Math.min(b.start,et),St=b.end===void 0?W:Math.min(b.end,et);!w.extend&&W>St&&(f=St,St=W,W=f);var E=Rf(p,W),T=Rf(p,St);if(E&&T&&(w.rangeCount!==1||w.anchorNode!==E.node||w.anchorOffset!==E.offset||w.focusNode!==T.node||w.focusOffset!==T.offset)){var D=V.createRange();D.setStart(E.node,E.offset),w.removeAllRanges(),W>St?(w.addRange(D),w.extend(T.node,T.offset)):(D.setEnd(T.node,T.offset),w.addRange(D))}}}}for(V=[],w=p;w=w.parentNode;)w.nodeType===1&&V.push({element:w,left:w.scrollLeft,top:w.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var j=V[p];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}js=!!er,nr=er=null}finally{gt=s,U.p=a,C.T=n}}t.current=e,Ft=2}}function Gd(){if(Ft===2){Ft=0;var t=On,e=Ga,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=C.T,C.T=null;var a=U.p;U.p=2;var s=gt;gt|=4;try{bd(t,e.alternate,e)}finally{gt=s,U.p=a,C.T=n}}Ft=3}}function Xd(){if(Ft===4||Ft===3){Ft=0,Hy();var t=On,e=Ga,n=Xa,a=Od;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ft=5:(Ft=0,Ga=On=null,Qd(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(wn=null),uu(n),e=e.stateNode,de&&typeof de.onCommitFiberRoot=="function")try{de.onCommitFiberRoot(ui,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=C.T,s=U.p,U.p=2,C.T=null;try{for(var r=t.onRecoverableError,f=0;f<a.length;f++){var p=a[f];r(p.value,{componentStack:p.stack})}}finally{C.T=e,U.p=s}}(Xa&3)!==0&&xs(),Qe(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===Yo?Zi++:(Zi=0,Yo=t):Zi=0,Ki(0)}}function Qd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ei(e)))}function xs(t){return Yd(),Gd(),Xd(),Zd()}function Zd(){if(Ft!==5)return!1;var t=On,e=Lo;Lo=0;var n=uu(Xa),a=C.T,s=U.p;try{U.p=32>n?32:n,C.T=null,n=qo,qo=null;var r=On,f=Xa;if(Ft=0,Ga=On=null,Xa=0,(gt&6)!==0)throw Error(o(331));var p=gt;if(gt|=4,Nd(r.current),Ed(r,r.current,f,n),gt=p,Ki(0,!1),de&&typeof de.onPostCommitFiberRoot=="function")try{de.onPostCommitFiberRoot(ui,r)}catch{}return!0}finally{U.p=s,C.T=a,Qd(t,e)}}function Kd(t,e,n){e=Me(n,e),e=bo(t.stateNode,e,2),t=xn(t,e,2),t!==null&&(ri(t,2),Qe(t))}function Tt(t,e,n){if(t.tag===3)Kd(t,t,n);else for(;e!==null;){if(e.tag===3){Kd(e,t,n);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(wn===null||!wn.has(a))){t=Me(n,t),n=Fh(2),a=xn(e,n,2),a!==null&&(Wh(n,a,e,t),ri(a,2),Qe(a));break}}e=e.return}}function Zo(t,e,n){var a=t.pingCache;if(a===null){a=t.pingCache=new fv;var s=new Set;a.set(e,s)}else s=a.get(e),s===void 0&&(s=new Set,a.set(e,s));s.has(n)||(_o=!0,s.add(n),t=yv.bind(null,t,e,n),e.then(t,t))}function yv(t,e,n){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,At===t&&(ft&n)===n&&(Ct===4||Ct===3&&(ft&62914560)===ft&&300>Le()-Ho?(gt&2)===0&&Qa(t,0):Uo|=n,Ya===ft&&(Ya=0)),Qe(t)}function kd(t,e){e===0&&(e=Gc()),t=Da(t,e),t!==null&&(ri(t,e),Qe(t))}function gv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),kd(t,n)}function vv(t,e){var n=0;switch(t.tag){case 13:var a=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(o(314))}a!==null&&a.delete(e),kd(t,n)}function bv(t,e){return au(t,e)}var Ss=null,Ka=null,Ko=!1,Ts=!1,ko=!1,la=0;function Qe(t){t!==Ka&&t.next===null&&(Ka===null?Ss=Ka=t:Ka=Ka.next=t),Ts=!0,Ko||(Ko=!0,Sv())}function Ki(t,e){if(!ko&&Ts){ko=!0;do for(var n=!1,a=Ss;a!==null;){if(t!==0){var s=a.pendingLanes;if(s===0)var r=0;else{var f=a.suspendedLanes,p=a.pingedLanes;r=(1<<31-me(42|t)+1)-1,r&=s&~(f&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Wd(a,r))}else r=ft,r=Nl(a,a===At?r:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(r&3)===0||oi(a,r)||(n=!0,Wd(a,r));a=a.next}while(n);ko=!1}}function xv(){Jd()}function Jd(){Ts=Ko=!1;var t=0;la!==0&&(wv()&&(t=la),la=0);for(var e=Le(),n=null,a=Ss;a!==null;){var s=a.next,r=Pd(a,e);r===0?(a.next=null,n===null?Ss=s:n.next=s,s===null&&(Ka=n)):(n=a,(t!==0||(r&3)!==0)&&(Ts=!0)),a=s}Ki(t)}function Pd(t,e){for(var n=t.suspendedLanes,a=t.pingedLanes,s=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var f=31-me(r),p=1<<f,b=s[f];b===-1?((p&n)===0||(p&a)!==0)&&(s[f]=Ky(p,e)):b<=e&&(t.expiredLanes|=p),r&=~p}if(e=At,n=ft,n=Nl(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,n===0||t===e&&(vt===2||vt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&iu(a),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||oi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(a!==null&&iu(a),uu(n)){case 2:case 8:n=Lc;break;case 32:n=El;break;case 268435456:n=qc;break;default:n=El}return a=Fd.bind(null,t),n=au(n,a),t.callbackPriority=e,t.callbackNode=n,e}return a!==null&&a!==null&&iu(a),t.callbackPriority=2,t.callbackNode=null,2}function Fd(t,e){if(Ft!==0&&Ft!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(xs()&&t.callbackNode!==n)return null;var a=ft;return a=Nl(t,t===At?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(jd(t,a,e),Pd(t,Le()),t.callbackNode!=null&&t.callbackNode===n?Fd.bind(null,t):null)}function Wd(t,e){if(xs())return null;jd(t,e,!0)}function Sv(){Cv(function(){(gt&6)!==0?au(Hc,xv):Jd()})}function Jo(){return la===0&&(la=Yc()),la}function $d(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Vl(""+t)}function Id(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Tv(t,e,n,a,s){if(e==="submit"&&n&&n.stateNode===s){var r=$d((s[se]||null).action),f=a.submitter;f&&(e=(e=f[se]||null)?$d(e.formAction):f.getAttribute("formAction"),e!==null&&(r=e,f=null));var p=new Bl("action","action",null,a,s);t.push({event:p,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(la!==0){var b=f?Id(s,f):new FormData(s);mo(n,{pending:!0,data:b,method:s.method,action:r},null,b)}}else typeof r=="function"&&(p.preventDefault(),b=f?Id(s,f):new FormData(s),mo(n,{pending:!0,data:b,method:s.method,action:r},r,b))},currentTarget:s}]})}}for(var Po=0;Po<ju.length;Po++){var Fo=ju[Po],Av=Fo.toLowerCase(),Mv=Fo[0].toUpperCase()+Fo.slice(1);ze(Av,"on"+Mv)}ze(jf,"onAnimationEnd"),ze(Vf,"onAnimationIteration"),ze(zf,"onAnimationStart"),ze("dblclick","onDoubleClick"),ze("focusin","onFocus"),ze("focusout","onBlur"),ze(Yg,"onTransitionRun"),ze(Gg,"onTransitionStart"),ze(Xg,"onTransitionCancel"),ze(_f,"onTransitionEnd"),ya("onMouseEnter",["mouseout","mouseover"]),ya("onMouseLeave",["mouseout","mouseover"]),ya("onPointerEnter",["pointerout","pointerover"]),ya("onPointerLeave",["pointerout","pointerover"]),Xn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ki="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ev=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ki));function tm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var a=t[n],s=a.event;a=a.listeners;t:{var r=void 0;if(e)for(var f=a.length-1;0<=f;f--){var p=a[f],b=p.instance,R=p.currentTarget;if(p=p.listener,b!==r&&s.isPropagationStopped())break t;r=p,s.currentTarget=R;try{r(s)}catch(O){rs(O)}s.currentTarget=null,r=b}else for(f=0;f<a.length;f++){if(p=a[f],b=p.instance,R=p.currentTarget,p=p.listener,b!==r&&s.isPropagationStopped())break t;r=p,s.currentTarget=R;try{r(s)}catch(O){rs(O)}s.currentTarget=null,r=b}}}}function ct(t,e){var n=e[ou];n===void 0&&(n=e[ou]=new Set);var a=t+"__bubble";n.has(a)||(em(e,t,2,!1),n.add(a))}function Wo(t,e,n){var a=0;e&&(a|=4),em(n,t,a,e)}var As="_reactListening"+Math.random().toString(36).slice(2);function $o(t){if(!t[As]){t[As]=!0,kc.forEach(function(n){n!=="selectionchange"&&(Ev.has(n)||Wo(n,!1,t),Wo(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[As]||(e[As]=!0,Wo("selectionchange",!1,e))}}function em(t,e,n,a){switch(Em(e)){case 2:var s=$v;break;case 8:s=Iv;break;default:s=hr}n=s.bind(null,e,n,t),s=void 0,!bu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),a?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function Io(t,e,n,a,s){var r=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var p=a.stateNode.containerInfo;if(p===s)break;if(f===4)for(f=a.return;f!==null;){var b=f.tag;if((b===3||b===4)&&f.stateNode.containerInfo===s)return;f=f.return}for(;p!==null;){if(f=da(p),f===null)return;if(b=f.tag,b===5||b===6||b===26||b===27){a=r=f;continue t}p=p.parentNode}}a=a.return}of(function(){var R=r,O=gu(n),V=[];t:{var N=Uf.get(t);if(N!==void 0){var w=Bl,et=t;switch(t){case"keypress":if(_l(n)===0)break t;case"keydown":case"keyup":w=bg;break;case"focusin":et="focus",w=Au;break;case"focusout":et="blur",w=Au;break;case"beforeblur":case"afterblur":w=Au;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=ug;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Tg;break;case jf:case Vf:case zf:w=cg;break;case _f:w=Mg;break;case"scroll":case"scrollend":w=lg;break;case"wheel":w=Dg;break;case"copy":case"cut":case"paste":w=hg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=df;break;case"toggle":case"beforetoggle":w=Ng}var W=(e&4)!==0,St=!W&&(t==="scroll"||t==="scrollend"),E=W?N!==null?N+"Capture":null:N;W=[];for(var T=R,D;T!==null;){var j=T;if(D=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||D===null||E===null||(j=hi(T,E),j!=null&&W.push(Ji(T,j,D))),St)break;T=T.return}0<W.length&&(N=new w(N,et,null,n,O),V.push({event:N,listeners:W}))}}if((e&7)===0){t:{if(N=t==="mouseover"||t==="pointerover",w=t==="mouseout"||t==="pointerout",N&&n!==yu&&(et=n.relatedTarget||n.fromElement)&&(da(et)||et[ha]))break t;if((w||N)&&(N=O.window===O?O:(N=O.ownerDocument)?N.defaultView||N.parentWindow:window,w?(et=n.relatedTarget||n.toElement,w=R,et=et?da(et):null,et!==null&&(St=d(et),W=et.tag,et!==St||W!==5&&W!==27&&W!==6)&&(et=null)):(w=null,et=R),w!==et)){if(W=ff,j="onMouseLeave",E="onMouseEnter",T="mouse",(t==="pointerout"||t==="pointerover")&&(W=df,j="onPointerLeave",E="onPointerEnter",T="pointer"),St=w==null?N:fi(w),D=et==null?N:fi(et),N=new W(j,T+"leave",w,n,O),N.target=St,N.relatedTarget=D,j=null,da(O)===R&&(W=new W(E,T+"enter",et,n,O),W.target=D,W.relatedTarget=St,j=W),St=j,w&&et)e:{for(W=w,E=et,T=0,D=W;D;D=ka(D))T++;for(D=0,j=E;j;j=ka(j))D++;for(;0<T-D;)W=ka(W),T--;for(;0<D-T;)E=ka(E),D--;for(;T--;){if(W===E||E!==null&&W===E.alternate)break e;W=ka(W),E=ka(E)}W=null}else W=null;w!==null&&nm(V,N,w,W,!1),et!==null&&St!==null&&nm(V,St,et,W,!0)}}t:{if(N=R?fi(R):window,w=N.nodeName&&N.nodeName.toLowerCase(),w==="select"||w==="input"&&N.type==="file")var Z=Sf;else if(bf(N))if(Tf)Z=Hg;else{Z=Ug;var ot=_g}else w=N.nodeName,!w||w.toLowerCase()!=="input"||N.type!=="checkbox"&&N.type!=="radio"?R&&pu(R.elementType)&&(Z=Sf):Z=Bg;if(Z&&(Z=Z(t,R))){xf(V,Z,n,O);break t}ot&&ot(t,N,R),t==="focusout"&&R&&N.type==="number"&&R.memoizedProps.value!=null&&mu(N,"number",N.value)}switch(ot=R?fi(R):window,t){case"focusin":(bf(ot)||ot.contentEditable==="true")&&(Aa=ot,wu=R,xi=null);break;case"focusout":xi=wu=Aa=null;break;case"mousedown":Ou=!0;break;case"contextmenu":case"mouseup":case"dragend":Ou=!1,Of(V,n,O);break;case"selectionchange":if(qg)break;case"keydown":case"keyup":Of(V,n,O)}var J;if(Eu)t:{switch(t){case"compositionstart":var I="onCompositionStart";break t;case"compositionend":I="onCompositionEnd";break t;case"compositionupdate":I="onCompositionUpdate";break t}I=void 0}else Ta?gf(t,n)&&(I="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(I="onCompositionStart");I&&(mf&&n.locale!=="ko"&&(Ta||I!=="onCompositionStart"?I==="onCompositionEnd"&&Ta&&(J=rf()):(yn=O,xu="value"in yn?yn.value:yn.textContent,Ta=!0)),ot=Ms(R,I),0<ot.length&&(I=new hf(I,t,null,n,O),V.push({event:I,listeners:ot}),J?I.data=J:(J=vf(n),J!==null&&(I.data=J)))),(J=Og?Cg(t,n):jg(t,n))&&(I=Ms(R,"onBeforeInput"),0<I.length&&(ot=new hf("onBeforeInput","beforeinput",null,n,O),V.push({event:ot,listeners:I}),ot.data=J)),Tv(V,t,R,n,O)}tm(V,e)})}function Ji(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Ms(t,e){for(var n=e+"Capture",a=[];t!==null;){var s=t,r=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||r===null||(s=hi(t,n),s!=null&&a.unshift(Ji(t,s,r)),s=hi(t,e),s!=null&&a.push(Ji(t,s,r))),t.tag===3)return a;t=t.return}return[]}function ka(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function nm(t,e,n,a,s){for(var r=e._reactName,f=[];n!==null&&n!==a;){var p=n,b=p.alternate,R=p.stateNode;if(p=p.tag,b!==null&&b===a)break;p!==5&&p!==26&&p!==27||R===null||(b=R,s?(R=hi(n,r),R!=null&&f.unshift(Ji(n,R,b))):s||(R=hi(n,r),R!=null&&f.push(Ji(n,R,b)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var Dv=/\r\n?/g,Rv=/\u0000|\uFFFD/g;function am(t){return(typeof t=="string"?t:""+t).replace(Dv,`
`).replace(Rv,"")}function im(t,e){return e=am(e),am(t)===e}function Es(){}function xt(t,e,n,a,s,r){switch(n){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ba(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ba(t,""+a);break;case"className":Ol(t,"class",a);break;case"tabIndex":Ol(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ol(t,n,a);break;case"style":sf(t,a,r);break;case"data":if(e!=="object"){Ol(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=Vl(""+a),t.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&xt(t,e,"name",s.name,s,null),xt(t,e,"formEncType",s.formEncType,s,null),xt(t,e,"formMethod",s.formMethod,s,null),xt(t,e,"formTarget",s.formTarget,s,null)):(xt(t,e,"encType",s.encType,s,null),xt(t,e,"method",s.method,s,null),xt(t,e,"target",s.target,s,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(n);break}a=Vl(""+a),t.setAttribute(n,a);break;case"onClick":a!=null&&(t.onclick=Es);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}n=Vl(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""+a):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":a===!0?t.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(n,a):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(n,a):t.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(n):t.setAttribute(n,a);break;case"popover":ct("beforetoggle",t),ct("toggle",t),wl(t,"popover",a);break;case"xlinkActuate":Pe(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Pe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Pe(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Pe(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Pe(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Pe(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":wl(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=ag.get(n)||n,wl(t,n,a))}}function tr(t,e,n,a,s,r){switch(n){case"style":sf(t,a,r);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(o(61));if(n=a.__html,n!=null){if(s.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof a=="string"?ba(t,a):(typeof a=="number"||typeof a=="bigint")&&ba(t,""+a);break;case"onScroll":a!=null&&ct("scroll",t);break;case"onScrollEnd":a!=null&&ct("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Es);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Jc.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),r=t[se]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,s),typeof a=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,a,s);break t}n in t?t[n]=a:a===!0?t.setAttribute(n,""):wl(t,n,a)}}}function Wt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ct("error",t),ct("load",t);var a=!1,s=!1,r;for(r in n)if(n.hasOwnProperty(r)){var f=n[r];if(f!=null)switch(r){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:xt(t,e,r,f,n,null)}}s&&xt(t,e,"srcSet",n.srcSet,n,null),a&&xt(t,e,"src",n.src,n,null);return;case"input":ct("invalid",t);var p=r=f=s=null,b=null,R=null;for(a in n)if(n.hasOwnProperty(a)){var O=n[a];if(O!=null)switch(a){case"name":s=O;break;case"type":f=O;break;case"checked":b=O;break;case"defaultChecked":R=O;break;case"value":r=O;break;case"defaultValue":p=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(o(137,e));break;default:xt(t,e,a,O,n,null)}}ef(t,r,p,b,R,f,s,!1),Cl(t);return;case"select":ct("invalid",t),a=f=r=null;for(s in n)if(n.hasOwnProperty(s)&&(p=n[s],p!=null))switch(s){case"value":r=p;break;case"defaultValue":f=p;break;case"multiple":a=p;default:xt(t,e,s,p,n,null)}e=r,n=f,t.multiple=!!a,e!=null?va(t,!!a,e,!1):n!=null&&va(t,!!a,n,!0);return;case"textarea":ct("invalid",t),r=s=a=null;for(f in n)if(n.hasOwnProperty(f)&&(p=n[f],p!=null))switch(f){case"value":a=p;break;case"defaultValue":s=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(o(91));break;default:xt(t,e,f,p,n,null)}af(t,a,s,r),Cl(t);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(a=n[b],a!=null))switch(b){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:xt(t,e,b,a,n,null)}return;case"dialog":ct("beforetoggle",t),ct("toggle",t),ct("cancel",t),ct("close",t);break;case"iframe":case"object":ct("load",t);break;case"video":case"audio":for(a=0;a<ki.length;a++)ct(ki[a],t);break;case"image":ct("error",t),ct("load",t);break;case"details":ct("toggle",t);break;case"embed":case"source":case"link":ct("error",t),ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(R in n)if(n.hasOwnProperty(R)&&(a=n[R],a!=null))switch(R){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:xt(t,e,R,a,n,null)}return;default:if(pu(e)){for(O in n)n.hasOwnProperty(O)&&(a=n[O],a!==void 0&&tr(t,e,O,a,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(a=n[p],a!=null&&xt(t,e,p,a,n,null))}function Nv(t,e,n,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,r=null,f=null,p=null,b=null,R=null,O=null;for(w in n){var V=n[w];if(n.hasOwnProperty(w)&&V!=null)switch(w){case"checked":break;case"value":break;case"defaultValue":b=V;default:a.hasOwnProperty(w)||xt(t,e,w,null,a,V)}}for(var N in a){var w=a[N];if(V=n[N],a.hasOwnProperty(N)&&(w!=null||V!=null))switch(N){case"type":r=w;break;case"name":s=w;break;case"checked":R=w;break;case"defaultChecked":O=w;break;case"value":f=w;break;case"defaultValue":p=w;break;case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(o(137,e));break;default:w!==V&&xt(t,e,N,w,a,V)}}du(t,f,p,b,R,O,r,s);return;case"select":w=f=p=N=null;for(r in n)if(b=n[r],n.hasOwnProperty(r)&&b!=null)switch(r){case"value":break;case"multiple":w=b;default:a.hasOwnProperty(r)||xt(t,e,r,null,a,b)}for(s in a)if(r=a[s],b=n[s],a.hasOwnProperty(s)&&(r!=null||b!=null))switch(s){case"value":N=r;break;case"defaultValue":p=r;break;case"multiple":f=r;default:r!==b&&xt(t,e,s,r,a,b)}e=p,n=f,a=w,N!=null?va(t,!!n,N,!1):!!a!=!!n&&(e!=null?va(t,!!n,e,!0):va(t,!!n,n?[]:"",!1));return;case"textarea":w=N=null;for(p in n)if(s=n[p],n.hasOwnProperty(p)&&s!=null&&!a.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:xt(t,e,p,null,a,s)}for(f in a)if(s=a[f],r=n[f],a.hasOwnProperty(f)&&(s!=null||r!=null))switch(f){case"value":N=s;break;case"defaultValue":w=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==r&&xt(t,e,f,s,a,r)}nf(t,N,w);return;case"option":for(var et in n)if(N=n[et],n.hasOwnProperty(et)&&N!=null&&!a.hasOwnProperty(et))switch(et){case"selected":t.selected=!1;break;default:xt(t,e,et,null,a,N)}for(b in a)if(N=a[b],w=n[b],a.hasOwnProperty(b)&&N!==w&&(N!=null||w!=null))switch(b){case"selected":t.selected=N&&typeof N!="function"&&typeof N!="symbol";break;default:xt(t,e,b,N,a,w)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var W in n)N=n[W],n.hasOwnProperty(W)&&N!=null&&!a.hasOwnProperty(W)&&xt(t,e,W,null,a,N);for(R in a)if(N=a[R],w=n[R],a.hasOwnProperty(R)&&N!==w&&(N!=null||w!=null))switch(R){case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(o(137,e));break;default:xt(t,e,R,N,a,w)}return;default:if(pu(e)){for(var St in n)N=n[St],n.hasOwnProperty(St)&&N!==void 0&&!a.hasOwnProperty(St)&&tr(t,e,St,void 0,a,N);for(O in a)N=a[O],w=n[O],!a.hasOwnProperty(O)||N===w||N===void 0&&w===void 0||tr(t,e,O,N,a,w);return}}for(var E in n)N=n[E],n.hasOwnProperty(E)&&N!=null&&!a.hasOwnProperty(E)&&xt(t,e,E,null,a,N);for(V in a)N=a[V],w=n[V],!a.hasOwnProperty(V)||N===w||N==null&&w==null||xt(t,e,V,N,a,w)}var er=null,nr=null;function Ds(t){return t.nodeType===9?t:t.ownerDocument}function lm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ar(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ir=null;function wv(){var t=window.event;return t&&t.type==="popstate"?t===ir?!1:(ir=t,!0):(ir=null,!1)}var um=typeof setTimeout=="function"?setTimeout:void 0,Ov=typeof clearTimeout=="function"?clearTimeout:void 0,om=typeof Promise=="function"?Promise:void 0,Cv=typeof queueMicrotask=="function"?queueMicrotask:typeof om<"u"?function(t){return om.resolve(null).then(t).catch(jv)}:um;function jv(t){setTimeout(function(){throw t})}function jn(t){return t==="head"}function rm(t,e){var n=e,a=0,s=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<a&&8>a){n=a;var f=t.ownerDocument;if(n&1&&Pi(f.documentElement),n&2&&Pi(f.body),n&4)for(n=f.head,Pi(n),f=n.firstChild;f;){var p=f.nextSibling,b=f.nodeName;f[ci]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=p}}if(s===0){t.removeChild(r),al(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:a=n.charCodeAt(0)-48;else a=0;n=r}while(n);al(e)}function lr(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":lr(n),ru(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Vv(t,e,n,a){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[ci])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ue(t.nextSibling),t===null)break}return null}function zv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ue(t.nextSibling),t===null))return null;return t}function sr(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function _v(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var a=function(){e(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Ue(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ur=null;function cm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function fm(t,e,n){switch(e=Ds(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function Pi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ru(t)}var Oe=new Map,hm=new Set;function Rs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var cn=U.d;U.d={f:Uv,r:Bv,D:Hv,C:Lv,L:qv,m:Yv,X:Xv,S:Gv,M:Qv};function Uv(){var t=cn.f(),e=vs();return t||e}function Bv(t){var e=ma(t);e!==null&&e.tag===5&&e.type==="form"?jh(e):cn.r(t)}var Ja=typeof document>"u"?null:document;function dm(t,e,n){var a=Ja;if(a&&typeof e=="string"&&e){var s=Ae(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),hm.has(s)||(hm.add(s),t={rel:t,crossOrigin:n,href:e},a.querySelector(s)===null&&(e=a.createElement("link"),Wt(e,"link",t),Qt(e),a.head.appendChild(e)))}}function Hv(t){cn.D(t),dm("dns-prefetch",t,null)}function Lv(t,e){cn.C(t,e),dm("preconnect",t,e)}function qv(t,e,n){cn.L(t,e,n);var a=Ja;if(a&&t&&e){var s='link[rel="preload"][as="'+Ae(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Ae(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Ae(n.imageSizes)+'"]')):s+='[href="'+Ae(t)+'"]';var r=s;switch(e){case"style":r=Pa(t);break;case"script":r=Fa(t)}Oe.has(r)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Oe.set(r,t),a.querySelector(s)!==null||e==="style"&&a.querySelector(Fi(r))||e==="script"&&a.querySelector(Wi(r))||(e=a.createElement("link"),Wt(e,"link",t),Qt(e),a.head.appendChild(e)))}}function Yv(t,e){cn.m(t,e);var n=Ja;if(n&&t){var a=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Ae(a)+'"][href="'+Ae(t)+'"]',r=s;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Fa(t)}if(!Oe.has(r)&&(t=v({rel:"modulepreload",href:t},e),Oe.set(r,t),n.querySelector(s)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Wi(r)))return}a=n.createElement("link"),Wt(a,"link",t),Qt(a),n.head.appendChild(a)}}}function Gv(t,e,n){cn.S(t,e,n);var a=Ja;if(a&&t){var s=pa(a).hoistableStyles,r=Pa(t);e=e||"default";var f=s.get(r);if(!f){var p={loading:0,preload:null};if(f=a.querySelector(Fi(r)))p.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Oe.get(r))&&or(t,n);var b=f=a.createElement("link");Qt(b),Wt(b,"link",t),b._p=new Promise(function(R,O){b.onload=R,b.onerror=O}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,Ns(f,e,a)}f={type:"stylesheet",instance:f,count:1,state:p},s.set(r,f)}}}function Xv(t,e){cn.X(t,e);var n=Ja;if(n&&t){var a=pa(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector(Wi(s)),r||(t=v({src:t,async:!0},e),(e=Oe.get(s))&&rr(t,e),r=n.createElement("script"),Qt(r),Wt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function Qv(t,e){cn.M(t,e);var n=Ja;if(n&&t){var a=pa(n).hoistableScripts,s=Fa(t),r=a.get(s);r||(r=n.querySelector(Wi(s)),r||(t=v({src:t,async:!0,type:"module"},e),(e=Oe.get(s))&&rr(t,e),r=n.createElement("script"),Qt(r),Wt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(s,r))}}function mm(t,e,n,a){var s=(s=it.current)?Rs(s):null;if(!s)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Pa(n.href),n=pa(s).hoistableStyles,a=n.get(e),a||(a={type:"style",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Pa(n.href);var r=pa(s).hoistableStyles,f=r.get(t);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,f),(r=s.querySelector(Fi(t)))&&!r._p&&(f.instance=r,f.state.loading=5),Oe.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Oe.set(t,n),r||Zv(s,t,n,f.state))),e&&a===null)throw Error(o(528,""));return f}if(e&&a!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Fa(n),n=pa(s).hoistableScripts,a=n.get(e),a||(a={type:"script",instance:null,count:0,state:null},n.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function Pa(t){return'href="'+Ae(t)+'"'}function Fi(t){return'link[rel="stylesheet"]['+t+"]"}function pm(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function Zv(t,e,n,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Wt(e,"link",n),Qt(e),t.head.appendChild(e))}function Fa(t){return'[src="'+Ae(t)+'"]'}function Wi(t){return"script[async]"+t}function ym(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Ae(n.href)+'"]');if(a)return e.instance=a,Qt(a),a;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Qt(a),Wt(a,"style",s),Ns(a,n.precedence,t),e.instance=a;case"stylesheet":s=Pa(n.href);var r=t.querySelector(Fi(s));if(r)return e.state.loading|=4,e.instance=r,Qt(r),r;a=pm(n),(s=Oe.get(s))&&or(a,s),r=(t.ownerDocument||t).createElement("link"),Qt(r);var f=r;return f._p=new Promise(function(p,b){f.onload=p,f.onerror=b}),Wt(r,"link",a),e.state.loading|=4,Ns(r,n.precedence,t),e.instance=r;case"script":return r=Fa(n.src),(s=t.querySelector(Wi(r)))?(e.instance=s,Qt(s),s):(a=n,(s=Oe.get(r))&&(a=v({},n),rr(a,s)),t=t.ownerDocument||t,s=t.createElement("script"),Qt(s),Wt(s,"link",a),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ns(a,n.precedence,t));return e.instance}function Ns(t,e,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=a.length?a[a.length-1]:null,r=s,f=0;f<a.length;f++){var p=a[f];if(p.dataset.precedence===e)r=p;else if(r!==s)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function or(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function rr(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ws=null;function gm(t,e,n){if(ws===null){var a=new Map,s=ws=new Map;s.set(n,a)}else s=ws,a=s.get(n),a||(a=new Map,s.set(n,a));if(a.has(t))return a;for(a.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var r=n[s];if(!(r[ci]||r[te]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(e)||"";f=t+f;var p=a.get(f);p?p.push(r):a.set(f,[r])}}return a}function vm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Kv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function bm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var $i=null;function kv(){}function Jv(t,e,n){if($i===null)throw Error(o(475));var a=$i;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Pa(n.href),r=t.querySelector(Fi(s));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Os.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=r,Qt(r);return}r=t.ownerDocument||t,n=pm(n),(s=Oe.get(s))&&or(n,s),r=r.createElement("link"),Qt(r);var f=r;f._p=new Promise(function(p,b){f.onload=p,f.onerror=b}),Wt(r,"link",n),e.instance=r}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Os.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function Pv(){if($i===null)throw Error(o(475));var t=$i;return t.stylesheets&&t.count===0&&cr(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&cr(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Os(){if(this.count--,this.count===0){if(this.stylesheets)cr(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Cs=null;function cr(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Cs=new Map,e.forEach(Fv,t),Cs=null,Os.call(t))}function Fv(t,e){if(!(e.state.loading&4)){var n=Cs.get(t);if(n)var a=n.get(null);else{n=new Map,Cs.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<s.length;r++){var f=s[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),a=f)}a&&n.set(null,a)}s=e.instance,f=s.getAttribute("data-precedence"),r=n.get(f)||a,r===a&&n.set(null,s),n.set(f,s),this.count++,a=Os.bind(this),s.addEventListener("load",a),s.addEventListener("error",a),r?r.parentNode.insertBefore(s,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var Ii={$$typeof:L,Provider:null,Consumer:null,_currentValue:k,_currentValue2:k,_threadCount:0};function Wv(t,e,n,a,s,r,f,p){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lu(0),this.hiddenUpdates=lu(null),this.identifierPrefix=a,this.onUncaughtError=s,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function xm(t,e,n,a,s,r,f,p,b,R,O,V){return t=new Wv(t,e,n,f,p,b,R,V),e=1,r===!0&&(e|=24),r=ye(3,null,null,e),t.current=r,r.stateNode=t,e=Qu(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:a,isDehydrated:n,cache:e},Ju(r),t}function Sm(t){return t?(t=Ra,t):Ra}function Tm(t,e,n,a,s,r){s=Sm(s),a.context===null?a.context=s:a.pendingContext=s,a=bn(e),a.payload={element:n},r=r===void 0?null:r,r!==null&&(a.callback=r),n=xn(t,a,e),n!==null&&(Se(n,t,e),wi(n,t,e))}function Am(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function fr(t,e){Am(t,e),(t=t.alternate)&&Am(t,e)}function Mm(t){if(t.tag===13){var e=Da(t,67108864);e!==null&&Se(e,t,67108864),fr(t,67108864)}}var js=!0;function $v(t,e,n,a){var s=C.T;C.T=null;var r=U.p;try{U.p=2,hr(t,e,n,a)}finally{U.p=r,C.T=s}}function Iv(t,e,n,a){var s=C.T;C.T=null;var r=U.p;try{U.p=8,hr(t,e,n,a)}finally{U.p=r,C.T=s}}function hr(t,e,n,a){if(js){var s=dr(a);if(s===null)Io(t,e,a,Vs,n),Dm(t,a);else if(e1(s,t,e,n,a))a.stopPropagation();else if(Dm(t,a),e&4&&-1<t1.indexOf(t)){for(;s!==null;){var r=ma(s);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=Gn(r.pendingLanes);if(f!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;f;){var b=1<<31-me(f);p.entanglements[1]|=b,f&=~b}Qe(r),(gt&6)===0&&(ys=Le()+500,Ki(0))}}break;case 13:p=Da(r,2),p!==null&&Se(p,r,2),vs(),fr(r,2)}if(r=dr(a),r===null&&Io(t,e,a,Vs,n),r===s)break;s=r}s!==null&&a.stopPropagation()}else Io(t,e,a,null,n)}}function dr(t){return t=gu(t),mr(t)}var Vs=null;function mr(t){if(Vs=null,t=da(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=h(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Vs=t,null}function Em(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ly()){case Hc:return 2;case Lc:return 8;case El:case qy:return 32;case qc:return 268435456;default:return 32}default:return 32}}var pr=!1,Vn=null,zn=null,_n=null,tl=new Map,el=new Map,Un=[],t1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Dm(t,e){switch(t){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":zn=null;break;case"mouseover":case"mouseout":_n=null;break;case"pointerover":case"pointerout":tl.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":el.delete(e.pointerId)}}function nl(t,e,n,a,s,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:a,nativeEvent:r,targetContainers:[s]},e!==null&&(e=ma(e),e!==null&&Mm(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function e1(t,e,n,a,s){switch(e){case"focusin":return Vn=nl(Vn,t,e,n,a,s),!0;case"dragenter":return zn=nl(zn,t,e,n,a,s),!0;case"mouseover":return _n=nl(_n,t,e,n,a,s),!0;case"pointerover":var r=s.pointerId;return tl.set(r,nl(tl.get(r)||null,t,e,n,a,s)),!0;case"gotpointercapture":return r=s.pointerId,el.set(r,nl(el.get(r)||null,t,e,n,a,s)),!0}return!1}function Rm(t){var e=da(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=h(n),e!==null){t.blockedOn=e,Jy(t.priority,function(){if(n.tag===13){var a=xe();a=su(a);var s=Da(n,a);s!==null&&Se(s,n,a),fr(n,a)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=dr(t.nativeEvent);if(n===null){n=t.nativeEvent;var a=new n.constructor(n.type,n);yu=a,n.target.dispatchEvent(a),yu=null}else return e=ma(n),e!==null&&Mm(e),t.blockedOn=n,!1;e.shift()}return!0}function Nm(t,e,n){zs(t)&&n.delete(e)}function n1(){pr=!1,Vn!==null&&zs(Vn)&&(Vn=null),zn!==null&&zs(zn)&&(zn=null),_n!==null&&zs(_n)&&(_n=null),tl.forEach(Nm),el.forEach(Nm)}function _s(t,e){t.blockedOn===e&&(t.blockedOn=null,pr||(pr=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,n1)))}var Us=null;function wm(t){Us!==t&&(Us=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Us===t&&(Us=null);for(var e=0;e<t.length;e+=3){var n=t[e],a=t[e+1],s=t[e+2];if(typeof a!="function"){if(mr(a||n)===null)continue;break}var r=ma(n);r!==null&&(t.splice(e,3),e-=3,mo(r,{pending:!0,data:s,method:n.method,action:a},a,s))}}))}function al(t){function e(b){return _s(b,t)}Vn!==null&&_s(Vn,t),zn!==null&&_s(zn,t),_n!==null&&_s(_n,t),tl.forEach(e),el.forEach(e);for(var n=0;n<Un.length;n++){var a=Un[n];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Un.length&&(n=Un[0],n.blockedOn===null);)Rm(n),n.blockedOn===null&&Un.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var s=n[a],r=n[a+1],f=s[se]||null;if(typeof r=="function")f||wm(n);else if(f){var p=null;if(r&&r.hasAttribute("formAction")){if(s=r,f=r[se]||null)p=f.formAction;else if(mr(s)!==null)continue}else p=f.action;typeof p=="function"?n[a+1]=p:(n.splice(a,3),a-=3),wm(n)}}}function yr(t){this._internalRoot=t}Bs.prototype.render=yr.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,a=xe();Tm(n,a,t,e,null,null)},Bs.prototype.unmount=yr.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Tm(t.current,2,null,t,null,null),vs(),e[ha]=null}};function Bs(t){this._internalRoot=t}Bs.prototype.unstable_scheduleHydration=function(t){if(t){var e=Zc();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Un.length&&e!==0&&e<Un[n].priority;n++);Un.splice(n,0,t),n===0&&Rm(t)}};var Om=l.version;if(Om!=="19.1.0")throw Error(o(527,Om,"19.1.0"));U.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=y(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var a1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hs.isDisabled&&Hs.supportsFiber)try{ui=Hs.inject(a1),de=Hs}catch{}}return ll.createRoot=function(t,e){if(!c(t))throw Error(o(299));var n=!1,a="",s=Kh,r=kh,f=Jh,p=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(p=e.unstable_transitionCallbacks)),e=xm(t,1,!1,null,null,n,a,s,r,f,p,null),t[ha]=e.current,$o(t),new yr(e)},ll.hydrateRoot=function(t,e,n){if(!c(t))throw Error(o(299));var a=!1,s="",r=Kh,f=kh,p=Jh,b=null,R=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(R=n.formState)),e=xm(t,1,!0,e,n??null,a,s,r,f,p,b,R),e.context=Sm(null),n=e.current,a=xe(),a=su(a),s=bn(a),s.callback=null,xn(n,s,a),n=a,e.current.lanes=n,ri(e,n),Qe(e),t[ha]=e.current,$o(t),new Bs(e)},ll.version="19.1.0",ll}var qm;function d1(){if(qm)return br.exports;qm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(l){console.error(l)}}return i(),br.exports=h1(),br.exports}var m1=d1();const e0=P.createContext({});function p1(i){const l=P.useRef(null);return l.current===null&&(l.current=i()),l.current}const ic=typeof window<"u",y1=ic?P.useLayoutEffect:P.useEffect,lc=P.createContext(null);function sc(i,l){i.indexOf(l)===-1&&i.push(l)}function uc(i,l){const u=i.indexOf(l);u>-1&&i.splice(u,1)}const fn=(i,l,u)=>u>l?l:u<i?i:u;let oc=()=>{};const hn={},n0=i=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(i);function a0(i){return typeof i=="object"&&i!==null}const i0=i=>/^0[^.\s]+$/u.test(i);function rc(i){let l;return()=>(l===void 0&&(l=i()),l)}const Ve=i=>i,g1=(i,l)=>u=>l(i(u)),xl=(...i)=>i.reduce(g1),dl=(i,l,u)=>{const o=l-i;return o===0?1:(u-i)/o};class cc{constructor(){this.subscriptions=[]}add(l){return sc(this.subscriptions,l),()=>uc(this.subscriptions,l)}notify(l,u,o){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,o);else for(let d=0;d<c;d++){const h=this.subscriptions[d];h&&h(l,u,o)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ze=i=>i*1e3,Ke=i=>i/1e3;function l0(i,l){return l?i*(1e3/l):0}const s0=(i,l,u)=>(((1-3*u+3*l)*i+(3*u-6*l))*i+3*l)*i,v1=1e-7,b1=12;function x1(i,l,u,o,c){let d,h,g=0;do h=l+(u-l)/2,d=s0(h,o,c)-i,d>0?u=h:l=h;while(Math.abs(d)>v1&&++g<b1);return h}function Sl(i,l,u,o){if(i===l&&u===o)return Ve;const c=d=>x1(d,0,1,i,u);return d=>d===0||d===1?d:s0(c(d),l,o)}const u0=i=>l=>l<=.5?i(2*l)/2:(2-i(2*(1-l)))/2,o0=i=>l=>1-i(1-l),r0=Sl(.33,1.53,.69,.99),fc=o0(r0),c0=u0(fc),f0=i=>(i*=2)<1?.5*fc(i):.5*(2-Math.pow(2,-10*(i-1))),hc=i=>1-Math.sin(Math.acos(i)),h0=o0(hc),d0=u0(hc),S1=Sl(.42,0,1,1),T1=Sl(0,0,.58,1),m0=Sl(.42,0,.58,1),A1=i=>Array.isArray(i)&&typeof i[0]!="number",p0=i=>Array.isArray(i)&&typeof i[0]=="number",M1={linear:Ve,easeIn:S1,easeInOut:m0,easeOut:T1,circIn:hc,circInOut:d0,circOut:h0,backIn:fc,backInOut:c0,backOut:r0,anticipate:f0},E1=i=>typeof i=="string",Ym=i=>{if(p0(i)){oc(i.length===4);const[l,u,o,c]=i;return Sl(l,u,o,c)}else if(E1(i))return M1[i];return i},Ls=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Gm={value:null};function D1(i,l){let u=new Set,o=new Set,c=!1,d=!1;const h=new WeakSet;let g={delta:0,timestamp:0,isProcessing:!1},y=0;function m(x){h.has(x)&&(v.schedule(x),i()),y++,x(g)}const v={schedule:(x,A=!1,z=!1)=>{const Q=z&&c?u:o;return A&&h.add(x),Q.has(x)||Q.add(x),x},cancel:x=>{o.delete(x),h.delete(x)},process:x=>{if(g=x,c){d=!0;return}c=!0,[u,o]=[o,u],u.forEach(m),l&&Gm.value&&Gm.value.frameloop[l].push(y),y=0,u.clear(),c=!1,d&&(d=!1,v.process(x))}};return v}const R1=40;function y0(i,l){let u=!1,o=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,h=Ls.reduce((L,at)=>(L[at]=D1(d,l?at:void 0),L),{}),{setup:g,read:y,resolveKeyframes:m,preUpdate:v,update:x,preRender:A,render:z,postRender:B}=h,Q=()=>{const L=hn.useManualTiming?c.timestamp:performance.now();u=!1,hn.useManualTiming||(c.delta=o?1e3/60:Math.max(Math.min(L-c.timestamp,R1),1)),c.timestamp=L,c.isProcessing=!0,g.process(c),y.process(c),m.process(c),v.process(c),x.process(c),A.process(c),z.process(c),B.process(c),c.isProcessing=!1,u&&l&&(o=!1,i(Q))},G=()=>{u=!0,o=!0,c.isProcessing||i(Q)};return{schedule:Ls.reduce((L,at)=>{const H=h[at];return L[at]=(st,ht=!1,$=!1)=>(u||G(),H.schedule(st,ht,$)),L},{}),cancel:L=>{for(let at=0;at<Ls.length;at++)h[Ls[at]].cancel(L)},state:c,steps:h}}const{schedule:Rt,cancel:Ln,state:$t,steps:Ar}=y0(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ve,!0);let Gs;function N1(){Gs=void 0}const fe={now:()=>(Gs===void 0&&fe.set($t.isProcessing||hn.useManualTiming?$t.timestamp:performance.now()),Gs),set:i=>{Gs=i,queueMicrotask(N1)}},g0=i=>l=>typeof l=="string"&&l.startsWith(i),dc=g0("--"),w1=g0("var(--"),mc=i=>w1(i)?O1.test(i.split("/*")[0].trim()):!1,O1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ii={test:i=>typeof i=="number",parse:parseFloat,transform:i=>i},ml={...ii,transform:i=>fn(0,1,i)},qs={...ii,default:1},ol=i=>Math.round(i*1e5)/1e5,pc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function C1(i){return i==null}const j1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,yc=(i,l)=>u=>!!(typeof u=="string"&&j1.test(u)&&u.startsWith(i)||l&&!C1(u)&&Object.prototype.hasOwnProperty.call(u,l)),v0=(i,l,u)=>o=>{if(typeof o!="string")return o;const[c,d,h,g]=o.match(pc);return{[i]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(h),alpha:g!==void 0?parseFloat(g):1}},V1=i=>fn(0,255,i),Mr={...ii,transform:i=>Math.round(V1(i))},oa={test:yc("rgb","red"),parse:v0("red","green","blue"),transform:({red:i,green:l,blue:u,alpha:o=1})=>"rgba("+Mr.transform(i)+", "+Mr.transform(l)+", "+Mr.transform(u)+", "+ol(ml.transform(o))+")"};function z1(i){let l="",u="",o="",c="";return i.length>5?(l=i.substring(1,3),u=i.substring(3,5),o=i.substring(5,7),c=i.substring(7,9)):(l=i.substring(1,2),u=i.substring(2,3),o=i.substring(3,4),c=i.substring(4,5),l+=l,u+=u,o+=o,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(o,16),alpha:c?parseInt(c,16)/255:1}}const Ur={test:yc("#"),parse:z1,transform:oa.transform},Tl=i=>({test:l=>typeof l=="string"&&l.endsWith(i)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${i}`}),Hn=Tl("deg"),ke=Tl("%"),nt=Tl("px"),_1=Tl("vh"),U1=Tl("vw"),Xm={...ke,parse:i=>ke.parse(i)/100,transform:i=>ke.transform(i*100)},Wa={test:yc("hsl","hue"),parse:v0("hue","saturation","lightness"),transform:({hue:i,saturation:l,lightness:u,alpha:o=1})=>"hsla("+Math.round(i)+", "+ke.transform(ol(l))+", "+ke.transform(ol(u))+", "+ol(ml.transform(o))+")"},Lt={test:i=>oa.test(i)||Ur.test(i)||Wa.test(i),parse:i=>oa.test(i)?oa.parse(i):Wa.test(i)?Wa.parse(i):Ur.parse(i),transform:i=>typeof i=="string"?i:i.hasOwnProperty("red")?oa.transform(i):Wa.transform(i),getAnimatableNone:i=>{const l=Lt.parse(i);return l.alpha=0,Lt.transform(l)}},B1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function H1(i){return isNaN(i)&&typeof i=="string"&&(i.match(pc)?.length||0)+(i.match(B1)?.length||0)>0}const b0="number",x0="color",L1="var",q1="var(",Qm="${}",Y1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function pl(i){const l=i.toString(),u=[],o={color:[],number:[],var:[]},c=[];let d=0;const g=l.replace(Y1,y=>(Lt.test(y)?(o.color.push(d),c.push(x0),u.push(Lt.parse(y))):y.startsWith(q1)?(o.var.push(d),c.push(L1),u.push(y)):(o.number.push(d),c.push(b0),u.push(parseFloat(y))),++d,Qm)).split(Qm);return{values:u,split:g,indexes:o,types:c}}function S0(i){return pl(i).values}function T0(i){const{split:l,types:u}=pl(i),o=l.length;return c=>{let d="";for(let h=0;h<o;h++)if(d+=l[h],c[h]!==void 0){const g=u[h];g===b0?d+=ol(c[h]):g===x0?d+=Lt.transform(c[h]):d+=c[h]}return d}}const G1=i=>typeof i=="number"?0:Lt.test(i)?Lt.getAnimatableNone(i):i;function X1(i){const l=S0(i);return T0(i)(l.map(G1))}const qn={test:H1,parse:S0,createTransformer:T0,getAnimatableNone:X1};function Er(i,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?i+(l-i)*6*u:u<1/2?l:u<2/3?i+(l-i)*(2/3-u)*6:i}function Q1({hue:i,saturation:l,lightness:u,alpha:o}){i/=360,l/=100,u/=100;let c=0,d=0,h=0;if(!l)c=d=h=u;else{const g=u<.5?u*(1+l):u+l-u*l,y=2*u-g;c=Er(y,g,i+1/3),d=Er(y,g,i),h=Er(y,g,i-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(h*255),alpha:o}}function Ks(i,l){return u=>u>0?l:i}const wt=(i,l,u)=>i+(l-i)*u,Dr=(i,l,u)=>{const o=i*i,c=u*(l*l-o)+o;return c<0?0:Math.sqrt(c)},Z1=[Ur,oa,Wa],K1=i=>Z1.find(l=>l.test(i));function Zm(i){const l=K1(i);if(!l)return!1;let u=l.parse(i);return l===Wa&&(u=Q1(u)),u}const Km=(i,l)=>{const u=Zm(i),o=Zm(l);if(!u||!o)return Ks(i,l);const c={...u};return d=>(c.red=Dr(u.red,o.red,d),c.green=Dr(u.green,o.green,d),c.blue=Dr(u.blue,o.blue,d),c.alpha=wt(u.alpha,o.alpha,d),oa.transform(c))},Br=new Set(["none","hidden"]);function k1(i,l){return Br.has(i)?u=>u<=0?i:l:u=>u>=1?l:i}function J1(i,l){return u=>wt(i,l,u)}function gc(i){return typeof i=="number"?J1:typeof i=="string"?mc(i)?Ks:Lt.test(i)?Km:W1:Array.isArray(i)?A0:typeof i=="object"?Lt.test(i)?Km:P1:Ks}function A0(i,l){const u=[...i],o=u.length,c=i.map((d,h)=>gc(d)(d,l[h]));return d=>{for(let h=0;h<o;h++)u[h]=c[h](d);return u}}function P1(i,l){const u={...i,...l},o={};for(const c in u)i[c]!==void 0&&l[c]!==void 0&&(o[c]=gc(i[c])(i[c],l[c]));return c=>{for(const d in o)u[d]=o[d](c);return u}}function F1(i,l){const u=[],o={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],h=i.indexes[d][o[d]],g=i.values[h]??0;u[c]=g,o[d]++}return u}const W1=(i,l)=>{const u=qn.createTransformer(l),o=pl(i),c=pl(l);return o.indexes.var.length===c.indexes.var.length&&o.indexes.color.length===c.indexes.color.length&&o.indexes.number.length>=c.indexes.number.length?Br.has(i)&&!c.values.length||Br.has(l)&&!o.values.length?k1(i,l):xl(A0(F1(o,c),c.values),u):Ks(i,l)};function M0(i,l,u){return typeof i=="number"&&typeof l=="number"&&typeof u=="number"?wt(i,l,u):gc(i)(i,l)}const $1=i=>{const l=({timestamp:u})=>i(u);return{start:(u=!0)=>Rt.update(l,u),stop:()=>Ln(l),now:()=>$t.isProcessing?$t.timestamp:fe.now()}},E0=(i,l,u=10)=>{let o="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)o+=Math.round(i(d/(c-1))*1e4)/1e4+", ";return`linear(${o.substring(0,o.length-2)})`},ks=2e4;function vc(i){let l=0;const u=50;let o=i.next(l);for(;!o.done&&l<ks;)l+=u,o=i.next(l);return l>=ks?1/0:l}function I1(i,l=100,u){const o=u({...i,keyframes:[0,l]}),c=Math.min(vc(o),ks);return{type:"keyframes",ease:d=>o.next(c*d).value/l,duration:Ke(c)}}const tb=5;function D0(i,l,u){const o=Math.max(l-tb,0);return l0(u-i(o),l-o)}const jt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Rr=.001;function eb({duration:i=jt.duration,bounce:l=jt.bounce,velocity:u=jt.velocity,mass:o=jt.mass}){let c,d,h=1-l;h=fn(jt.minDamping,jt.maxDamping,h),i=fn(jt.minDuration,jt.maxDuration,Ke(i)),h<1?(c=m=>{const v=m*h,x=v*i,A=v-u,z=Hr(m,h),B=Math.exp(-x);return Rr-A/z*B},d=m=>{const x=m*h*i,A=x*u+u,z=Math.pow(h,2)*Math.pow(m,2)*i,B=Math.exp(-x),Q=Hr(Math.pow(m,2),h);return(-c(m)+Rr>0?-1:1)*((A-z)*B)/Q}):(c=m=>{const v=Math.exp(-m*i),x=(m-u)*i+1;return-Rr+v*x},d=m=>{const v=Math.exp(-m*i),x=(u-m)*(i*i);return v*x});const g=5/i,y=ab(c,d,g);if(i=Ze(i),isNaN(y))return{stiffness:jt.stiffness,damping:jt.damping,duration:i};{const m=Math.pow(y,2)*o;return{stiffness:m,damping:h*2*Math.sqrt(o*m),duration:i}}}const nb=12;function ab(i,l,u){let o=u;for(let c=1;c<nb;c++)o=o-i(o)/l(o);return o}function Hr(i,l){return i*Math.sqrt(1-l*l)}const ib=["duration","bounce"],lb=["stiffness","damping","mass"];function km(i,l){return l.some(u=>i[u]!==void 0)}function sb(i){let l={velocity:jt.velocity,stiffness:jt.stiffness,damping:jt.damping,mass:jt.mass,isResolvedFromDuration:!1,...i};if(!km(i,lb)&&km(i,ib))if(i.visualDuration){const u=i.visualDuration,o=2*Math.PI/(u*1.2),c=o*o,d=2*fn(.05,1,1-(i.bounce||0))*Math.sqrt(c);l={...l,mass:jt.mass,stiffness:c,damping:d}}else{const u=eb(i);l={...l,...u,mass:jt.mass},l.isResolvedFromDuration=!0}return l}function Js(i=jt.visualDuration,l=jt.bounce){const u=typeof i!="object"?{visualDuration:i,keyframes:[0,1],bounce:l}:i;let{restSpeed:o,restDelta:c}=u;const d=u.keyframes[0],h=u.keyframes[u.keyframes.length-1],g={done:!1,value:d},{stiffness:y,damping:m,mass:v,duration:x,velocity:A,isResolvedFromDuration:z}=sb({...u,velocity:-Ke(u.velocity||0)}),B=A||0,Q=m/(2*Math.sqrt(y*v)),G=h-d,q=Ke(Math.sqrt(y/v)),K=Math.abs(G)<5;o||(o=K?jt.restSpeed.granular:jt.restSpeed.default),c||(c=K?jt.restDelta.granular:jt.restDelta.default);let L;if(Q<1){const H=Hr(q,Q);L=st=>{const ht=Math.exp(-Q*q*st);return h-ht*((B+Q*q*G)/H*Math.sin(H*st)+G*Math.cos(H*st))}}else if(Q===1)L=H=>h-Math.exp(-q*H)*(G+(B+q*G)*H);else{const H=q*Math.sqrt(Q*Q-1);L=st=>{const ht=Math.exp(-Q*q*st),$=Math.min(H*st,300);return h-ht*((B+Q*q*G)*Math.sinh($)+H*G*Math.cosh($))/H}}const at={calculatedDuration:z&&x||null,next:H=>{const st=L(H);if(z)g.done=H>=x;else{let ht=H===0?B:0;Q<1&&(ht=H===0?Ze(B):D0(L,H,st));const $=Math.abs(ht)<=o,Mt=Math.abs(h-st)<=c;g.done=$&&Mt}return g.value=g.done?h:st,g},toString:()=>{const H=Math.min(vc(at),ks),st=E0(ht=>at.next(H*ht).value,H,30);return H+"ms "+st},toTransition:()=>{}};return at}Js.applyToOptions=i=>{const l=I1(i,100,Js);return i.ease=l.ease,i.duration=Ze(l.duration),i.type="keyframes",i};function Lr({keyframes:i,velocity:l=0,power:u=.8,timeConstant:o=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:h,min:g,max:y,restDelta:m=.5,restSpeed:v}){const x=i[0],A={done:!1,value:x},z=$=>g!==void 0&&$<g||y!==void 0&&$>y,B=$=>g===void 0?y:y===void 0||Math.abs(g-$)<Math.abs(y-$)?g:y;let Q=u*l;const G=x+Q,q=h===void 0?G:h(G);q!==G&&(Q=q-x);const K=$=>-Q*Math.exp(-$/o),L=$=>q+K($),at=$=>{const Mt=K($),qt=L($);A.done=Math.abs(Mt)<=m,A.value=A.done?q:qt};let H,st;const ht=$=>{z(A.value)&&(H=$,st=Js({keyframes:[A.value,B(A.value)],velocity:D0(L,$,A.value),damping:c,stiffness:d,restDelta:m,restSpeed:v}))};return ht(0),{calculatedDuration:null,next:$=>{let Mt=!1;return!st&&H===void 0&&(Mt=!0,at($),ht($)),H!==void 0&&$>=H?st.next($-H):(!Mt&&at($),A)}}}function ub(i,l,u){const o=[],c=u||hn.mix||M0,d=i.length-1;for(let h=0;h<d;h++){let g=c(i[h],i[h+1]);if(l){const y=Array.isArray(l)?l[h]||Ve:l;g=xl(y,g)}o.push(g)}return o}function ob(i,l,{clamp:u=!0,ease:o,mixer:c}={}){const d=i.length;if(oc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const h=i[0]===i[1];i[0]>i[d-1]&&(i=[...i].reverse(),l=[...l].reverse());const g=ub(l,o,c),y=g.length,m=v=>{if(h&&v<i[0])return l[0];let x=0;if(y>1)for(;x<i.length-2&&!(v<i[x+1]);x++);const A=dl(i[x],i[x+1],v);return g[x](A)};return u?v=>m(fn(i[0],i[d-1],v)):m}function rb(i,l){const u=i[i.length-1];for(let o=1;o<=l;o++){const c=dl(0,l,o);i.push(wt(u,1,c))}}function cb(i){const l=[0];return rb(l,i.length-1),l}function fb(i,l){return i.map(u=>u*l)}function hb(i,l){return i.map(()=>l||m0).splice(0,i.length-1)}function rl({duration:i=300,keyframes:l,times:u,ease:o="easeInOut"}){const c=A1(o)?o.map(Ym):Ym(o),d={done:!1,value:l[0]},h=fb(u&&u.length===l.length?u:cb(l),i),g=ob(h,l,{ease:Array.isArray(c)?c:hb(l,c)});return{calculatedDuration:i,next:y=>(d.value=g(y),d.done=y>=i,d)}}const db=i=>i!==null;function bc(i,{repeat:l,repeatType:u="loop"},o,c=1){const d=i.filter(db),g=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!g||o===void 0?d[g]:o}const mb={decay:Lr,inertia:Lr,tween:rl,keyframes:rl,spring:Js};function R0(i){typeof i.type=="string"&&(i.type=mb[i.type])}class xc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const pb=i=>i/100;class Sc extends xc{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:u}=this.options;u&&u.updatedAt!==fe.now()&&this.tick(fe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;R0(l);const{type:u=rl,repeat:o=0,repeatDelay:c=0,repeatType:d,velocity:h=0}=l;let{keyframes:g}=l;const y=u||rl;y!==rl&&typeof g[0]!="number"&&(this.mixKeyframes=xl(pb,M0(g[0],g[1])),g=[0,100]);const m=y({...l,keyframes:g});d==="mirror"&&(this.mirroredGenerator=y({...l,keyframes:[...g].reverse(),velocity:-h})),m.calculatedDuration===null&&(m.calculatedDuration=vc(m));const{calculatedDuration:v}=m;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(o+1)-c,this.generator=m}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:o,totalDuration:c,mixKeyframes:d,mirroredGenerator:h,resolvedDuration:g,calculatedDuration:y}=this;if(this.startTime===null)return o.next(0);const{delay:m=0,keyframes:v,repeat:x,repeatType:A,repeatDelay:z,type:B,onUpdate:Q,finalKeyframe:G}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const q=this.currentTime-m*(this.playbackSpeed>=0?1:-1),K=this.playbackSpeed>=0?q<0:q>c;this.currentTime=Math.max(q,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let L=this.currentTime,at=o;if(x){const $=Math.min(this.currentTime,c)/g;let Mt=Math.floor($),qt=$%1;!qt&&$>=1&&(qt=1),qt===1&&Mt--,Mt=Math.min(Mt,x+1),!!(Mt%2)&&(A==="reverse"?(qt=1-qt,z&&(qt-=z/g)):A==="mirror"&&(at=h)),L=fn(0,1,qt)*g}const H=K?{done:!1,value:v[0]}:at.next(L);d&&(H.value=d(H.value));let{done:st}=H;!K&&y!==null&&(st=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const ht=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&st);return ht&&B!==Lr&&(H.value=bc(v,this.options,G,this.speed)),Q&&Q(H.value),ht&&this.finish(),H}then(l,u){return this.finished.then(l,u)}get duration(){return Ke(this.calculatedDuration)}get time(){return Ke(this.currentTime)}set time(l){l=Ze(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(fe.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=Ke(this.currentTime))}play(){if(this.isStopped)return;const{driver:l=$1,startTime:u}=this.options;this.driver||(this.driver=l(c=>this.tick(c))),this.options.onPlay?.();const o=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=o):this.holdTime!==null?this.startTime=o-this.holdTime:this.startTime||(this.startTime=u??o),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(fe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),l.observe(this)}}function yb(i){for(let l=1;l<i.length;l++)i[l]??(i[l]=i[l-1])}const ra=i=>i*180/Math.PI,qr=i=>{const l=ra(Math.atan2(i[1],i[0]));return Yr(l)},gb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:i=>(Math.abs(i[0])+Math.abs(i[3]))/2,rotate:qr,rotateZ:qr,skewX:i=>ra(Math.atan(i[1])),skewY:i=>ra(Math.atan(i[2])),skew:i=>(Math.abs(i[1])+Math.abs(i[2]))/2},Yr=i=>(i=i%360,i<0&&(i+=360),i),Jm=qr,Pm=i=>Math.sqrt(i[0]*i[0]+i[1]*i[1]),Fm=i=>Math.sqrt(i[4]*i[4]+i[5]*i[5]),vb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Pm,scaleY:Fm,scale:i=>(Pm(i)+Fm(i))/2,rotateX:i=>Yr(ra(Math.atan2(i[6],i[5]))),rotateY:i=>Yr(ra(Math.atan2(-i[2],i[0]))),rotateZ:Jm,rotate:Jm,skewX:i=>ra(Math.atan(i[4])),skewY:i=>ra(Math.atan(i[1])),skew:i=>(Math.abs(i[1])+Math.abs(i[4]))/2};function Gr(i){return i.includes("scale")?1:0}function Xr(i,l){if(!i||i==="none")return Gr(l);const u=i.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let o,c;if(u)o=vb,c=u;else{const g=i.match(/^matrix\(([-\d.e\s,]+)\)$/u);o=gb,c=g}if(!c)return Gr(l);const d=o[l],h=c[1].split(",").map(xb);return typeof d=="function"?d(h):h[d]}const bb=(i,l)=>{const{transform:u="none"}=getComputedStyle(i);return Xr(u,l)};function xb(i){return parseFloat(i.trim())}const li=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],si=new Set(li),Wm=i=>i===ii||i===nt,Sb=new Set(["x","y","z"]),Tb=li.filter(i=>!Sb.has(i));function Ab(i){const l=[];return Tb.forEach(u=>{const o=i.getValue(u);o!==void 0&&(l.push([u,o.get()]),o.set(u.startsWith("scale")?1:0))}),l}const ca={width:({x:i},{paddingLeft:l="0",paddingRight:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),height:({y:i},{paddingTop:l="0",paddingBottom:u="0"})=>i.max-i.min-parseFloat(l)-parseFloat(u),top:(i,{top:l})=>parseFloat(l),left:(i,{left:l})=>parseFloat(l),bottom:({y:i},{top:l})=>parseFloat(l)+(i.max-i.min),right:({x:i},{left:l})=>parseFloat(l)+(i.max-i.min),x:(i,{transform:l})=>Xr(l,"x"),y:(i,{transform:l})=>Xr(l,"y")};ca.translateX=ca.x;ca.translateY=ca.y;const fa=new Set;let Qr=!1,Zr=!1,Kr=!1;function N0(){if(Zr){const i=Array.from(fa).filter(o=>o.needsMeasurement),l=new Set(i.map(o=>o.element)),u=new Map;l.forEach(o=>{const c=Ab(o);c.length&&(u.set(o,c),o.render())}),i.forEach(o=>o.measureInitialState()),l.forEach(o=>{o.render();const c=u.get(o);c&&c.forEach(([d,h])=>{o.getValue(d)?.set(h)})}),i.forEach(o=>o.measureEndState()),i.forEach(o=>{o.suspendedScrollY!==void 0&&window.scrollTo(0,o.suspendedScrollY)})}Zr=!1,Qr=!1,fa.forEach(i=>i.complete(Kr)),fa.clear()}function w0(){fa.forEach(i=>{i.readKeyframes(),i.needsMeasurement&&(Zr=!0)})}function Mb(){Kr=!0,w0(),N0(),Kr=!1}class Tc{constructor(l,u,o,c,d,h=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=o,this.motionValue=c,this.element=d,this.isAsync=h}scheduleResolve(){this.state="scheduled",this.isAsync?(fa.add(this),Qr||(Qr=!0,Rt.read(w0),Rt.resolveKeyframes(N0))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:o,motionValue:c}=this;if(l[0]===null){const d=c?.get(),h=l[l.length-1];if(d!==void 0)l[0]=d;else if(o&&u){const g=o.readValue(u,h);g!=null&&(l[0]=g)}l[0]===void 0&&(l[0]=h),c&&d===void 0&&c.set(l[0])}yb(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),fa.delete(this)}cancel(){this.state==="scheduled"&&(fa.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Eb=i=>i.startsWith("--");function Db(i,l,u){Eb(l)?i.style.setProperty(l,u):i.style[l]=u}const Rb=rc(()=>window.ScrollTimeline!==void 0),Nb={};function wb(i,l){const u=rc(i);return()=>Nb[l]??u()}const O0=wb(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ul=([i,l,u,o])=>`cubic-bezier(${i}, ${l}, ${u}, ${o})`,$m={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ul([0,.65,.55,1]),circOut:ul([.55,0,1,.45]),backIn:ul([.31,.01,.66,-.59]),backOut:ul([.33,1.53,.69,.99])};function C0(i,l){if(i)return typeof i=="function"?O0()?E0(i,l):"ease-out":p0(i)?ul(i):Array.isArray(i)?i.map(u=>C0(u,l)||$m.easeOut):$m[i]}function Ob(i,l,u,{delay:o=0,duration:c=300,repeat:d=0,repeatType:h="loop",ease:g="easeOut",times:y}={},m=void 0){const v={[l]:u};y&&(v.offset=y);const x=C0(g,c);Array.isArray(x)&&(v.easing=x);const A={delay:o,duration:c,easing:Array.isArray(x)?"linear":x,fill:"both",iterations:d+1,direction:h==="reverse"?"alternate":"normal"};return m&&(A.pseudoElement=m),i.animate(v,A)}function j0(i){return typeof i=="function"&&"applyToOptions"in i}function Cb({type:i,...l}){return j0(i)&&O0()?i.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class jb extends xc{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:o,keyframes:c,pseudoElement:d,allowFlatten:h=!1,finalKeyframe:g,onComplete:y}=l;this.isPseudoElement=!!d,this.allowFlatten=h,this.options=l,oc(typeof l.type!="string");const m=Cb(l);this.animation=Ob(u,o,c,m,d),m.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=bc(c,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(v):Db(u,o,v),this.animation.cancel()}y?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const l=this.animation.effect?.getComputedTiming?.().duration||0;return Ke(Number(l))}get time(){return Ke(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Ze(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,l&&Rb()?(this.animation.timeline=l,Ve):u(this)}}const V0={anticipate:f0,backInOut:c0,circInOut:d0};function Vb(i){return i in V0}function zb(i){typeof i.ease=="string"&&Vb(i.ease)&&(i.ease=V0[i.ease])}const Im=10;class _b extends jb{constructor(l){zb(l),R0(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:o,onComplete:c,element:d,...h}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const g=new Sc({...h,autoplay:!1}),y=Ze(this.finishedTime??this.time);u.setWithVelocity(g.sample(y-Im).value,g.sample(y).value,Im),g.stop()}}const tp=(i,l)=>l==="zIndex"?!1:!!(typeof i=="number"||Array.isArray(i)||typeof i=="string"&&(qn.test(i)||i==="0")&&!i.startsWith("url("));function Ub(i){const l=i[0];if(i.length===1)return!0;for(let u=0;u<i.length;u++)if(i[u]!==l)return!0}function Bb(i,l,u,o){const c=i[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=i[i.length-1],h=tp(c,l),g=tp(d,l);return!h||!g?!1:Ub(i)||(u==="spring"||j0(u))&&o}function z0(i){return a0(i)&&"offsetHeight"in i}const Hb=new Set(["opacity","clipPath","filter","transform"]),Lb=rc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function qb(i){const{motionValue:l,name:u,repeatDelay:o,repeatType:c,damping:d,type:h}=i;if(!z0(l?.owner?.current))return!1;const{onUpdate:g,transformTemplate:y}=l.owner.getProps();return Lb()&&u&&Hb.has(u)&&(u!=="transform"||!y)&&!g&&!o&&c!=="mirror"&&d!==0&&h!=="inertia"}const Yb=40;class Gb extends xc{constructor({autoplay:l=!0,delay:u=0,type:o="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:h="loop",keyframes:g,name:y,motionValue:m,element:v,...x}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=fe.now();const A={autoplay:l,delay:u,type:o,repeat:c,repeatDelay:d,repeatType:h,name:y,motionValue:m,element:v,...x},z=v?.KeyframeResolver||Tc;this.keyframeResolver=new z(g,(B,Q,G)=>this.onKeyframesResolved(B,Q,A,!G),y,m,v),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(l,u,o,c){this.keyframeResolver=void 0;const{name:d,type:h,velocity:g,delay:y,isHandoff:m,onUpdate:v}=o;this.resolvedAt=fe.now(),Bb(l,d,h,g)||((hn.instantAnimations||!y)&&v?.(bc(l,o,u)),l[0]=l[l.length-1],o.duration=0,o.repeat=0);const A={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>Yb?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...o,keyframes:l},z=!m&&qb(A)?new _b({...A,element:A.motionValue.owner.current}):new Sc(A);z.finished.then(()=>this.notifyFinished()).catch(Ve),this.pendingTimeline&&(this.stopTimeline=z.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=z}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Mb()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Xb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Qb(i){const l=Xb.exec(i);if(!l)return[,];const[,u,o,c]=l;return[`--${u??o}`,c]}function _0(i,l,u=1){const[o,c]=Qb(i);if(!o)return;const d=window.getComputedStyle(l).getPropertyValue(o);if(d){const h=d.trim();return n0(h)?parseFloat(h):h}return mc(c)?_0(c,l,u+1):c}function Ac(i,l){return i?.[l]??i?.default??i}const U0=new Set(["width","height","top","left","right","bottom",...li]),Zb={test:i=>i==="auto",parse:i=>i},B0=i=>l=>l.test(i),H0=[ii,nt,ke,Hn,U1,_1,Zb],ep=i=>H0.find(B0(i));function Kb(i){return typeof i=="number"?i===0:i!==null?i==="none"||i==="0"||i0(i):!0}const kb=new Set(["brightness","contrast","saturate","opacity"]);function Jb(i){const[l,u]=i.slice(0,-1).split("(");if(l==="drop-shadow")return i;const[o]=u.match(pc)||[];if(!o)return i;const c=u.replace(o,"");let d=kb.has(l)?1:0;return o!==u&&(d*=100),l+"("+d+c+")"}const Pb=/\b([a-z-]*)\(.*?\)/gu,kr={...qn,getAnimatableNone:i=>{const l=i.match(Pb);return l?l.map(Jb).join(" "):i}},np={...ii,transform:Math.round},Fb={rotate:Hn,rotateX:Hn,rotateY:Hn,rotateZ:Hn,scale:qs,scaleX:qs,scaleY:qs,scaleZ:qs,skew:Hn,skewX:Hn,skewY:Hn,distance:nt,translateX:nt,translateY:nt,translateZ:nt,x:nt,y:nt,z:nt,perspective:nt,transformPerspective:nt,opacity:ml,originX:Xm,originY:Xm,originZ:nt},Mc={borderWidth:nt,borderTopWidth:nt,borderRightWidth:nt,borderBottomWidth:nt,borderLeftWidth:nt,borderRadius:nt,radius:nt,borderTopLeftRadius:nt,borderTopRightRadius:nt,borderBottomRightRadius:nt,borderBottomLeftRadius:nt,width:nt,maxWidth:nt,height:nt,maxHeight:nt,top:nt,right:nt,bottom:nt,left:nt,padding:nt,paddingTop:nt,paddingRight:nt,paddingBottom:nt,paddingLeft:nt,margin:nt,marginTop:nt,marginRight:nt,marginBottom:nt,marginLeft:nt,backgroundPositionX:nt,backgroundPositionY:nt,...Fb,zIndex:np,fillOpacity:ml,strokeOpacity:ml,numOctaves:np},Wb={...Mc,color:Lt,backgroundColor:Lt,outlineColor:Lt,fill:Lt,stroke:Lt,borderColor:Lt,borderTopColor:Lt,borderRightColor:Lt,borderBottomColor:Lt,borderLeftColor:Lt,filter:kr,WebkitFilter:kr},L0=i=>Wb[i];function q0(i,l){let u=L0(i);return u!==kr&&(u=qn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const $b=new Set(["auto","none","0"]);function Ib(i,l,u){let o=0,c;for(;o<i.length&&!c;){const d=i[o];typeof d=="string"&&!$b.has(d)&&pl(d).values.length&&(c=i[o]),o++}if(c&&u)for(const d of l)i[d]=q0(u,c)}class tx extends Tc{constructor(l,u,o,c,d){super(l,u,o,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:o}=this;if(!u||!u.current)return;super.readKeyframes();for(let y=0;y<l.length;y++){let m=l[y];if(typeof m=="string"&&(m=m.trim(),mc(m))){const v=_0(m,u.current);v!==void 0&&(l[y]=v),y===l.length-1&&(this.finalKeyframe=m)}}if(this.resolveNoneKeyframes(),!U0.has(o)||l.length!==2)return;const[c,d]=l,h=ep(c),g=ep(d);if(h!==g)if(Wm(h)&&Wm(g))for(let y=0;y<l.length;y++){const m=l[y];typeof m=="string"&&(l[y]=parseFloat(m))}else ca[o]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,o=[];for(let c=0;c<l.length;c++)(l[c]===null||Kb(l[c]))&&o.push(c);o.length&&Ib(l,o,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:o}=this;if(!l||!l.current)return;o==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ca[o](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(o,c).jump(c,!1)}measureEndState(){const{element:l,name:u,unresolvedKeyframes:o}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=o.length-1,h=o[d];o[d]=ca[u](l.measureViewportBox(),window.getComputedStyle(l.current)),h!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=h),this.removedTransforms?.length&&this.removedTransforms.forEach(([g,y])=>{l.getValue(g).set(y)}),this.resolveNoneKeyframes()}}function Y0(i,l,u){if(i instanceof EventTarget)return[i];if(typeof i=="string"){const c=document.querySelectorAll(i);return c?Array.from(c):[]}return Array.from(i)}const G0=(i,l)=>l&&typeof i=="number"?l.transform(i):i,ap=30,ex=i=>!isNaN(parseFloat(i));class nx{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(o,c=!0)=>{const d=fe.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(o),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const h of this.dependents)h.dirty();c&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=fe.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=ex(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new cc);const o=this.events[l].add(u);return l==="change"?()=>{o(),Rt.read(()=>{this.events.change.getSize()||this.stop()})}:o}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l,u=!0){!u||!this.passiveEffect?this.updateAndNotify(l,u):this.passiveEffect(l,this.updateAndNotify)}setWithVelocity(l,u,o){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-o}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=fe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>ap)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,ap);return l0(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ni(i,l){return new nx(i,l)}const{schedule:Ec}=y0(queueMicrotask,!1),Be={x:!1,y:!1};function X0(){return Be.x||Be.y}function ax(i){return i==="x"||i==="y"?Be[i]?null:(Be[i]=!0,()=>{Be[i]=!1}):Be.x||Be.y?null:(Be.x=Be.y=!0,()=>{Be.x=Be.y=!1})}function Q0(i,l){const u=Y0(i),o=new AbortController,c={passive:!0,...l,signal:o.signal};return[u,c,()=>o.abort()]}function ip(i){return!(i.pointerType==="touch"||X0())}function ix(i,l,u={}){const[o,c,d]=Q0(i,u),h=g=>{if(!ip(g))return;const{target:y}=g,m=l(y,g);if(typeof m!="function"||!y)return;const v=x=>{ip(x)&&(m(x),y.removeEventListener("pointerleave",v))};y.addEventListener("pointerleave",v,c)};return o.forEach(g=>{g.addEventListener("pointerenter",h,c)}),d}const Z0=(i,l)=>l?i===l?!0:Z0(i,l.parentElement):!1,Dc=i=>i.pointerType==="mouse"?typeof i.button!="number"||i.button<=0:i.isPrimary!==!1,lx=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function sx(i){return lx.has(i.tagName)||i.tabIndex!==-1}const Xs=new WeakSet;function lp(i){return l=>{l.key==="Enter"&&i(l)}}function Nr(i,l){i.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const ux=(i,l)=>{const u=i.currentTarget;if(!u)return;const o=lp(()=>{if(Xs.has(u))return;Nr(u,"down");const c=lp(()=>{Nr(u,"up")}),d=()=>Nr(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",o,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",o),l)};function sp(i){return Dc(i)&&!X0()}function ox(i,l,u={}){const[o,c,d]=Q0(i,u),h=g=>{const y=g.currentTarget;if(!sp(g))return;Xs.add(y);const m=l(y,g),v=(z,B)=>{window.removeEventListener("pointerup",x),window.removeEventListener("pointercancel",A),Xs.has(y)&&Xs.delete(y),sp(z)&&typeof m=="function"&&m(z,{success:B})},x=z=>{v(z,y===window||y===document||u.useGlobalTarget||Z0(y,z.target))},A=z=>{v(z,!1)};window.addEventListener("pointerup",x,c),window.addEventListener("pointercancel",A,c)};return o.forEach(g=>{(u.useGlobalTarget?window:g).addEventListener("pointerdown",h,c),z0(g)&&(g.addEventListener("focus",m=>ux(m,c)),!sx(g)&&!g.hasAttribute("tabindex")&&(g.tabIndex=0))}),d}function K0(i){return a0(i)&&"ownerSVGElement"in i}function rx(i){return K0(i)&&i.tagName==="svg"}const ae=i=>!!(i&&i.getVelocity),cx=[...H0,Lt,qn],fx=i=>cx.find(B0(i)),k0=P.createContext({transformPagePoint:i=>i,isStatic:!1,reducedMotion:"never"});function hx(i=!0){const l=P.useContext(lc);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:o,register:c}=l,d=P.useId();P.useEffect(()=>{if(i)return c(d)},[i]);const h=P.useCallback(()=>i&&o&&o(d),[d,o,i]);return!u&&o?[!1,h]:[!0]}const J0=P.createContext({strict:!1}),up={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ai={};for(const i in up)ai[i]={isEnabled:l=>up[i].some(u=>!!l[u])};function dx(i){for(const l in i)ai[l]={...ai[l],...i[l]}}const mx=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ps(i){return i.startsWith("while")||i.startsWith("drag")&&i!=="draggable"||i.startsWith("layout")||i.startsWith("onTap")||i.startsWith("onPan")||i.startsWith("onLayout")||mx.has(i)}let P0=i=>!Ps(i);function px(i){typeof i=="function"&&(P0=l=>l.startsWith("on")?!Ps(l):i(l))}try{px(require("@emotion/is-prop-valid").default)}catch{}function yx(i,l,u){const o={};for(const c in i)c==="values"&&typeof i.values=="object"||(P0(c)||u===!0&&Ps(c)||!l&&!Ps(c)||i.draggable&&c.startsWith("onDrag"))&&(o[c]=i[c]);return o}function gx(i){if(typeof Proxy>"u")return i;const l=new Map,u=(...o)=>i(...o);return new Proxy(u,{get:(o,c)=>c==="create"?i:(l.has(c)||l.set(c,i(c)),l.get(c))})}const Ws=P.createContext({});function $s(i){return i!==null&&typeof i=="object"&&typeof i.start=="function"}function yl(i){return typeof i=="string"||Array.isArray(i)}const Rc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Nc=["initial",...Rc];function Is(i){return $s(i.animate)||Nc.some(l=>yl(i[l]))}function F0(i){return!!(Is(i)||i.variants)}function vx(i,l){if(Is(i)){const{initial:u,animate:o}=i;return{initial:u===!1||yl(u)?u:void 0,animate:yl(o)?o:void 0}}return i.inherit!==!1?l:{}}function bx(i){const{initial:l,animate:u}=vx(i,P.useContext(Ws));return P.useMemo(()=>({initial:l,animate:u}),[op(l),op(u)])}function op(i){return Array.isArray(i)?i.join(" "):i}const xx=Symbol.for("motionComponentSymbol");function $a(i){return i&&typeof i=="object"&&Object.prototype.hasOwnProperty.call(i,"current")}function Sx(i,l,u){return P.useCallback(o=>{o&&i.onMount&&i.onMount(o),l&&(o?l.mount(o):l.unmount()),u&&(typeof u=="function"?u(o):$a(u)&&(u.current=o))},[l])}const wc=i=>i.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Tx="framerAppearId",W0="data-"+wc(Tx),$0=P.createContext({});function Ax(i,l,u,o,c){const{visualElement:d}=P.useContext(Ws),h=P.useContext(J0),g=P.useContext(lc),y=P.useContext(k0).reducedMotion,m=P.useRef(null);o=o||h.renderer,!m.current&&o&&(m.current=o(i,{visualState:l,parent:d,props:u,presenceContext:g,blockInitialAnimation:g?g.initial===!1:!1,reducedMotionConfig:y}));const v=m.current,x=P.useContext($0);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&Mx(m.current,u,c,x);const A=P.useRef(!1);P.useInsertionEffect(()=>{v&&A.current&&v.update(u,g)});const z=u[W0],B=P.useRef(!!z&&!window.MotionHandoffIsComplete?.(z)&&window.MotionHasOptimisedAnimation?.(z));return y1(()=>{v&&(A.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),Ec.render(v.render),B.current&&v.animationState&&v.animationState.animateChanges())}),P.useEffect(()=>{v&&(!B.current&&v.animationState&&v.animationState.animateChanges(),B.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(z)}),B.current=!1))}),v}function Mx(i,l,u,o){const{layoutId:c,layout:d,drag:h,dragConstraints:g,layoutScroll:y,layoutRoot:m,layoutCrossfade:v}=l;i.projection=new u(i.latestValues,l["data-framer-portal-id"]?void 0:I0(i.parent)),i.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!h||g&&$a(g),visualElement:i,animationType:typeof d=="string"?d:"both",initialPromotionConfig:o,crossfade:v,layoutScroll:y,layoutRoot:m})}function I0(i){if(i)return i.options.allowProjection!==!1?i.projection:I0(i.parent)}function Ex({preloadedFeatures:i,createVisualElement:l,useRender:u,useVisualState:o,Component:c}){i&&dx(i);function d(g,y){let m;const v={...P.useContext(k0),...g,layoutId:Dx(g)},{isStatic:x}=v,A=bx(g),z=o(g,x);if(!x&&ic){Rx();const B=Nx(v);m=B.MeasureLayout,A.visualElement=Ax(c,z,v,l,B.ProjectionNode)}return M.jsxs(Ws.Provider,{value:A,children:[m&&A.visualElement?M.jsx(m,{visualElement:A.visualElement,...v}):null,u(c,g,Sx(z,A.visualElement,y),z,x,A.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const h=P.forwardRef(d);return h[xx]=c,h}function Dx({layoutId:i}){const l=P.useContext(e0).id;return l&&i!==void 0?l+"-"+i:i}function Rx(i,l){P.useContext(J0).strict}function Nx(i){const{drag:l,layout:u}=ai;if(!l&&!u)return{};const o={...l,...u};return{MeasureLayout:l?.isEnabled(i)||u?.isEnabled(i)?o.MeasureLayout:void 0,ProjectionNode:o.ProjectionNode}}const gl={};function wx(i){for(const l in i)gl[l]=i[l],dc(l)&&(gl[l].isCSSVariable=!0)}function ty(i,{layout:l,layoutId:u}){return si.has(i)||i.startsWith("origin")||(l||u!==void 0)&&(!!gl[i]||i==="opacity")}const Ox={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Cx=li.length;function jx(i,l,u){let o="",c=!0;for(let d=0;d<Cx;d++){const h=li[d],g=i[h];if(g===void 0)continue;let y=!0;if(typeof g=="number"?y=g===(h.startsWith("scale")?1:0):y=parseFloat(g)===0,!y||u){const m=G0(g,Mc[h]);if(!y){c=!1;const v=Ox[h]||h;o+=`${v}(${m}) `}u&&(l[h]=m)}}return o=o.trim(),u?o=u(l,c?"":o):c&&(o="none"),o}function Oc(i,l,u){const{style:o,vars:c,transformOrigin:d}=i;let h=!1,g=!1;for(const y in l){const m=l[y];if(si.has(y)){h=!0;continue}else if(dc(y)){c[y]=m;continue}else{const v=G0(m,Mc[y]);y.startsWith("origin")?(g=!0,d[y]=v):o[y]=v}}if(l.transform||(h||u?o.transform=jx(l,i.transform,u):o.transform&&(o.transform="none")),g){const{originX:y="50%",originY:m="50%",originZ:v=0}=d;o.transformOrigin=`${y} ${m} ${v}`}}const Cc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ey(i,l,u){for(const o in l)!ae(l[o])&&!ty(o,u)&&(i[o]=l[o])}function Vx({transformTemplate:i},l){return P.useMemo(()=>{const u=Cc();return Oc(u,l,i),Object.assign({},u.vars,u.style)},[l])}function zx(i,l){const u=i.style||{},o={};return ey(o,u,i),Object.assign(o,Vx(i,l)),o}function _x(i,l){const u={},o=zx(i,l);return i.drag&&i.dragListener!==!1&&(u.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=i.drag===!0?"none":`pan-${i.drag==="x"?"y":"x"}`),i.tabIndex===void 0&&(i.onTap||i.onTapStart||i.whileTap)&&(u.tabIndex=0),u.style=o,u}const Ux={offset:"stroke-dashoffset",array:"stroke-dasharray"},Bx={offset:"strokeDashoffset",array:"strokeDasharray"};function Hx(i,l,u=1,o=0,c=!0){i.pathLength=1;const d=c?Ux:Bx;i[d.offset]=nt.transform(-o);const h=nt.transform(l),g=nt.transform(u);i[d.array]=`${h} ${g}`}function ny(i,{attrX:l,attrY:u,attrScale:o,pathLength:c,pathSpacing:d=1,pathOffset:h=0,...g},y,m,v){if(Oc(i,g,m),y){i.style.viewBox&&(i.attrs.viewBox=i.style.viewBox);return}i.attrs=i.style,i.style={};const{attrs:x,style:A}=i;x.transform&&(A.transform=x.transform,delete x.transform),(A.transform||x.transformOrigin)&&(A.transformOrigin=x.transformOrigin??"50% 50%",delete x.transformOrigin),A.transform&&(A.transformBox=v?.transformBox??"fill-box",delete x.transformBox),l!==void 0&&(x.x=l),u!==void 0&&(x.y=u),o!==void 0&&(x.scale=o),c!==void 0&&Hx(x,c,d,h,!1)}const ay=()=>({...Cc(),attrs:{}}),iy=i=>typeof i=="string"&&i.toLowerCase()==="svg";function Lx(i,l,u,o){const c=P.useMemo(()=>{const d=ay();return ny(d,l,iy(o),i.transformTemplate,i.style),{...d.attrs,style:{...d.style}}},[l]);if(i.style){const d={};ey(d,i.style,i),c.style={...d,...c.style}}return c}const qx=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function jc(i){return typeof i!="string"||i.includes("-")?!1:!!(qx.indexOf(i)>-1||/[A-Z]/u.test(i))}function Yx(i=!1){return(u,o,c,{latestValues:d},h)=>{const y=(jc(u)?Lx:_x)(o,d,h,u),m=yx(o,typeof u=="string",i),v=u!==P.Fragment?{...m,...y,ref:c}:{},{children:x}=o,A=P.useMemo(()=>ae(x)?x.get():x,[x]);return P.createElement(u,{...v,children:A})}}function rp(i){const l=[{},{}];return i?.values.forEach((u,o)=>{l[0][o]=u.get(),l[1][o]=u.getVelocity()}),l}function Vc(i,l,u,o){if(typeof l=="function"){const[c,d]=rp(o);l=l(u!==void 0?u:i.custom,c,d)}if(typeof l=="string"&&(l=i.variants&&i.variants[l]),typeof l=="function"){const[c,d]=rp(o);l=l(u!==void 0?u:i.custom,c,d)}return l}function Qs(i){return ae(i)?i.get():i}function Gx({scrapeMotionValuesFromProps:i,createRenderState:l},u,o,c){return{latestValues:Xx(u,o,c,i),renderState:l()}}const ly=i=>(l,u)=>{const o=P.useContext(Ws),c=P.useContext(lc),d=()=>Gx(i,l,o,c);return u?d():p1(d)};function Xx(i,l,u,o){const c={},d=o(i,{});for(const A in d)c[A]=Qs(d[A]);let{initial:h,animate:g}=i;const y=Is(i),m=F0(i);l&&m&&!y&&i.inherit!==!1&&(h===void 0&&(h=l.initial),g===void 0&&(g=l.animate));let v=u?u.initial===!1:!1;v=v||h===!1;const x=v?g:h;if(x&&typeof x!="boolean"&&!$s(x)){const A=Array.isArray(x)?x:[x];for(let z=0;z<A.length;z++){const B=Vc(i,A[z]);if(B){const{transitionEnd:Q,transition:G,...q}=B;for(const K in q){let L=q[K];if(Array.isArray(L)){const at=v?L.length-1:0;L=L[at]}L!==null&&(c[K]=L)}for(const K in Q)c[K]=Q[K]}}}return c}function zc(i,l,u){const{style:o}=i,c={};for(const d in o)(ae(o[d])||l.style&&ae(l.style[d])||ty(d,i)||u?.getValue(d)?.liveStyle!==void 0)&&(c[d]=o[d]);return c}const Qx={useVisualState:ly({scrapeMotionValuesFromProps:zc,createRenderState:Cc})};function sy(i,l,u){const o=zc(i,l,u);for(const c in i)if(ae(i[c])||ae(l[c])){const d=li.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;o[d]=i[c]}return o}const Zx={useVisualState:ly({scrapeMotionValuesFromProps:sy,createRenderState:ay})};function Kx(i,l){return function(o,{forwardMotionProps:c}={forwardMotionProps:!1}){const h={...jc(o)?Zx:Qx,preloadedFeatures:i,useRender:Yx(c),createVisualElement:l,Component:o};return Ex(h)}}function vl(i,l,u){const o=i.getProps();return Vc(o,l,u!==void 0?u:o.custom,i)}const Jr=i=>Array.isArray(i);function kx(i,l,u){i.hasValue(l)?i.getValue(l).set(u):i.addValue(l,ni(u))}function Jx(i){return Jr(i)?i[i.length-1]||0:i}function Px(i,l){const u=vl(i,l);let{transitionEnd:o={},transition:c={},...d}=u||{};d={...d,...o};for(const h in d){const g=Jx(d[h]);kx(i,h,g)}}function Fx(i){return!!(ae(i)&&i.add)}function Pr(i,l){const u=i.getValue("willChange");if(Fx(u))return u.add(l);if(!u&&hn.WillChange){const o=new hn.WillChange("auto");i.addValue("willChange",o),o.add(l)}}function uy(i){return i.props[W0]}const Wx=i=>i!==null;function $x(i,{repeat:l,repeatType:u="loop"},o){const c=i.filter(Wx),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const Ix={type:"spring",stiffness:500,damping:25,restSpeed:10},tS=i=>({type:"spring",stiffness:550,damping:i===0?2*Math.sqrt(550):30,restSpeed:10}),eS={type:"keyframes",duration:.8},nS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},aS=(i,{keyframes:l})=>l.length>2?eS:si.has(i)?i.startsWith("scale")?tS(l[1]):Ix:nS;function iS({when:i,delay:l,delayChildren:u,staggerChildren:o,staggerDirection:c,repeat:d,repeatType:h,repeatDelay:g,from:y,elapsed:m,...v}){return!!Object.keys(v).length}const _c=(i,l,u,o={},c,d)=>h=>{const g=Ac(o,i)||{},y=g.delay||o.delay||0;let{elapsed:m=0}=o;m=m-Ze(y);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...g,delay:-m,onUpdate:A=>{l.set(A),g.onUpdate&&g.onUpdate(A)},onComplete:()=>{h(),g.onComplete&&g.onComplete()},name:i,motionValue:l,element:d?void 0:c};iS(g)||Object.assign(v,aS(i,v)),v.duration&&(v.duration=Ze(v.duration)),v.repeatDelay&&(v.repeatDelay=Ze(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let x=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(x=!0)),(hn.instantAnimations||hn.skipAnimations)&&(x=!0,v.duration=0,v.delay=0),v.allowFlatten=!g.type&&!g.ease,x&&!d&&l.get()!==void 0){const A=$x(v.keyframes,g);if(A!==void 0){Rt.update(()=>{v.onUpdate(A),v.onComplete()});return}}return g.isSync?new Sc(v):new Gb(v)};function lS({protectedKeys:i,needsAnimating:l},u){const o=i.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,o}function oy(i,l,{delay:u=0,transitionOverride:o,type:c}={}){let{transition:d=i.getDefaultTransition(),transitionEnd:h,...g}=l;o&&(d=o);const y=[],m=c&&i.animationState&&i.animationState.getState()[c];for(const v in g){const x=i.getValue(v,i.latestValues[v]??null),A=g[v];if(A===void 0||m&&lS(m,v))continue;const z={delay:u,...Ac(d||{},v)},B=x.get();if(B!==void 0&&!x.isAnimating&&!Array.isArray(A)&&A===B&&!z.velocity)continue;let Q=!1;if(window.MotionHandoffAnimation){const q=uy(i);if(q){const K=window.MotionHandoffAnimation(q,v,Rt);K!==null&&(z.startTime=K,Q=!0)}}Pr(i,v),x.start(_c(v,x,A,i.shouldReduceMotion&&U0.has(v)?{type:!1}:z,i,Q));const G=x.animation;G&&y.push(G)}return h&&Promise.all(y).then(()=>{Rt.update(()=>{h&&Px(i,h)})}),y}function Fr(i,l,u={}){const o=vl(i,l,u.type==="exit"?i.presenceContext?.custom:void 0);let{transition:c=i.getDefaultTransition()||{}}=o||{};u.transitionOverride&&(c=u.transitionOverride);const d=o?()=>Promise.all(oy(i,o,u)):()=>Promise.resolve(),h=i.variantChildren&&i.variantChildren.size?(y=0)=>{const{delayChildren:m=0,staggerChildren:v,staggerDirection:x}=c;return sS(i,l,y,m,v,x,u)}:()=>Promise.resolve(),{when:g}=c;if(g){const[y,m]=g==="beforeChildren"?[d,h]:[h,d];return y().then(()=>m())}else return Promise.all([d(),h(u.delay)])}function sS(i,l,u=0,o=0,c=0,d=1,h){const g=[],y=i.variantChildren.size,m=(y-1)*c,v=typeof o=="function",x=v?A=>o(A,y):d===1?(A=0)=>A*c:(A=0)=>m-A*c;return Array.from(i.variantChildren).sort(uS).forEach((A,z)=>{A.notify("AnimationStart",l),g.push(Fr(A,l,{...h,delay:u+(v?0:o)+x(z)}).then(()=>A.notify("AnimationComplete",l)))}),Promise.all(g)}function uS(i,l){return i.sortNodePosition(l)}function oS(i,l,u={}){i.notify("AnimationStart",l);let o;if(Array.isArray(l)){const c=l.map(d=>Fr(i,d,u));o=Promise.all(c)}else if(typeof l=="string")o=Fr(i,l,u);else{const c=typeof l=="function"?vl(i,l,u.custom):l;o=Promise.all(oy(i,c,u))}return o.then(()=>{i.notify("AnimationComplete",l)})}function ry(i,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==i.length)return!1;for(let o=0;o<u;o++)if(l[o]!==i[o])return!1;return!0}const rS=Nc.length;function cy(i){if(!i)return;if(!i.isControllingVariants){const u=i.parent?cy(i.parent)||{}:{};return i.props.initial!==void 0&&(u.initial=i.props.initial),u}const l={};for(let u=0;u<rS;u++){const o=Nc[u],c=i.props[o];(yl(c)||c===!1)&&(l[o]=c)}return l}const cS=[...Rc].reverse(),fS=Rc.length;function hS(i){return l=>Promise.all(l.map(({animation:u,options:o})=>oS(i,u,o)))}function dS(i){let l=hS(i),u=cp(),o=!0;const c=y=>(m,v)=>{const x=vl(i,v,y==="exit"?i.presenceContext?.custom:void 0);if(x){const{transition:A,transitionEnd:z,...B}=x;m={...m,...B,...z}}return m};function d(y){l=y(i)}function h(y){const{props:m}=i,v=cy(i.parent)||{},x=[],A=new Set;let z={},B=1/0;for(let G=0;G<fS;G++){const q=cS[G],K=u[q],L=m[q]!==void 0?m[q]:v[q],at=yl(L),H=q===y?K.isActive:null;H===!1&&(B=G);let st=L===v[q]&&L!==m[q]&&at;if(st&&o&&i.manuallyAnimateOnMount&&(st=!1),K.protectedKeys={...z},!K.isActive&&H===null||!L&&!K.prevProp||$s(L)||typeof L=="boolean")continue;const ht=mS(K.prevProp,L);let $=ht||q===y&&K.isActive&&!st&&at||G>B&&at,Mt=!1;const qt=Array.isArray(L)?L:[L];let It=qt.reduce(c(q),{});H===!1&&(It={});const{prevResolvedValues:Yt={}}=K,Je={...Yt,...It},He=U=>{$=!0,A.has(U)&&(Mt=!0,A.delete(U)),K.needsAnimating[U]=!0;const k=i.getValue(U);k&&(k.liveStyle=!1)};for(const U in Je){const k=It[U],pt=Yt[U];if(z.hasOwnProperty(U))continue;let S=!1;Jr(k)&&Jr(pt)?S=!ry(k,pt):S=k!==pt,S?k!=null?He(U):A.add(U):k!==void 0&&A.has(U)?He(U):K.protectedKeys[U]=!0}K.prevProp=L,K.prevResolvedValues=It,K.isActive&&(z={...z,...It}),o&&i.blockInitialAnimation&&($=!1),$&&(!(st&&ht)||Mt)&&x.push(...qt.map(U=>({animation:U,options:{type:q}})))}if(A.size){const G={};if(typeof m.initial!="boolean"){const q=vl(i,Array.isArray(m.initial)?m.initial[0]:m.initial);q&&q.transition&&(G.transition=q.transition)}A.forEach(q=>{const K=i.getBaseTarget(q),L=i.getValue(q);L&&(L.liveStyle=!0),G[q]=K??null}),x.push({animation:G})}let Q=!!x.length;return o&&(m.initial===!1||m.initial===m.animate)&&!i.manuallyAnimateOnMount&&(Q=!1),o=!1,Q?l(x):Promise.resolve()}function g(y,m){if(u[y].isActive===m)return Promise.resolve();i.variantChildren?.forEach(x=>x.animationState?.setActive(y,m)),u[y].isActive=m;const v=h(y);for(const x in u)u[x].protectedKeys={};return v}return{animateChanges:h,setActive:g,setAnimateFunction:d,getState:()=>u,reset:()=>{u=cp(),o=!0}}}function mS(i,l){return typeof l=="string"?l!==i:Array.isArray(l)?!ry(l,i):!1}function sa(i=!1){return{isActive:i,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function cp(){return{animate:sa(!0),whileInView:sa(),whileHover:sa(),whileTap:sa(),whileDrag:sa(),whileFocus:sa(),exit:sa()}}class Yn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class pS extends Yn{constructor(l){super(l),l.animationState||(l.animationState=dS(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();$s(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let yS=0;class gS extends Yn{constructor(){super(...arguments),this.id=yS++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:o}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===o)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const vS={animation:{Feature:pS},exit:{Feature:gS}};function bl(i,l,u,o={passive:!0}){return i.addEventListener(l,u,o),()=>i.removeEventListener(l,u)}function Al(i){return{point:{x:i.pageX,y:i.pageY}}}const bS=i=>l=>Dc(l)&&i(l,Al(l));function cl(i,l,u,o){return bl(i,l,bS(u),o)}function fy({top:i,left:l,right:u,bottom:o}){return{x:{min:l,max:u},y:{min:i,max:o}}}function xS({x:i,y:l}){return{top:l.min,right:i.max,bottom:l.max,left:i.min}}function SS(i,l){if(!l)return i;const u=l({x:i.left,y:i.top}),o=l({x:i.right,y:i.bottom});return{top:u.y,left:u.x,bottom:o.y,right:o.x}}const hy=1e-4,TS=1-hy,AS=1+hy,dy=.01,MS=0-dy,ES=0+dy;function le(i){return i.max-i.min}function DS(i,l,u){return Math.abs(i-l)<=u}function fp(i,l,u,o=.5){i.origin=o,i.originPoint=wt(l.min,l.max,i.origin),i.scale=le(u)/le(l),i.translate=wt(u.min,u.max,i.origin)-i.originPoint,(i.scale>=TS&&i.scale<=AS||isNaN(i.scale))&&(i.scale=1),(i.translate>=MS&&i.translate<=ES||isNaN(i.translate))&&(i.translate=0)}function fl(i,l,u,o){fp(i.x,l.x,u.x,o?o.originX:void 0),fp(i.y,l.y,u.y,o?o.originY:void 0)}function hp(i,l,u){i.min=u.min+l.min,i.max=i.min+le(l)}function RS(i,l,u){hp(i.x,l.x,u.x),hp(i.y,l.y,u.y)}function dp(i,l,u){i.min=l.min-u.min,i.max=i.min+le(l)}function hl(i,l,u){dp(i.x,l.x,u.x),dp(i.y,l.y,u.y)}const mp=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ia=()=>({x:mp(),y:mp()}),pp=()=>({min:0,max:0}),_t=()=>({x:pp(),y:pp()});function je(i){return[i("x"),i("y")]}function wr(i){return i===void 0||i===1}function Wr({scale:i,scaleX:l,scaleY:u}){return!wr(i)||!wr(l)||!wr(u)}function ua(i){return Wr(i)||my(i)||i.z||i.rotate||i.rotateX||i.rotateY||i.skewX||i.skewY}function my(i){return yp(i.x)||yp(i.y)}function yp(i){return i&&i!=="0%"}function Fs(i,l,u){const o=i-u,c=l*o;return u+c}function gp(i,l,u,o,c){return c!==void 0&&(i=Fs(i,c,o)),Fs(i,u,o)+l}function $r(i,l=0,u=1,o,c){i.min=gp(i.min,l,u,o,c),i.max=gp(i.max,l,u,o,c)}function py(i,{x:l,y:u}){$r(i.x,l.translate,l.scale,l.originPoint),$r(i.y,u.translate,u.scale,u.originPoint)}const vp=.999999999999,bp=1.0000000000001;function NS(i,l,u,o=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,h;for(let g=0;g<c;g++){d=u[g],h=d.projectionDelta;const{visualElement:y}=d.options;y&&y.props.style&&y.props.style.display==="contents"||(o&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ei(i,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),h&&(l.x*=h.x.scale,l.y*=h.y.scale,py(i,h)),o&&ua(d.latestValues)&&ei(i,d.latestValues))}l.x<bp&&l.x>vp&&(l.x=1),l.y<bp&&l.y>vp&&(l.y=1)}function ti(i,l){i.min=i.min+l,i.max=i.max+l}function xp(i,l,u,o,c=.5){const d=wt(i.min,i.max,c);$r(i,l,u,d,o)}function ei(i,l){xp(i.x,l.x,l.scaleX,l.scale,l.originX),xp(i.y,l.y,l.scaleY,l.scale,l.originY)}function yy(i,l){return fy(SS(i.getBoundingClientRect(),l))}function wS(i,l,u){const o=yy(i,u),{scroll:c}=l;return c&&(ti(o.x,c.offset.x),ti(o.y,c.offset.y)),o}const gy=({current:i})=>i?i.ownerDocument.defaultView:null,Sp=(i,l)=>Math.abs(i-l);function OS(i,l){const u=Sp(i.x,l.x),o=Sp(i.y,l.y);return Math.sqrt(u**2+o**2)}class vy{constructor(l,u,{transformPagePoint:o,contextWindow:c=window,dragSnapToOrigin:d=!1,distanceThreshold:h=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const A=Cr(this.lastMoveEventInfo,this.history),z=this.startEvent!==null,B=OS(A.offset,{x:0,y:0})>=this.distanceThreshold;if(!z&&!B)return;const{point:Q}=A,{timestamp:G}=$t;this.history.push({...Q,timestamp:G});const{onStart:q,onMove:K}=this.handlers;z||(q&&q(this.lastMoveEvent,A),this.startEvent=this.lastMoveEvent),K&&K(this.lastMoveEvent,A)},this.handlePointerMove=(A,z)=>{this.lastMoveEvent=A,this.lastMoveEventInfo=Or(z,this.transformPagePoint),Rt.update(this.updatePoint,!0)},this.handlePointerUp=(A,z)=>{this.end();const{onEnd:B,onSessionEnd:Q,resumeAnimation:G}=this.handlers;if(this.dragSnapToOrigin&&G&&G(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const q=Cr(A.type==="pointercancel"?this.lastMoveEventInfo:Or(z,this.transformPagePoint),this.history);this.startEvent&&B&&B(A,q),Q&&Q(A,q)},!Dc(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=o,this.distanceThreshold=h,this.contextWindow=c||window;const g=Al(l),y=Or(g,this.transformPagePoint),{point:m}=y,{timestamp:v}=$t;this.history=[{...m,timestamp:v}];const{onSessionStart:x}=u;x&&x(l,Cr(y,this.history)),this.removeListeners=xl(cl(this.contextWindow,"pointermove",this.handlePointerMove),cl(this.contextWindow,"pointerup",this.handlePointerUp),cl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),Ln(this.updatePoint)}}function Or(i,l){return l?{point:l(i.point)}:i}function Tp(i,l){return{x:i.x-l.x,y:i.y-l.y}}function Cr({point:i},l){return{point:i,delta:Tp(i,by(l)),offset:Tp(i,CS(l)),velocity:jS(l,.1)}}function CS(i){return i[0]}function by(i){return i[i.length-1]}function jS(i,l){if(i.length<2)return{x:0,y:0};let u=i.length-1,o=null;const c=by(i);for(;u>=0&&(o=i[u],!(c.timestamp-o.timestamp>Ze(l)));)u--;if(!o)return{x:0,y:0};const d=Ke(c.timestamp-o.timestamp);if(d===0)return{x:0,y:0};const h={x:(c.x-o.x)/d,y:(c.y-o.y)/d};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}function VS(i,{min:l,max:u},o){return l!==void 0&&i<l?i=o?wt(l,i,o.min):Math.max(i,l):u!==void 0&&i>u&&(i=o?wt(u,i,o.max):Math.min(i,u)),i}function Ap(i,l,u){return{min:l!==void 0?i.min+l:void 0,max:u!==void 0?i.max+u-(i.max-i.min):void 0}}function zS(i,{top:l,left:u,bottom:o,right:c}){return{x:Ap(i.x,u,c),y:Ap(i.y,l,o)}}function Mp(i,l){let u=l.min-i.min,o=l.max-i.max;return l.max-l.min<i.max-i.min&&([u,o]=[o,u]),{min:u,max:o}}function _S(i,l){return{x:Mp(i.x,l.x),y:Mp(i.y,l.y)}}function US(i,l){let u=.5;const o=le(i),c=le(l);return c>o?u=dl(l.min,l.max-o,i.min):o>c&&(u=dl(i.min,i.max-c,l.min)),fn(0,1,u)}function BS(i,l){const u={};return l.min!==void 0&&(u.min=l.min-i.min),l.max!==void 0&&(u.max=l.max-i.min),u}const Ir=.35;function HS(i=Ir){return i===!1?i=0:i===!0&&(i=Ir),{x:Ep(i,"left","right"),y:Ep(i,"top","bottom")}}function Ep(i,l,u){return{min:Dp(i,l),max:Dp(i,u)}}function Dp(i,l){return typeof i=="number"?i:i[l]||0}const LS=new WeakMap;class qS{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=_t(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=l}start(l,{snapToCursor:u=!1,distanceThreshold:o}={}){const{presenceContext:c}=this.visualElement;if(c&&c.isPresent===!1)return;const d=x=>{const{dragSnapToOrigin:A}=this.getProps();A?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(Al(x).point)},h=(x,A)=>{const{drag:z,dragPropagation:B,onDragStart:Q}=this.getProps();if(z&&!B&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ax(z),!this.openDragLock))return;this.latestPointerEvent=x,this.latestPanInfo=A,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),je(q=>{let K=this.getAxisMotionValue(q).get()||0;if(ke.test(K)){const{projection:L}=this.visualElement;if(L&&L.layout){const at=L.layout.layoutBox[q];at&&(K=le(at)*(parseFloat(K)/100))}}this.originPoint[q]=K}),Q&&Rt.postRender(()=>Q(x,A)),Pr(this.visualElement,"transform");const{animationState:G}=this.visualElement;G&&G.setActive("whileDrag",!0)},g=(x,A)=>{this.latestPointerEvent=x,this.latestPanInfo=A;const{dragPropagation:z,dragDirectionLock:B,onDirectionLock:Q,onDrag:G}=this.getProps();if(!z&&!this.openDragLock)return;const{offset:q}=A;if(B&&this.currentDirection===null){this.currentDirection=YS(q),this.currentDirection!==null&&Q&&Q(this.currentDirection);return}this.updateAxis("x",A.point,q),this.updateAxis("y",A.point,q),this.visualElement.render(),G&&G(x,A)},y=(x,A)=>{this.latestPointerEvent=x,this.latestPanInfo=A,this.stop(x,A),this.latestPointerEvent=null,this.latestPanInfo=null},m=()=>je(x=>this.getAnimationState(x)==="paused"&&this.getAxisMotionValue(x).animation?.play()),{dragSnapToOrigin:v}=this.getProps();this.panSession=new vy(l,{onSessionStart:d,onStart:h,onMove:g,onSessionEnd:y,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:v,distanceThreshold:o,contextWindow:gy(this.visualElement)})}stop(l,u){const o=l||this.latestPointerEvent,c=u||this.latestPanInfo,d=this.isDragging;if(this.cancel(),!d||!c||!o)return;const{velocity:h}=c;this.startAnimation(h);const{onDragEnd:g}=this.getProps();g&&Rt.postRender(()=>g(o,c))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:o}=this.getProps();!o&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,o){const{drag:c}=this.getProps();if(!o||!Ys(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let h=this.originPoint[l]+o[l];this.constraints&&this.constraints[l]&&(h=VS(h,this.constraints[l],this.elastic[l])),d.set(h)}resolveConstraints(){const{dragConstraints:l,dragElastic:u}=this.getProps(),o=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,c=this.constraints;l&&$a(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&o?this.constraints=zS(o.layoutBox,l):this.constraints=!1,this.elastic=HS(u),c!==this.constraints&&o&&this.constraints&&!this.hasMutatedConstraints&&je(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=BS(o.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!$a(l))return!1;const o=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=wS(o,c.root,this.visualElement.getTransformPagePoint());let h=_S(c.layout.layoutBox,d);if(u){const g=u(xS(h));this.hasMutatedConstraints=!!g,g&&(h=fy(g))}return h}startAnimation(l){const{drag:u,dragMomentum:o,dragElastic:c,dragTransition:d,dragSnapToOrigin:h,onDragTransitionEnd:g}=this.getProps(),y=this.constraints||{},m=je(v=>{if(!Ys(v,u,this.currentDirection))return;let x=y&&y[v]||{};h&&(x={min:0,max:0});const A=c?200:1e6,z=c?40:1e7,B={type:"inertia",velocity:o?l[v]:0,bounceStiffness:A,bounceDamping:z,timeConstant:750,restDelta:1,restSpeed:10,...d,...x};return this.startAxisValueAnimation(v,B)});return Promise.all(m).then(g)}startAxisValueAnimation(l,u){const o=this.getAxisMotionValue(l);return Pr(this.visualElement,l),o.start(_c(l,o,0,u,this.visualElement,!1))}stopAnimation(){je(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){je(l=>this.getAxisMotionValue(l).animation?.pause())}getAnimationState(l){return this.getAxisMotionValue(l).animation?.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,o=this.visualElement.getProps(),c=o[u];return c||this.visualElement.getValue(l,(o.initial?o.initial[l]:void 0)||0)}snapToCursor(l){je(u=>{const{drag:o}=this.getProps();if(!Ys(u,o,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:h,max:g}=c.layout.layoutBox[u];d.set(l[u]-wt(h,g,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:o}=this.visualElement;if(!$a(u)||!o||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};je(h=>{const g=this.getAxisMotionValue(h);if(g&&this.constraints!==!1){const y=g.get();c[h]=US({min:y,max:y},this.constraints[h])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",o.root&&o.root.updateScroll(),o.updateLayout(),this.resolveConstraints(),je(h=>{if(!Ys(h,l,null))return;const g=this.getAxisMotionValue(h),{min:y,max:m}=this.constraints[h];g.set(wt(y,m,c[h]))})}addListeners(){if(!this.visualElement.current)return;LS.set(this.visualElement,this);const l=this.visualElement.current,u=cl(l,"pointerdown",y=>{const{drag:m,dragListener:v=!0}=this.getProps();m&&v&&this.start(y)}),o=()=>{const{dragConstraints:y}=this.getProps();$a(y)&&y.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",o);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Rt.read(o);const h=bl(window,"resize",()=>this.scalePositionWithinConstraints()),g=c.addEventListener("didUpdate",({delta:y,hasLayoutChanged:m})=>{this.isDragging&&m&&(je(v=>{const x=this.getAxisMotionValue(v);x&&(this.originPoint[v]+=y[v].translate,x.set(x.get()+y[v].translate))}),this.visualElement.render())});return()=>{h(),u(),d(),g&&g()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:o=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:h=Ir,dragMomentum:g=!0}=l;return{...l,drag:u,dragDirectionLock:o,dragPropagation:c,dragConstraints:d,dragElastic:h,dragMomentum:g}}}function Ys(i,l,u){return(l===!0||l===i)&&(u===null||u===i)}function YS(i,l=10){let u=null;return Math.abs(i.y)>l?u="y":Math.abs(i.x)>l&&(u="x"),u}class GS extends Yn{constructor(l){super(l),this.removeGroupControls=Ve,this.removeListeners=Ve,this.controls=new qS(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ve}unmount(){this.removeGroupControls(),this.removeListeners()}}const Rp=i=>(l,u)=>{i&&Rt.postRender(()=>i(l,u))};class XS extends Yn{constructor(){super(...arguments),this.removePointerDownListener=Ve}onPointerDown(l){this.session=new vy(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:gy(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:o,onPanEnd:c}=this.node.getProps();return{onSessionStart:Rp(l),onStart:Rp(u),onMove:o,onEnd:(d,h)=>{delete this.session,c&&Rt.postRender(()=>c(d,h))}}}mount(){this.removePointerDownListener=cl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Zs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Np(i,l){return l.max===l.min?0:i/(l.max-l.min)*100}const sl={correct:(i,l)=>{if(!l.target)return i;if(typeof i=="string")if(nt.test(i))i=parseFloat(i);else return i;const u=Np(i,l.target.x),o=Np(i,l.target.y);return`${u}% ${o}%`}},QS={correct:(i,{treeScale:l,projectionDelta:u})=>{const o=i,c=qn.parse(i);if(c.length>5)return o;const d=qn.createTransformer(i),h=typeof c[0]!="number"?1:0,g=u.x.scale*l.x,y=u.y.scale*l.y;c[0+h]/=g,c[1+h]/=y;const m=wt(g,y,.5);return typeof c[2+h]=="number"&&(c[2+h]/=m),typeof c[3+h]=="number"&&(c[3+h]/=m),d(c)}};let wp=!1;class ZS extends P.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o,layoutId:c}=this.props,{projection:d}=l;wx(KS),d&&(u.group&&u.group.add(d),o&&o.register&&c&&o.register(d),wp&&d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Zs.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:o,drag:c,isPresent:d}=this.props,{projection:h}=o;return h&&(h.isPresent=d,wp=!0,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?h.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?h.promote():h.relegate()||Rt.postRender(()=>{const g=h.getStack();(!g||!g.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Ec.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:o}=this.props,{projection:c}=l;c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),o&&o.deregister&&o.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function xy(i){const[l,u]=hx(),o=P.useContext(e0);return M.jsx(ZS,{...i,layoutGroup:o,switchLayoutGroup:P.useContext($0),isPresent:l,safeToRemove:u})}const KS={borderRadius:{...sl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sl,borderTopRightRadius:sl,borderBottomLeftRadius:sl,borderBottomRightRadius:sl,boxShadow:QS};function kS(i,l,u){const o=ae(i)?i:ni(i);return o.start(_c("",o,l,u)),o.animation}const JS=(i,l)=>i.depth-l.depth;class PS{constructor(){this.children=[],this.isDirty=!1}add(l){sc(this.children,l),this.isDirty=!0}remove(l){uc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort(JS),this.isDirty=!1,this.children.forEach(l)}}function FS(i,l){const u=fe.now(),o=({timestamp:c})=>{const d=c-u;d>=l&&(Ln(o),i(d-l))};return Rt.setup(o,!0),()=>Ln(o)}const Sy=["TopLeft","TopRight","BottomLeft","BottomRight"],WS=Sy.length,Op=i=>typeof i=="string"?parseFloat(i):i,Cp=i=>typeof i=="number"||nt.test(i);function $S(i,l,u,o,c,d){c?(i.opacity=wt(0,u.opacity??1,IS(o)),i.opacityExit=wt(l.opacity??1,0,t2(o))):d&&(i.opacity=wt(l.opacity??1,u.opacity??1,o));for(let h=0;h<WS;h++){const g=`border${Sy[h]}Radius`;let y=jp(l,g),m=jp(u,g);if(y===void 0&&m===void 0)continue;y||(y=0),m||(m=0),y===0||m===0||Cp(y)===Cp(m)?(i[g]=Math.max(wt(Op(y),Op(m),o),0),(ke.test(m)||ke.test(y))&&(i[g]+="%")):i[g]=m}(l.rotate||u.rotate)&&(i.rotate=wt(l.rotate||0,u.rotate||0,o))}function jp(i,l){return i[l]!==void 0?i[l]:i.borderRadius}const IS=Ty(0,.5,h0),t2=Ty(.5,.95,Ve);function Ty(i,l,u){return o=>o<i?0:o>l?1:u(dl(i,l,o))}function Vp(i,l){i.min=l.min,i.max=l.max}function Ce(i,l){Vp(i.x,l.x),Vp(i.y,l.y)}function zp(i,l){i.translate=l.translate,i.scale=l.scale,i.originPoint=l.originPoint,i.origin=l.origin}function _p(i,l,u,o,c){return i-=l,i=Fs(i,1/u,o),c!==void 0&&(i=Fs(i,1/c,o)),i}function e2(i,l=0,u=1,o=.5,c,d=i,h=i){if(ke.test(l)&&(l=parseFloat(l),l=wt(h.min,h.max,l/100)-h.min),typeof l!="number")return;let g=wt(d.min,d.max,o);i===d&&(g-=l),i.min=_p(i.min,l,u,g,c),i.max=_p(i.max,l,u,g,c)}function Up(i,l,[u,o,c],d,h){e2(i,l[u],l[o],l[c],l.scale,d,h)}const n2=["x","scaleX","originX"],a2=["y","scaleY","originY"];function Bp(i,l,u,o){Up(i.x,l,n2,u?u.x:void 0,o?o.x:void 0),Up(i.y,l,a2,u?u.y:void 0,o?o.y:void 0)}function Hp(i){return i.translate===0&&i.scale===1}function Ay(i){return Hp(i.x)&&Hp(i.y)}function Lp(i,l){return i.min===l.min&&i.max===l.max}function i2(i,l){return Lp(i.x,l.x)&&Lp(i.y,l.y)}function qp(i,l){return Math.round(i.min)===Math.round(l.min)&&Math.round(i.max)===Math.round(l.max)}function My(i,l){return qp(i.x,l.x)&&qp(i.y,l.y)}function Yp(i){return le(i.x)/le(i.y)}function Gp(i,l){return i.translate===l.translate&&i.scale===l.scale&&i.originPoint===l.originPoint}class l2{constructor(){this.members=[]}add(l){sc(this.members,l),l.scheduleRender()}remove(l){if(uc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let o;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){o=d;break}}return o?(this.promote(o),!0):!1}promote(l,u){const o=this.lead;if(l!==o&&(this.prevLead=o,this.lead=l,l.show(),o)){o.instance&&o.scheduleRender(),l.scheduleRender(),l.resumeFrom=o,u&&(l.resumeFrom.preserveOpacity=!0),o.snapshot&&(l.snapshot=o.snapshot,l.snapshot.latestValues=o.animationValues||o.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&o.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:o}=l;u.onExitComplete&&u.onExitComplete(),o&&o.options.onExitComplete&&o.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function s2(i,l,u){let o="";const c=i.x.translate/l.x,d=i.y.translate/l.y,h=u?.z||0;if((c||d||h)&&(o=`translate3d(${c}px, ${d}px, ${h}px) `),(l.x!==1||l.y!==1)&&(o+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:m,rotate:v,rotateX:x,rotateY:A,skewX:z,skewY:B}=u;m&&(o=`perspective(${m}px) ${o}`),v&&(o+=`rotate(${v}deg) `),x&&(o+=`rotateX(${x}deg) `),A&&(o+=`rotateY(${A}deg) `),z&&(o+=`skewX(${z}deg) `),B&&(o+=`skewY(${B}deg) `)}const g=i.x.scale*l.x,y=i.y.scale*l.y;return(g!==1||y!==1)&&(o+=`scale(${g}, ${y})`),o||"none"}const jr=["","X","Y","Z"],u2=1e3;let o2=0;function Vr(i,l,u,o){const{latestValues:c}=l;c[i]&&(u[i]=c[i],l.setStaticValue(i,0),o&&(o[i]=0))}function Ey(i){if(i.hasCheckedOptimisedAppear=!0,i.root===i)return;const{visualElement:l}=i.options;if(!l)return;const u=uy(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=i.options;window.MotionCancelOptimisedAnimation(u,"transform",Rt,!(c||d))}const{parent:o}=i;o&&!o.hasCheckedOptimisedAppear&&Ey(o)}function Dy({attachResizeListener:i,defaultParent:l,measureScroll:u,checkIsScrollRoot:o,resetTransform:c}){return class{constructor(h={},g=l?.()){this.id=o2++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(f2),this.nodes.forEach(p2),this.nodes.forEach(y2),this.nodes.forEach(h2)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=h,this.root=g?g.root||g:this,this.path=g?[...g.path,g]:[],this.parent=g,this.depth=g?g.depth+1:0;for(let y=0;y<this.path.length;y++)this.path[y].shouldResetTransform=!0;this.root===this&&(this.nodes=new PS)}addEventListener(h,g){return this.eventHandlers.has(h)||this.eventHandlers.set(h,new cc),this.eventHandlers.get(h).add(g)}notifyListeners(h,...g){const y=this.eventHandlers.get(h);y&&y.notify(...g)}hasListeners(h){return this.eventHandlers.has(h)}mount(h){if(this.instance)return;this.isSVG=K0(h)&&!rx(h),this.instance=h;const{layoutId:g,layout:y,visualElement:m}=this.options;if(m&&!m.current&&m.mount(h),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(y||g)&&(this.isLayoutDirty=!0),i){let v,x=0;const A=()=>this.root.updateBlockedByResize=!1;Rt.read(()=>{x=window.innerWidth}),i(h,()=>{const z=window.innerWidth;z!==x&&(x=z,this.root.updateBlockedByResize=!0,v&&v(),v=FS(A,250),Zs.hasAnimatedSinceResize&&(Zs.hasAnimatedSinceResize=!1,this.nodes.forEach(Zp)))})}g&&this.root.registerSharedNode(g,this),this.options.animate!==!1&&m&&(g||y)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:x,hasRelativeLayoutChanged:A,layout:z})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const B=this.options.transition||m.getDefaultTransition()||S2,{onLayoutAnimationStart:Q,onLayoutAnimationComplete:G}=m.getProps(),q=!this.targetLayout||!My(this.targetLayout,z),K=!x&&A;if(this.options.layoutRoot||this.resumeFrom||K||x&&(q||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const L={...Ac(B,"layout"),onPlay:Q,onComplete:G};(m.shouldReduceMotion||this.options.layoutRoot)&&(L.delay=0,L.type=!1),this.startAnimation(L),this.setAnimationOrigin(v,K)}else x||Zp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=z})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const h=this.getStack();h&&h.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ln(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(g2),this.animationId++)}getTransformTemplate(){const{visualElement:h}=this.options;return h&&h.getProps().transformTemplate}willUpdate(h=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ey(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const x=this.path[v];x.shouldResetTransform=!0,x.updateScroll("snapshot"),x.options.layoutRoot&&x.willUpdate(!1)}const{layoutId:g,layout:y}=this.options;if(g===void 0&&!y)return;const m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),h&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Xp);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Qp);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(m2),this.nodes.forEach(r2),this.nodes.forEach(c2)):this.nodes.forEach(Qp),this.clearAllSnapshots();const g=fe.now();$t.delta=fn(0,1e3/60,g-$t.timestamp),$t.timestamp=g,$t.isProcessing=!0,Ar.update.process($t),Ar.preRender.process($t),Ar.render.process($t),$t.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ec.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(d2),this.sharedNodes.forEach(v2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Rt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Rt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!le(this.snapshot.measuredBox.x)&&!le(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let y=0;y<this.path.length;y++)this.path[y].updateScroll();const h=this.layout;this.layout=this.measure(!1),this.layoutCorrected=_t(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:g}=this.options;g&&g.notify("LayoutMeasure",this.layout.layoutBox,h?h.layoutBox:void 0)}updateScroll(h="measure"){let g=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===h&&(g=!1),g&&this.instance){const y=o(this.instance);this.scroll={animationId:this.root.animationId,phase:h,isRoot:y,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:y}}}resetTransform(){if(!c)return;const h=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,g=this.projectionDelta&&!Ay(this.projectionDelta),y=this.getTransformTemplate(),m=y?y(this.latestValues,""):void 0,v=m!==this.prevTransformTemplateValue;h&&this.instance&&(g||ua(this.latestValues)||v)&&(c(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(h=!0){const g=this.measurePageBox();let y=this.removeElementScroll(g);return h&&(y=this.removeTransform(y)),T2(y),{animationId:this.root.animationId,measuredBox:g,layoutBox:y,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:h}=this.options;if(!h)return _t();const g=h.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(A2))){const{scroll:m}=this.root;m&&(ti(g.x,m.offset.x),ti(g.y,m.offset.y))}return g}removeElementScroll(h){const g=_t();if(Ce(g,h),this.scroll?.wasRoot)return g;for(let y=0;y<this.path.length;y++){const m=this.path[y],{scroll:v,options:x}=m;m!==this.root&&v&&x.layoutScroll&&(v.wasRoot&&Ce(g,h),ti(g.x,v.offset.x),ti(g.y,v.offset.y))}return g}applyTransform(h,g=!1){const y=_t();Ce(y,h);for(let m=0;m<this.path.length;m++){const v=this.path[m];!g&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ei(y,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),ua(v.latestValues)&&ei(y,v.latestValues)}return ua(this.latestValues)&&ei(y,this.latestValues),y}removeTransform(h){const g=_t();Ce(g,h);for(let y=0;y<this.path.length;y++){const m=this.path[y];if(!m.instance||!ua(m.latestValues))continue;Wr(m.latestValues)&&m.updateSnapshot();const v=_t(),x=m.measurePageBox();Ce(v,x),Bp(g,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,v)}return ua(this.latestValues)&&Bp(g,this.latestValues),g}setTargetDelta(h){this.targetDelta=h,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(h){this.options={...this.options,...h,crossfade:h.crossfade!==void 0?h.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$t.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(h=!1){const g=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=g.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=g.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=g.isSharedProjectionDirty);const y=!!this.resumingFrom||this!==g;if(!(h||y&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:x}=this.options;if(!(!this.layout||!(v||x))){if(this.resolvedRelativeTargetAt=$t.timestamp,!this.targetDelta&&!this.relativeTarget){const A=this.getClosestProjectingParent();A&&A.layout&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_t(),this.relativeTargetOrigin=_t(),hl(this.relativeTargetOrigin,this.layout.layoutBox,A.layout.layoutBox),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=_t(),this.targetWithTransforms=_t()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),RS(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ce(this.target,this.layout.layoutBox),py(this.target,this.targetDelta)):Ce(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const A=this.getClosestProjectingParent();A&&!!A.resumingFrom==!!this.resumingFrom&&!A.options.layoutScroll&&A.target&&this.animationProgress!==1?(this.relativeParent=A,this.forceRelativeParentToResolveTarget(),this.relativeTarget=_t(),this.relativeTargetOrigin=_t(),hl(this.relativeTargetOrigin,this.target,A.target),Ce(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Wr(this.parent.latestValues)||my(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const h=this.getLead(),g=!!this.resumingFrom||this!==h;let y=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(y=!1),g&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(y=!1),this.resolvedRelativeTargetAt===$t.timestamp&&(y=!1),y)return;const{layout:m,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(m||v))return;Ce(this.layoutCorrected,this.layout.layoutBox);const x=this.treeScale.x,A=this.treeScale.y;NS(this.layoutCorrected,this.treeScale,this.path,g),h.layout&&!h.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(h.target=h.layout.layoutBox,h.targetWithTransforms=_t());const{target:z}=h;if(!z){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(zp(this.prevProjectionDelta.x,this.projectionDelta.x),zp(this.prevProjectionDelta.y,this.projectionDelta.y)),fl(this.projectionDelta,this.layoutCorrected,z,this.latestValues),(this.treeScale.x!==x||this.treeScale.y!==A||!Gp(this.projectionDelta.x,this.prevProjectionDelta.x)||!Gp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",z))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(h=!0){if(this.options.visualElement?.scheduleRender(),h){const g=this.getStack();g&&g.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Ia(),this.projectionDelta=Ia(),this.projectionDeltaWithTransform=Ia()}setAnimationOrigin(h,g=!1){const y=this.snapshot,m=y?y.latestValues:{},v={...this.latestValues},x=Ia();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!g;const A=_t(),z=y?y.source:void 0,B=this.layout?this.layout.source:void 0,Q=z!==B,G=this.getStack(),q=!G||G.members.length<=1,K=!!(Q&&!q&&this.options.crossfade===!0&&!this.path.some(x2));this.animationProgress=0;let L;this.mixTargetDelta=at=>{const H=at/1e3;Kp(x.x,h.x,H),Kp(x.y,h.y,H),this.setTargetDelta(x),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(hl(A,this.layout.layoutBox,this.relativeParent.layout.layoutBox),b2(this.relativeTarget,this.relativeTargetOrigin,A,H),L&&i2(this.relativeTarget,L)&&(this.isProjectionDirty=!1),L||(L=_t()),Ce(L,this.relativeTarget)),Q&&(this.animationValues=v,$S(v,m,this.latestValues,H,K,q)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=H},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(h){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Ln(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Rt.update(()=>{Zs.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ni(0)),this.currentAnimation=kS(this.motionValue,[0,1e3],{...h,velocity:0,isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),h.onUpdate&&h.onUpdate(g)},onStop:()=>{},onComplete:()=>{h.onComplete&&h.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const h=this.getStack();h&&h.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(u2),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const h=this.getLead();let{targetWithTransforms:g,target:y,layout:m,latestValues:v}=h;if(!(!g||!y||!m)){if(this!==h&&this.layout&&m&&Ry(this.options.animationType,this.layout.layoutBox,m.layoutBox)){y=this.target||_t();const x=le(this.layout.layoutBox.x);y.x.min=h.target.x.min,y.x.max=y.x.min+x;const A=le(this.layout.layoutBox.y);y.y.min=h.target.y.min,y.y.max=y.y.min+A}Ce(g,y),ei(g,v),fl(this.projectionDeltaWithTransform,this.layoutCorrected,g,v)}}registerSharedNode(h,g){this.sharedNodes.has(h)||this.sharedNodes.set(h,new l2),this.sharedNodes.get(h).add(g);const m=g.options.initialPromotionConfig;g.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(g):void 0})}isLead(){const h=this.getStack();return h?h.lead===this:!0}getLead(){const{layoutId:h}=this.options;return h?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:h}=this.options;return h?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:h}=this.options;if(h)return this.root.sharedNodes.get(h)}promote({needsReset:h,transition:g,preserveFollowOpacity:y}={}){const m=this.getStack();m&&m.promote(this,y),h&&(this.projectionDelta=void 0,this.needsReset=!0),g&&this.setOptions({transition:g})}relegate(){const h=this.getStack();return h?h.relegate(this):!1}resetSkewAndRotation(){const{visualElement:h}=this.options;if(!h)return;let g=!1;const{latestValues:y}=h;if((y.z||y.rotate||y.rotateX||y.rotateY||y.rotateZ||y.skewX||y.skewY)&&(g=!0),!g)return;const m={};y.z&&Vr("z",h,m,this.animationValues);for(let v=0;v<jr.length;v++)Vr(`rotate${jr[v]}`,h,m,this.animationValues),Vr(`skew${jr[v]}`,h,m,this.animationValues);h.render();for(const v in m)h.setStaticValue(v,m[v]),this.animationValues&&(this.animationValues[v]=m[v]);h.scheduleRender()}applyProjectionStyles(h,g){if(!this.instance||this.isSVG)return;if(!this.isVisible){h.visibility="hidden";return}const y=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,h.visibility="",h.opacity="",h.pointerEvents=Qs(g?.pointerEvents)||"",h.transform=y?y(this.latestValues,""):"none";return}const m=this.getLead();if(!this.projectionDelta||!this.layout||!m.target){this.options.layoutId&&(h.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,h.pointerEvents=Qs(g?.pointerEvents)||""),this.hasProjected&&!ua(this.latestValues)&&(h.transform=y?y({},""):"none",this.hasProjected=!1);return}h.visibility="";const v=m.animationValues||m.latestValues;this.applyTransformsToTarget();let x=s2(this.projectionDeltaWithTransform,this.treeScale,v);y&&(x=y(v,x)),h.transform=x;const{x:A,y:z}=this.projectionDelta;h.transformOrigin=`${A.origin*100}% ${z.origin*100}% 0`,m.animationValues?h.opacity=m===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:h.opacity=m===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const B in gl){if(v[B]===void 0)continue;const{correct:Q,applyTo:G,isCSSVariable:q}=gl[B],K=x==="none"?v[B]:Q(v[B],m);if(G){const L=G.length;for(let at=0;at<L;at++)h[G[at]]=K}else q?this.options.visualElement.renderState.vars[B]=K:h[B]=K}this.options.layoutId&&(h.pointerEvents=m===this?Qs(g?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(h=>h.currentAnimation?.stop()),this.root.nodes.forEach(Xp),this.root.sharedNodes.clear()}}}function r2(i){i.updateLayout()}function c2(i){const l=i.resumeFrom?.snapshot||i.snapshot;if(i.isLead()&&i.layout&&l&&i.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:o}=i.layout,{animationType:c}=i.options,d=l.source!==i.layout.source;c==="size"?je(v=>{const x=d?l.measuredBox[v]:l.layoutBox[v],A=le(x);x.min=u[v].min,x.max=x.min+A}):Ry(c,l.layoutBox,u)&&je(v=>{const x=d?l.measuredBox[v]:l.layoutBox[v],A=le(u[v]);x.max=x.min+A,i.relativeTarget&&!i.currentAnimation&&(i.isProjectionDirty=!0,i.relativeTarget[v].max=i.relativeTarget[v].min+A)});const h=Ia();fl(h,u,l.layoutBox);const g=Ia();d?fl(g,i.applyTransform(o,!0),l.measuredBox):fl(g,u,l.layoutBox);const y=!Ay(h);let m=!1;if(!i.resumeFrom){const v=i.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:x,layout:A}=v;if(x&&A){const z=_t();hl(z,l.layoutBox,x.layoutBox);const B=_t();hl(B,u,A.layoutBox),My(z,B)||(m=!0),v.options.layoutRoot&&(i.relativeTarget=B,i.relativeTargetOrigin=z,i.relativeParent=v)}}}i.notifyListeners("didUpdate",{layout:u,snapshot:l,delta:g,layoutDelta:h,hasLayoutChanged:y,hasRelativeLayoutChanged:m})}else if(i.isLead()){const{onExitComplete:u}=i.options;u&&u()}i.options.transition=void 0}function f2(i){i.parent&&(i.isProjecting()||(i.isProjectionDirty=i.parent.isProjectionDirty),i.isSharedProjectionDirty||(i.isSharedProjectionDirty=!!(i.isProjectionDirty||i.parent.isProjectionDirty||i.parent.isSharedProjectionDirty)),i.isTransformDirty||(i.isTransformDirty=i.parent.isTransformDirty))}function h2(i){i.isProjectionDirty=i.isSharedProjectionDirty=i.isTransformDirty=!1}function d2(i){i.clearSnapshot()}function Xp(i){i.clearMeasurements()}function Qp(i){i.isLayoutDirty=!1}function m2(i){const{visualElement:l}=i.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),i.resetTransform()}function Zp(i){i.finishAnimation(),i.targetDelta=i.relativeTarget=i.target=void 0,i.isProjectionDirty=!0}function p2(i){i.resolveTargetDelta()}function y2(i){i.calcProjection()}function g2(i){i.resetSkewAndRotation()}function v2(i){i.removeLeadSnapshot()}function Kp(i,l,u){i.translate=wt(l.translate,0,u),i.scale=wt(l.scale,1,u),i.origin=l.origin,i.originPoint=l.originPoint}function kp(i,l,u,o){i.min=wt(l.min,u.min,o),i.max=wt(l.max,u.max,o)}function b2(i,l,u,o){kp(i.x,l.x,u.x,o),kp(i.y,l.y,u.y,o)}function x2(i){return i.animationValues&&i.animationValues.opacityExit!==void 0}const S2={duration:.45,ease:[.4,0,.1,1]},Jp=i=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(i),Pp=Jp("applewebkit/")&&!Jp("chrome/")?Math.round:Ve;function Fp(i){i.min=Pp(i.min),i.max=Pp(i.max)}function T2(i){Fp(i.x),Fp(i.y)}function Ry(i,l,u){return i==="position"||i==="preserve-aspect"&&!DS(Yp(l),Yp(u),.2)}function A2(i){return i!==i.root&&i.scroll?.wasRoot}const M2=Dy({attachResizeListener:(i,l)=>bl(i,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),zr={current:void 0},Ny=Dy({measureScroll:i=>({x:i.scrollLeft,y:i.scrollTop}),defaultParent:()=>{if(!zr.current){const i=new M2({});i.mount(window),i.setOptions({layoutScroll:!0}),zr.current=i}return zr.current},resetTransform:(i,l)=>{i.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:i=>window.getComputedStyle(i).position==="fixed"}),E2={pan:{Feature:XS},drag:{Feature:GS,ProjectionNode:Ny,MeasureLayout:xy}};function Wp(i,l,u){const{props:o}=i;i.animationState&&o.whileHover&&i.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=o[c];d&&Rt.postRender(()=>d(l,Al(l)))}class D2 extends Yn{mount(){const{current:l}=this.node;l&&(this.unmount=ix(l,(u,o)=>(Wp(this.node,o,"Start"),c=>Wp(this.node,c,"End"))))}unmount(){}}class R2 extends Yn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=xl(bl(this.node.current,"focus",()=>this.onFocus()),bl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function $p(i,l,u){const{props:o}=i;if(i.current instanceof HTMLButtonElement&&i.current.disabled)return;i.animationState&&o.whileTap&&i.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=o[c];d&&Rt.postRender(()=>d(l,Al(l)))}class N2 extends Yn{mount(){const{current:l}=this.node;l&&(this.unmount=ox(l,(u,o)=>($p(this.node,o,"Start"),(c,{success:d})=>$p(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const tc=new WeakMap,_r=new WeakMap,w2=i=>{const l=tc.get(i.target);l&&l(i)},O2=i=>{i.forEach(w2)};function C2({root:i,...l}){const u=i||document;_r.has(u)||_r.set(u,{});const o=_r.get(u),c=JSON.stringify(l);return o[c]||(o[c]=new IntersectionObserver(O2,{root:i,...l})),o[c]}function j2(i,l,u){const o=C2(l);return tc.set(i,u),o.observe(i),()=>{tc.delete(i),o.unobserve(i)}}const V2={some:0,all:1};class z2 extends Yn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:o,amount:c="some",once:d}=l,h={root:u?u.current:void 0,rootMargin:o,threshold:typeof c=="number"?c:V2[c]},g=y=>{const{isIntersecting:m}=y;if(this.isInView===m||(this.isInView=m,d&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);const{onViewportEnter:v,onViewportLeave:x}=this.node.getProps(),A=m?v:x;A&&A(y)};return j2(this.node.current,h,g)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(_2(l,u))&&this.startObserver()}unmount(){}}function _2({viewport:i={}},{viewport:l={}}={}){return u=>i[u]!==l[u]}const U2={inView:{Feature:z2},tap:{Feature:N2},focus:{Feature:R2},hover:{Feature:D2}},B2={layout:{ProjectionNode:Ny,MeasureLayout:xy}},ec={current:null},wy={current:!1};function H2(){if(wy.current=!0,!!ic)if(window.matchMedia){const i=window.matchMedia("(prefers-reduced-motion)"),l=()=>ec.current=i.matches;i.addEventListener("change",l),l()}else ec.current=!1}const L2=new WeakMap;function q2(i,l,u){for(const o in l){const c=l[o],d=u[o];if(ae(c))i.addValue(o,c);else if(ae(d))i.addValue(o,ni(c,{owner:i}));else if(d!==c)if(i.hasValue(o)){const h=i.getValue(o);h.liveStyle===!0?h.jump(c):h.hasAnimated||h.set(c)}else{const h=i.getStaticValue(o);i.addValue(o,ni(h!==void 0?h:c,{owner:i}))}}for(const o in u)l[o]===void 0&&i.removeValue(o);return l}const Ip=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Y2{scrapeMotionValuesFromProps(l,u,o){return{}}constructor({parent:l,props:u,presenceContext:o,reducedMotionConfig:c,blockInitialAnimation:d,visualState:h},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Tc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const A=fe.now();this.renderScheduledAt<A&&(this.renderScheduledAt=A,Rt.render(this.render,!1,!0))};const{latestValues:y,renderState:m}=h;this.latestValues=y,this.baseTarget={...y},this.initialValues=u.initial?{...y}:{},this.renderState=m,this.parent=l,this.props=u,this.presenceContext=o,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=g,this.blockInitialAnimation=!!d,this.isControllingVariants=Is(u),this.isVariantNode=F0(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...x}=this.scrapeMotionValuesFromProps(u,{},this);for(const A in x){const z=x[A];y[A]!==void 0&&ae(z)&&z.set(y[A],!1)}}mount(l){this.current=l,L2.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,o)=>this.bindToMotionValue(o,u)),wy.current||H2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ec.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ln(this.notifyUpdate),Ln(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const o=si.has(l);o&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",g=>{this.latestValues[l]=g,this.props.onUpdate&&Rt.preRender(this.notifyUpdate),o&&this.projection&&(this.projection.isTransformDirty=!0)}),d=u.on("renderRequest",this.scheduleRender);let h;window.MotionCheckAppearSync&&(h=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d(),h&&h(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ai){const u=ai[l];if(!u)continue;const{isEnabled:o,Feature:c}=u;if(!this.features[l]&&c&&o(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):_t()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let o=0;o<Ip.length;o++){const c=Ip[o];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,h=l[d];h&&(this.propEventSubscriptions[c]=this.on(c,h))}this.prevMotionValues=q2(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const o=this.values.get(l);u!==o&&(o&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let o=this.values.get(l);return o===void 0&&u!==void 0&&(o=ni(u===null?void 0:u,{owner:this}),this.addValue(l,o)),o}readValue(l,u){let o=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return o!=null&&(typeof o=="string"&&(n0(o)||i0(o))?o=parseFloat(o):!fx(o)&&qn.test(u)&&(o=q0(l,u)),this.setBaseTarget(l,ae(o)?o.get():o)),ae(o)?o.get():o}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){const{initial:u}=this.props;let o;if(typeof u=="string"||typeof u=="object"){const d=Vc(this.props,u,this.presenceContext?.custom);d&&(o=d[l])}if(u&&o!==void 0)return o;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ae(c)?c:this.initialValues[l]!==void 0&&o===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new cc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}}class Oy extends Y2{constructor(){super(...arguments),this.KeyframeResolver=tx}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:o}){delete u[l],delete o[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ae(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function Cy(i,{style:l,vars:u},o,c){const d=i.style;let h;for(h in l)d[h]=l[h];c?.applyProjectionStyles(d,o);for(h in u)d.setProperty(h,u[h])}function G2(i){return window.getComputedStyle(i)}class X2 extends Oy{constructor(){super(...arguments),this.type="html",this.renderInstance=Cy}readValueFromInstance(l,u){if(si.has(u))return this.projection?.isProjecting?Gr(u):bb(l,u);{const o=G2(l),c=(dc(u)?o.getPropertyValue(u):o[u])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(l,{transformPagePoint:u}){return yy(l,u)}build(l,u,o){Oc(l,u,o.transformTemplate)}scrapeMotionValuesFromProps(l,u,o){return zc(l,u,o)}}const jy=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Q2(i,l,u,o){Cy(i,l,void 0,o);for(const c in l.attrs)i.setAttribute(jy.has(c)?c:wc(c),l.attrs[c])}class Z2 extends Oy{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=_t}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(si.has(u)){const o=L0(u);return o&&o.default||0}return u=jy.has(u)?u:wc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,o){return sy(l,u,o)}build(l,u,o){ny(l,u,this.isSVGTag,o.transformTemplate,o.style)}renderInstance(l,u,o,c){Q2(l,u,o,c)}mount(l){this.isSVGTag=iy(l.tagName),super.mount(l)}}const K2=(i,l)=>jc(i)?new Z2(l):new X2(l,{allowProjection:i!==P.Fragment}),k2=Kx({...vS,...U2,...E2,...B2},K2),tt=gx(k2),J2={some:0,all:1};function P2(i,l,{root:u,margin:o,amount:c="some"}={}){const d=Y0(i),h=new WeakMap,g=m=>{m.forEach(v=>{const x=h.get(v.target);if(v.isIntersecting!==!!x)if(v.isIntersecting){const A=l(v.target,v);typeof A=="function"?h.set(v.target,A):y.unobserve(v.target)}else typeof x=="function"&&(x(v),h.delete(v.target))})},y=new IntersectionObserver(g,{root:u,rootMargin:o,threshold:typeof c=="number"?c:J2[c]});return d.forEach(m=>y.observe(m)),()=>y.disconnect()}function tu(i,{root:l,margin:u,amount:o,once:c=!1,initial:d=!1}={}){const[h,g]=P.useState(d);return P.useEffect(()=>{if(!i.current||c&&h)return;const y=()=>(g(!0),c?void 0:()=>g(!1)),m={root:l&&l.current||void 0,margin:u,amount:o};return P2(i.current,y,m)},[l,i,u,c,o]),h}/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F2=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),W2=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,u,o)=>o?o.toUpperCase():u.toLowerCase()),t0=i=>{const l=W2(i);return l.charAt(0).toUpperCase()+l.slice(1)},Vy=(...i)=>i.filter((l,u,o)=>!!l&&l.trim()!==""&&o.indexOf(l)===u).join(" ").trim(),$2=i=>{for(const l in i)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var I2={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tT=P.forwardRef(({color:i="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:h,...g},y)=>P.createElement("svg",{ref:y,...I2,width:l,height:l,stroke:i,strokeWidth:o?Number(u)*24/Number(l):u,className:Vy("lucide",c),...!d&&!$2(g)&&{"aria-hidden":"true"},...g},[...h.map(([m,v])=>P.createElement(m,v)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=(i,l)=>{const u=P.forwardRef(({className:o,...c},d)=>P.createElement(tT,{ref:d,iconNode:l,className:Vy(`lucide-${F2(t0(i))}`,`lucide-${i}`,o),...c}));return u.displayName=t0(i),u};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eT=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],nT=Ut("chart-column",eT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aT=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],iT=Ut("chevron-down",aT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lT=[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]],zy=Ut("code",lT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sT=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],Uc=Ut("database",sT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uT=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],oT=Ut("external-link",uT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rT=[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]],cT=Ut("gamepad-2",rT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fT=[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]],Bc=Ut("github",fT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hT=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],dT=Ut("globe",hT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mT=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]],pT=Ut("graduation-cap",mT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yT=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],nc=Ut("mail",yT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gT=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],vT=Ut("map-pin",gT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bT=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],xT=Ut("menu",bT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ST=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],TT=Ut("message-square",ST);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AT=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],_y=Ut("phone",AT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MT=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],ET=Ut("send",MT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DT=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],RT=Ut("shield",DT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NT=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],Uy=Ut("smartphone",NT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wT=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],OT=Ut("target",wT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CT=[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]],jT=Ut("test-tube",CT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VT=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],zT=Ut("user",VT);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _T=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],UT=Ut("x",_T),BT=()=>{const i={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.2}}},l={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.5}}};return M.jsxs("section",{id:"hero",className:"min-h-screen flex items-center justify-center gradient-bg relative overflow-hidden",children:[M.jsxs("div",{className:"absolute inset-0",children:[M.jsx(tt.div,{animate:{scale:[1,1.2,1],rotate:[0,180,360]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-64 h-64 bg-white/10 rounded-full blur-xl"}),M.jsx(tt.div,{animate:{scale:[1.2,1,1.2],rotate:[360,180,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-white/5 rounded-full blur-2xl"})]}),M.jsx("div",{className:"container mx-auto px-4 text-center relative z-10",children:M.jsxs(tt.div,{variants:i,initial:"hidden",animate:"visible",className:"max-w-4xl mx-auto",children:[M.jsxs(tt.h1,{variants:l,className:"text-5xl md:text-7xl font-bold text-white mb-6",children:["Hi, I'm"," ",M.jsx("span",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent",children:"Darshan Adhikari"})]}),M.jsx(tt.p,{variants:l,className:"text-xl md:text-2xl text-white/90 mb-8 leading-relaxed",children:"Senior Computer Science Student & Full-Stack Developer"}),M.jsx(tt.p,{variants:l,className:"text-lg text-white/80 mb-12 max-w-2xl mx-auto",children:"Passionate about creating innovative solutions through data analysis and full-stack development. Experienced in React Native, Firebase, Python, and modern web technologies."}),M.jsxs(tt.div,{variants:l,className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16",children:[M.jsx(tt.a,{href:"#projects",whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn btn-primary px-8 py-4 text-lg",children:"View My Work"}),M.jsx(tt.a,{href:"#contact",whileHover:{scale:1.05},whileTap:{scale:.95},className:"btn btn-outline px-8 py-4 text-lg text-white border-white hover:bg-white hover:text-gray-800",children:"Get In Touch"})]}),M.jsxs(tt.div,{variants:l,className:"flex justify-center space-x-6",children:[M.jsx(tt.a,{href:"https://github.com/DarshanAdh",target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.2,rotate:5},className:"text-white/80 hover:text-white transition-colors duration-300",children:M.jsx(Bc,{size:32})}),M.jsx(tt.a,{href:"mailto:<EMAIL>",whileHover:{scale:1.2,rotate:-5},className:"text-white/80 hover:text-white transition-colors duration-300",children:M.jsx(nc,{size:32})}),M.jsx(tt.a,{href:"tel:(*************",whileHover:{scale:1.2,rotate:5},className:"text-white/80 hover:text-white transition-colors duration-300",children:M.jsx(_y,{size:32})})]})]})}),M.jsx(tt.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:M.jsx("a",{href:"#about",className:"text-white/80 hover:text-white transition-colors duration-300",children:M.jsx(iT,{size:32})})})]})},HT=()=>{const i=P.useRef(null),l=tu(i,{once:!0}),u={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.2}}},o={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6}}},c=[{icon:M.jsx(pT,{className:"w-8 h-8"}),title:"Education",description:"Senior Computer Science Student at Southeast Missouri State University"},{icon:M.jsx(OT,{className:"w-8 h-8"}),title:"Focus Areas",description:"Data Analysis, Full-Stack Development, Mobile Applications"},{icon:M.jsx(zy,{className:"w-8 h-8"}),title:"Development",description:"React Native, Firebase, Python, JavaScript, TypeScript"},{icon:M.jsx(Uc,{className:"w-8 h-8"}),title:"Data & Backend",description:"MySQL, MongoDB, RESTful APIs, Cloud Functions"}];return M.jsx("section",{id:"about",className:"py-20 bg-gray-50",ref:i,children:M.jsx("div",{className:"container mx-auto px-4",children:M.jsxs(tt.div,{variants:u,initial:"hidden",animate:l?"visible":"hidden",className:"max-w-6xl mx-auto",children:[M.jsxs(tt.div,{variants:o,className:"text-center mb-16",children:[M.jsx("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"About Me"}),M.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"})]}),M.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[M.jsxs(tt.div,{variants:o,className:"space-y-6",children:[M.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-gray-800 mb-4",children:"Career Objective"}),M.jsx("p",{className:"text-lg text-gray-600 leading-relaxed",children:"Motivated and detail-oriented senior Computer Science student with a strong foundation in data analysis and full-stack development. I am passionate about pursuing opportunities in the field of data analysis, where I can apply my analytical skills and technical knowledge to solve real-world problems."}),M.jsx("p",{className:"text-lg text-gray-600 leading-relaxed",children:"Proficient in Python, Java, Flask, MySQL, JavaScript, HTML, and CSS, with extensive experience in developing comprehensive web applications and mobile solutions using modern frameworks like React Native and Firebase."}),M.jsx(tt.div,{whileHover:{scale:1.05},className:"inline-block",children:M.jsx("a",{href:"#contact",className:"btn btn-primary mt-6",children:"Let's Connect"})})]}),M.jsx(tt.div,{variants:o,className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:c.map((d,h)=>M.jsxs(tt.div,{variants:o,whileHover:{scale:1.05,boxShadow:"0 20px 40px rgba(0,0,0,0.1)"},className:"card text-center p-6",children:[M.jsx(tt.div,{whileHover:{rotate:360},transition:{duration:.5},className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full mb-4",children:d.icon}),M.jsx("h4",{className:"text-xl font-semibold text-gray-800 mb-2",children:d.title}),M.jsx("p",{className:"text-gray-600",children:d.description})]},h))})]}),M.jsx(tt.div,{variants:o,className:"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 text-center",children:[{number:"4+",label:"Major Projects"},{number:"10+",label:"Technologies"},{number:"35%",label:"Medication Adherence Improvement"},{number:"40%",label:"Admin Time Reduction"}].map((d,h)=>M.jsxs(tt.div,{variants:o,whileHover:{scale:1.1},className:"p-6",children:[M.jsx("div",{className:"text-3xl md:text-4xl font-bold gradient-text mb-2",children:d.number}),M.jsx("div",{className:"text-gray-600 font-medium",children:d.label})]},h))})]})})})},LT=()=>{const i=P.useRef(null),l=tu(i,{once:!0}),u=[{icon:M.jsx(zy,{className:"w-8 h-8"}),title:"Programming Languages",skills:[{name:"Python",level:90},{name:"JavaScript",level:85},{name:"TypeScript",level:80},{name:"Java",level:85},{name:"C/C++",level:75},{name:"C#",level:70}]},{icon:M.jsx(Uc,{className:"w-8 h-8"}),title:"Frameworks & Tools",skills:[{name:"React/React Native",level:90},{name:"Node.js/Express",level:85},{name:"Flask",level:80},{name:"Firebase",level:85},{name:"MongoDB",level:80},{name:"MySQL",level:85}]},{icon:M.jsx(Uy,{className:"w-8 h-8"}),title:"Mobile & Web",skills:[{name:"React Native",level:90},{name:"Expo",level:85},{name:"HTML/CSS",level:90},{name:"Responsive Design",level:85},{name:"RESTful APIs",level:85},{name:"JavaFX",level:75}]},{icon:M.jsx(RT,{className:"w-8 h-8"}),title:"Security & Auth",skills:[{name:"JWT Authentication",level:85},{name:"Firebase Auth",level:80},{name:"Bcrypt Hashing",level:80},{name:"Role-Based Access",level:85},{name:"Security Rules",level:75}]},{icon:M.jsx(jT,{className:"w-8 h-8"}),title:"Testing & Quality",skills:[{name:"White Box Testing",level:80},{name:"Test Documentation",level:85},{name:"Git/GitHub",level:90},{name:"Agile Methodology",level:80},{name:"Code Reviews",level:85}]},{icon:M.jsx(nT,{className:"w-8 h-8"}),title:"Data Analysis",skills:[{name:"NumPy",level:80},{name:"Pandas",level:85},{name:"Matplotlib",level:80},{name:"Seaborn",level:75},{name:"Data Visualization",level:80}]}],o={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.1}}},c={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6}}};return M.jsx("section",{id:"skills",className:"py-20 gradient-bg-3",ref:i,children:M.jsx("div",{className:"container mx-auto px-4",children:M.jsxs(tt.div,{variants:o,initial:"hidden",animate:l?"visible":"hidden",className:"max-w-7xl mx-auto",children:[M.jsxs(tt.div,{variants:c,className:"text-center mb-16",children:[M.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Technical Skills"}),M.jsx("div",{className:"w-24 h-1 bg-white/30 mx-auto mb-8"}),M.jsx("p",{className:"text-xl text-white/90 max-w-2xl mx-auto",children:"A comprehensive toolkit of modern technologies and frameworks"})]}),M.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:u.map((d,h)=>M.jsxs(tt.div,{variants:c,whileHover:{scale:1.02},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20",children:[M.jsxs("div",{className:"flex items-center mb-6",children:[M.jsx(tt.div,{whileHover:{rotate:360},transition:{duration:.5},className:"bg-white/20 p-3 rounded-full mr-4",children:d.icon}),M.jsx("h3",{className:"text-xl font-semibold text-white",children:d.title})]}),M.jsx("div",{className:"space-y-4",children:d.skills.map((g,y)=>M.jsxs("div",{className:"space-y-2",children:[M.jsxs("div",{className:"flex justify-between items-center",children:[M.jsx("span",{className:"text-white/90 font-medium",children:g.name}),M.jsxs("span",{className:"text-white/70 text-sm",children:[g.level,"%"]})]}),M.jsx("div",{className:"w-full bg-white/20 rounded-full h-2",children:M.jsx(tt.div,{initial:{width:0},animate:l?{width:`${g.level}%`}:{width:0},transition:{duration:1.5,delay:h*.1+y*.1,ease:"easeOut"},className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full"})})]},y))})]},h))}),M.jsxs(tt.div,{variants:c,className:"mt-16 text-center",children:[M.jsx("h3",{className:"text-2xl font-semibold text-white mb-8",children:"Additional Technologies & Tools"}),M.jsx("div",{className:"flex flex-wrap justify-center gap-4",children:["Netlify","GitHub","Leaflet","Mongoose","Cloud Functions","Cloud Messaging","QR Code Scanning","Geospatial Queries","MVC Architecture","Repository Pattern","Context API"].map((d,h)=>M.jsx(tt.span,{variants:c,whileHover:{scale:1.1},className:"bg-white/20 backdrop-blur-md px-4 py-2 rounded-full text-white/90 border border-white/30",children:d},h))})]})]})})})},qT=()=>{const i=P.useRef(null),l=tu(i,{once:!0}),u=[{title:"MediRemind - Medication Management App",description:"A comprehensive cross-platform mobile application for managing medication schedules with real-time notifications and role-based dashboards.",icon:M.jsx(Uy,{className:"w-8 h-8"}),technologies:["React Native","Expo","Firebase","Cloud Functions","JavaScript"],achievements:["35% improvement in medication adherence","40% reduction in admin time for providers","Scalable architecture supporting thousands of users","QR code scanning for prescription management"],features:["Cross-platform mobile app using React Native and Expo","Firebase backend with authentication and Firestore database","Automated medication reminders via Cloud Functions","Role-based dashboards for doctors, patients, and caregivers","Secure authentication with custom Firebase rules"],gradient:"from-blue-500 to-purple-600"},{title:"Roadside Assistance Web Application",description:"Full-stack web application providing real-time location-based roadside assistance services with comprehensive user management.",icon:M.jsx(dT,{className:"w-8 h-8"}),technologies:["React","TypeScript","Node.js","MongoDB","Leaflet","JWT"],achievements:["Real-time location-based services","Comprehensive white box testing coverage","Clean MVC architecture implementation","Successful team collaboration via GitHub"],features:["Secure JWT authentication with role-based access","MongoDB geospatial indexing with Leaflet integration","Serverless deployment on Netlify","MVC, Repository, and Middleware patterns","Comprehensive API documentation"],gradient:"from-green-500 to-teal-600"},{title:"Restaurant Orders & Rewards Management",description:"Dynamic web application for managing restaurant operations with integrated rewards system and real-time order tracking.",icon:M.jsx(Uc,{className:"w-8 h-8"}),technologies:["Python","Flask","MySQL","JavaScript","HTML","CSS"],achievements:["Dynamic order management system","Real-time rewards calculation","Complex SQL queries and triggers","Responsive user interface design"],features:["Backend API using Flask for data management","MySQL database with persistent storage","Responsive front-end with HTML, CSS, and JavaScript","Complex SQL queries for rewards management","Real-time order history and rewards tracking"],gradient:"from-orange-500 to-red-600"},{title:"JavaFX Ball Game Application",description:"Interactive ball game with rich graphical interface, featuring collision detection and progressive difficulty levels.",icon:M.jsx(cT,{className:"w-8 h-8"}),technologies:["Java","JavaFX"],achievements:["Smooth gameplay experience","Object-oriented design principles","Modular and maintainable code","Engaging visual experience"],features:["Rich graphical user interface using JavaFX","Collision detection and score tracking","Increasing difficulty levels","Object-oriented architecture for easy enhancements","Responsive and engaging gameplay"],gradient:"from-purple-500 to-pink-600"}],o={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.2}}},c={hidden:{y:50,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6}}};return M.jsx("section",{id:"projects",className:"py-20 bg-gray-50",ref:i,children:M.jsx("div",{className:"container mx-auto px-4",children:M.jsxs(tt.div,{variants:o,initial:"hidden",animate:l?"visible":"hidden",className:"max-w-7xl mx-auto",children:[M.jsxs(tt.div,{variants:c,className:"text-center mb-16",children:[M.jsx("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Featured Projects"}),M.jsx("div",{className:"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"}),M.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Showcasing innovative solutions and technical expertise across various domains"})]}),M.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:u.map((d,h)=>M.jsxs(tt.div,{variants:c,whileHover:{scale:1.02,boxShadow:"0 25px 50px rgba(0,0,0,0.15)"},className:"card overflow-hidden",children:[M.jsxs("div",{className:`bg-gradient-to-r ${d.gradient} p-6 text-white`,children:[M.jsxs("div",{className:"flex items-center mb-4",children:[M.jsx(tt.div,{whileHover:{rotate:360},transition:{duration:.5},className:"bg-white/20 p-3 rounded-full mr-4",children:d.icon}),M.jsx("h3",{className:"text-xl font-bold",children:d.title})]}),M.jsx("p",{className:"text-white/90 leading-relaxed",children:d.description})]}),M.jsxs("div",{className:"p-6",children:[M.jsxs("div",{className:"mb-6",children:[M.jsx("h4",{className:"font-semibold text-gray-800 mb-3",children:"Technologies Used:"}),M.jsx("div",{className:"flex flex-wrap gap-2",children:d.technologies.map((g,y)=>M.jsx("span",{className:"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium",children:g},y))})]}),M.jsxs("div",{className:"mb-6",children:[M.jsx("h4",{className:"font-semibold text-gray-800 mb-3",children:"Key Features:"}),M.jsx("ul",{className:"space-y-2",children:d.features.slice(0,3).map((g,y)=>M.jsxs("li",{className:"flex items-start",children:[M.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"}),M.jsx("span",{className:"text-gray-600 text-sm",children:g})]},y))})]}),M.jsxs("div",{className:"mb-6",children:[M.jsx("h4",{className:"font-semibold text-gray-800 mb-3",children:"Key Achievements:"}),M.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:d.achievements.map((g,y)=>M.jsx("div",{className:"bg-green-50 text-green-700 px-3 py-2 rounded-lg text-sm",children:g},y))})]}),M.jsxs("div",{className:"flex gap-4",children:[M.jsxs(tt.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center gap-2 bg-gray-800 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors duration-300",children:[M.jsx(Bc,{size:16}),"View Code"]}),M.jsxs(tt.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300",children:[M.jsx(oT,{size:16}),"Live Demo"]})]})]})]},h))})]})})})},YT=()=>{const i=P.useRef(null),l=tu(i,{once:!0}),[u,o]=P.useState({name:"",email:"",message:""}),c=m=>{o({...u,[m.target.name]:m.target.value})},d=m=>{m.preventDefault(),console.log("Form submitted:",u),o({name:"",email:"",message:""})},h=[{icon:M.jsx(nc,{className:"w-6 h-6"}),title:"Email",value:"<EMAIL>",link:"mailto:<EMAIL>"},{icon:M.jsx(_y,{className:"w-6 h-6"}),title:"Phone",value:"(*************",link:"tel:(*************"},{icon:M.jsx(vT,{className:"w-6 h-6"}),title:"Location",value:"Cape Girardeau, MO",link:null},{icon:M.jsx(Bc,{className:"w-6 h-6"}),title:"GitHub",value:"github.com/DarshanAdh",link:"https://github.com/DarshanAdh"}],g={hidden:{opacity:0},visible:{opacity:1,transition:{delayChildren:.3,staggerChildren:.2}}},y={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:.6}}};return M.jsx("section",{id:"contact",className:"py-20 gradient-bg-2",ref:i,children:M.jsx("div",{className:"container mx-auto px-4",children:M.jsxs(tt.div,{variants:g,initial:"hidden",animate:l?"visible":"hidden",className:"max-w-6xl mx-auto",children:[M.jsxs(tt.div,{variants:y,className:"text-center mb-16",children:[M.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Get In Touch"}),M.jsx("div",{className:"w-24 h-1 bg-white/30 mx-auto mb-8"}),M.jsx("p",{className:"text-xl text-white/90 max-w-2xl mx-auto",children:"Ready to collaborate on your next project? Let's discuss how we can work together!"})]}),M.jsxs("div",{className:"grid lg:grid-cols-2 gap-12",children:[M.jsxs(tt.div,{variants:y,className:"space-y-8",children:[M.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20",children:[M.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Contact Information"}),M.jsx("div",{className:"space-y-6",children:h.map((m,v)=>M.jsxs(tt.div,{whileHover:{scale:1.02},className:"flex items-center space-x-4",children:[M.jsx("div",{className:"bg-white/20 p-3 rounded-full",children:m.icon}),M.jsxs("div",{children:[M.jsx("h4",{className:"text-white font-semibold",children:m.title}),m.link?M.jsx("a",{href:m.link,target:"_blank",rel:"noopener noreferrer",className:"text-white/80 hover:text-white transition-colors duration-300",children:m.value}):M.jsx("p",{className:"text-white/80",children:m.value})]})]},v))})]}),M.jsxs(tt.div,{variants:y,className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20",children:[M.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"Let's Build Something Amazing Together!"}),M.jsx("p",{className:"text-white/80 mb-6",children:"I'm always interested in new opportunities, whether it's a full-time position, freelance project, or collaboration. Feel free to reach out!"}),M.jsxs("div",{className:"flex space-x-4",children:[M.jsx(tt.a,{href:"mailto:<EMAIL>",whileHover:{scale:1.05},className:"bg-white text-gray-800 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300",children:"Send Email"}),M.jsx(tt.a,{href:"https://github.com/DarshanAdh",target:"_blank",rel:"noopener noreferrer",whileHover:{scale:1.05},className:"bg-white/20 text-white px-6 py-3 rounded-full font-semibold hover:bg-white/30 transition-colors duration-300",children:"View GitHub"})]})]})]}),M.jsx(tt.div,{variants:y,children:M.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20",children:[M.jsx("h3",{className:"text-2xl font-bold text-white mb-6",children:"Send a Message"}),M.jsxs("form",{onSubmit:d,className:"space-y-6",children:[M.jsxs("div",{children:[M.jsxs("label",{className:"block text-white/90 font-medium mb-2",children:[M.jsx(zT,{className:"w-4 h-4 inline mr-2"}),"Your Name"]}),M.jsx("input",{type:"text",name:"name",value:u.name,onChange:c,required:!0,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300",placeholder:"Enter your name"})]}),M.jsxs("div",{children:[M.jsxs("label",{className:"block text-white/90 font-medium mb-2",children:[M.jsx(nc,{className:"w-4 h-4 inline mr-2"}),"Email Address"]}),M.jsx("input",{type:"email",name:"email",value:u.email,onChange:c,required:!0,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300",placeholder:"Enter your email"})]}),M.jsxs("div",{children:[M.jsxs("label",{className:"block text-white/90 font-medium mb-2",children:[M.jsx(TT,{className:"w-4 h-4 inline mr-2"}),"Message"]}),M.jsx("textarea",{name:"message",value:u.message,onChange:c,required:!0,rows:5,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:border-white/60 transition-colors duration-300 resize-none",placeholder:"Tell me about your project or opportunity..."})]}),M.jsxs(tt.button,{type:"submit",whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-white text-gray-800 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 flex items-center justify-center space-x-2",children:[M.jsx(ET,{className:"w-4 h-4"}),M.jsx("span",{children:"Send Message"})]})]})]})})]}),M.jsx(tt.div,{variants:y,className:"text-center mt-16",children:M.jsx("div",{className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20",children:M.jsx("p",{className:"text-white/80",children:"© 2024 Darshan Adhikari. Built with React, Framer Motion, and lots of ☕"})})})]})})})},GT=()=>{const[i,l]=P.useState(!1),[u,o]=P.useState(!1);P.useEffect(()=>{const d=()=>{o(window.scrollY>50)};return window.addEventListener("scroll",d),()=>window.removeEventListener("scroll",d)},[]);const c=[{name:"Home",href:"#hero"},{name:"About",href:"#about"},{name:"Skills",href:"#skills"},{name:"Projects",href:"#projects"},{name:"Contact",href:"#contact"}];return M.jsx(tt.nav,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${u?"bg-white/90 backdrop-blur-md shadow-lg":"bg-transparent"}`,children:M.jsxs("div",{className:"container mx-auto px-4",children:[M.jsxs("div",{className:"flex items-center justify-between h-16",children:[M.jsx(tt.div,{whileHover:{scale:1.05},className:"text-2xl font-bold gradient-text",children:"Darshan Adhikari"}),M.jsx("div",{className:"hidden md:flex space-x-8",children:c.map((d,h)=>M.jsx(tt.a,{href:d.href,initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:h*.1},whileHover:{scale:1.1},className:`font-medium transition-colors duration-300 ${u?"text-gray-700 hover:text-blue-600":"text-white hover:text-blue-300"}`,onClick:()=>l(!1),children:d.name},d.name))}),M.jsx("div",{className:"md:hidden",children:M.jsx("button",{onClick:()=>l(!i),className:`p-2 rounded-md ${u?"text-gray-700":"text-white"}`,children:i?M.jsx(UT,{size:24}):M.jsx(xT,{size:24})})})]}),M.jsx(tt.div,{initial:!1,animate:{height:i?"auto":0},className:"md:hidden overflow-hidden bg-white/95 backdrop-blur-md rounded-lg mt-2",children:M.jsx("div",{className:"py-4 space-y-2",children:c.map(d=>M.jsx("a",{href:d.href,className:"block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-300",onClick:()=>l(!1),children:d.name},d.name))})})]})})};function XT(){return M.jsxs("div",{className:"App",children:[M.jsx(GT,{}),M.jsx(BT,{}),M.jsx(HT,{}),M.jsx(LT,{}),M.jsx(qT,{}),M.jsx(YT,{})]})}m1.createRoot(document.getElementById("root")).render(M.jsx(P.StrictMode,{children:M.jsx(XT,{})}));
